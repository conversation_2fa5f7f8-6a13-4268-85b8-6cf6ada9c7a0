from util.logger.logger_utils import setup_logger, log_exception_as_single_line
from util.tools.memory import get_user_context, get_current_session_id
from typing import Optional

import httpx
import json
import os

logger = setup_logger(__name__)

async def query_user_asset_tool() -> str:
    """
    Wrapper function for query_user_asset that can be used as a tool.
    This function tries to get the session_id from various sources.
    """
    logger.info("[DEBUG] Starting query_user_asset_tool function")
    
    # Try to get session_id from multiple sources
    session_id = None
    
    # 1. Try from context variable
    session_id = get_current_session_id()
    if session_id:
        logger.info(f"[DEBUG] Retrieved session_id from context variable: {session_id}")
        return await query_user_asset(session_id)
    
    # 2. Try from callback context
    try:
        import inspect
        frame = inspect.currentframe()
        while frame:
            if 'callback_context' in frame.f_locals:
                callback_context = frame.f_locals['callback_context']
                if hasattr(callback_context, 'state') and 'session_id' in callback_context.state:
                    session_id = callback_context.state['session_id']
                    logger.info(f"[DEBUG] Retrieved session_id from callback context: {session_id}")
                    return await query_user_asset(session_id)
            frame = frame.f_back
    except Exception as e:
        logger.error(f"[DEBUG] Error trying to get session_id from callback context: {e}")
    
    # 3. Try from tool context
    try:
        import inspect
        frame = inspect.currentframe()
        while frame:
            if 'tool_context' in frame.f_locals:
                tool_context = frame.f_locals['tool_context']
                if hasattr(tool_context, 'state') and 'session_id' in tool_context.state:
                    session_id = tool_context.state['session_id']
                    logger.info(f"[DEBUG] Retrieved session_id from tool context: {session_id}")
                    return await query_user_asset(session_id)
            frame = frame.f_back
    except Exception as e:
        logger.error(f"[DEBUG] Error trying to get session_id from tool context: {e}")
    
    # 4. Try from invocation context
    try:
        import inspect
        frame = inspect.currentframe()
        while frame:
            if 'invocation_context' in frame.f_locals:
                invocation_context = frame.f_locals['invocation_context']
                if hasattr(invocation_context, 'state') and 'session_id' in invocation_context.state:
                    session_id = invocation_context.state['session_id']
                    logger.info(f"[DEBUG] Retrieved session_id from invocation context: {session_id}")
                    return await query_user_asset(session_id)
            frame = frame.f_back
    except Exception as e:
        logger.error(f"[DEBUG] Error trying to get session_id from invocation context: {e}")
    
    logger.error("[DEBUG] No session ID found in query_user_asset_tool")
    return "No session ID found - session context not properly initialized"


async def query_user_asset(session_id: Optional[str] = None) -> str:
    logger.info("[DEBUG] Starting query_user_asset function")

    # user_id = "100101551721"  1000004527087

    # get session_id first - try multiple sources
    if not session_id:
        session_id = get_current_session_id()
        logger.info(f"[DEBUG] Retrieved session_id from context variable: {session_id}")
    
    if not session_id:
        logger.error("[DEBUG] No session ID found in query_user_asset")
        logger.error("[DEBUG] This usually means the session context was not properly initialized")
        logger.error("[DEBUG] Check if set_current_session_id() was called before this function")
        
        # Try to get session_id from callback context as fallback
        try:
            import inspect
            frame = inspect.currentframe()
            while frame:
                if 'callback_context' in frame.f_locals:
                    callback_context = frame.f_locals['callback_context']
                    if hasattr(callback_context, 'state') and 'session_id' in callback_context.state:
                        session_id = callback_context.state['session_id']
                        logger.info(f"[DEBUG] Retrieved session_id from callback context: {session_id}")
                        break
                frame = frame.f_back
        except Exception as e:
            logger.error(f"[DEBUG] Error trying to get session_id from callback context: {e}")
        
        # Try to get session_id from tool context as another fallback
        if not session_id:
            try:
                import inspect
                frame = inspect.currentframe()
                while frame:
                    if 'tool_context' in frame.f_locals:
                        tool_context = frame.f_locals['tool_context']
                        if hasattr(tool_context, 'state') and 'session_id' in tool_context.state:
                            session_id = tool_context.state['session_id']
                            logger.info(f"[DEBUG] Retrieved session_id from tool context: {session_id}")
                            break
                    frame = frame.f_back
            except Exception as e:
                logger.error(f"[DEBUG] Error trying to get session_id from tool context: {e}")
        
        if not session_id:
            return "No session ID found - session context not properly initialized"

    logger.info(f"[DEBUG] Using session_id: {session_id}")
    
    # Get user context with better error handling
    try:
        user_id, user_language = get_user_context(session_id)
        if not user_id:
            logger.error(f"[DEBUG] No user_id found for session {session_id}")
            logger.error("[DEBUG] This means the user context was not properly set for this session")
            return f"No user context found for session {session_id}"
        
        logger.info(f"[DEBUG] Retrieved user context - user_id: {user_id}, language: {user_language}")
    except Exception as e:
        logger.error(f"[DEBUG] Error getting user context for session {session_id}: {e}")
        return f"Error retrieving user context: {str(e)}"

    # Determine environment (default to QA if not specified)
    environment = os.getenv("ENVIRONMENT", "qa").lower()
    logger.info(f"[DEBUG] Using environment: {environment}")
    
    # Set API endpoint based on environment Not use https!
    if environment == "prod":
        url = "http://spot-query-facade.eureka.local:9526/v2/spot/distribution/asset"
    else:
        url = "http://spot-query-facade.eureka.qa.local:9526/v2/spot/distribution/asset"
    
    logger.info(f"[DEBUG] Using API endpoint: {url}")

    headers = {
        "Content-Type": "application/json"
    }

        # just for alpha # "includeAccountTypes": [
        #     "ALPHA"
        #     ]
    payload = {
    "body": {
        "userId": int(user_id),
        "quoteAsset": "USDT"
    }
    }

    logger.info(f"[DEBUG] Querying user asset for user_id: {user_id}")
    logger.info(f"[DEBUG] Request payload: {json.dumps(payload, indent=2)}")

    try:
        logger.info("[DEBUG] Initializing HTTP client")
        async with httpx.AsyncClient(timeout=10) as client:
            logger.info("[DEBUG] Sending HTTP request")
            response = await client.post(url, headers=headers, json=payload)
            logger.info(f"[DEBUG] Received response status: {response.status_code}")
            
            response.raise_for_status()
            result = response.json()
            logger.info(f"[DEBUG] Parsed response: {json.dumps(result, indent=2)}")

            if "data" in result and "assetList" in result["data"]:
                result = simplify_asset_data(result)
                logger.info(f"[DEBUG] Simplified user asset data: {json.dumps(result, indent=2)}")
            else:
                logger.error(f"[DEBUG] No asset data found in result: {result}, session_id: {session_id}")
                return "No asset data found"
            
            formatted_result = json.dumps(result, indent=2, ensure_ascii=False)
            logger.info("[DEBUG] Successfully formatted response")
            return formatted_result
            
    except httpx.HTTPStatusError as e:
        error_msg = f"接口错误: {e.response.status_code} - {e.response.text}"
        logger.error(f"[DEBUG] HTTP error: {error_msg}")
        return error_msg
    except Exception as e:
        error_msg = f"请求失败: {str(e)}"
        logger.error(f"[DEBUG] General error: {error_msg}")
        log_exception_as_single_line(logger, e, "[DEBUG] Full exception details")
        return error_msg
    

def simplify_asset_data(asset_data: dict) -> dict:
    """
    Simplify the asset data to keep only essential fields.
    """
    """
    input example:
    {
                                "status": "OK",
                                "type": "GENERAL",
                                "code": "000000000",
                                "errorData": null,
                                "data": {
                                    "assetList": [
                                        {
                                            "asset": "BUSD",
                                            "amount": 9999,
                                            "quoteAmount": 1056987690660000,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "BTC",
                                            "amount": 9976.28513463,
                                            "quoteAmount": 1054586517.2335484,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "ETH",
                                            "amount": 9999,
                                            "quoteAmount": 24258573.9,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "SOL",
                                            "amount": 100008.79553273,
                                            "quoteAmount": 14633286.96234905,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "BNB",
                                            "amount": 19222.14186157,
                                            "quoteAmount": 12332157.33270884,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "OMG",
                                            "amount": 101202.67697801,
                                            "quoteAmount": 3036080.3093403,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "BCH",
                                            "amount": 1079.66,
                                            "quoteAmount": 496967.498,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "LTC",
                                            "amount": 1000,
                                            "quoteAmount": 85090,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "EOS",
                                            "amount": 1000,
                                            "quoteAmount": 31000,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "USDT",
                                            "amount": 20984.83563501,
                                            "quoteAmount": 20984.83563501,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "ICP",
                                            "amount": 1000,
                                            "quoteAmount": 5039,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "DOT",
                                            "amount": 969.49,
                                            "quoteAmount": 3372.85571,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "XRP",
                                            "amount": 1000,
                                            "quoteAmount": 2193.3,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "AVAX",
                                            "amount": 100,
                                            "quoteAmount": 1844,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "EUR",
                                            "amount": 1000,
                                            "quoteAmount": 1143.47397,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "ADA",
                                            "amount": 552.68,
                                            "quoteAmount": 327.683972,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "TRX",
                                            "amount": 1000,
                                            "quoteAmount": 272.6,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "DOGE",
                                            "amount": 1000,
                                            "quoteAmount": 166.72,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "LUNA",
                                            "amount": 1100,
                                            "quoteAmount": 163.13,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "WING",
                                            "amount": 37.99495554,
                                            "quoteAmount": 0,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "ARK",
                                            "amount": 40,
                                            "quoteAmount": 0,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        },
                                        {
                                            "asset": "PEPE",
                                            "amount": 85,
                                            "quoteAmount": 0,
                                            "pnl": null,
                                            "pnlRatio": null,
                                            "isCex": true,
                                            "chainId": null,
                                            "contractAddress": null
                                        }
                                    ],
                                    "quoteAsset": "USDT",
                                    "totalQuoteAmount": 1056988800155180.9,
                                    "totalPnl": null,
                                    "totalPnlRatio": null
                                },
                                "subData": null,
                                "params": null
                            }
                        }
                    }
    
    """
    # Extract the data from the response structure
    if "data" in asset_data and "assetList" in asset_data["data"]:
        # Simplify each asset in the assetList to only keep asset, amount, and quoteAmount
        simplified_assets = []
        for asset in asset_data["data"]["assetList"]:
            simplified_asset = {
                "asset": asset["asset"],
                "amount": asset["amount"],
                "quoteAmount": asset["quoteAmount"]
            }
            simplified_assets.append(simplified_asset)
            if len(simplified_assets) > 6:
                simplified_assets.append({
                    "comment": " The user also have some other assets."
                })
                break
        
        return {
            "assetList": simplified_assets,
            "quoteAsset": asset_data["data"].get("quoteAsset"),
            "totalQuoteAmount": asset_data["data"].get("totalQuoteAmount")
        }
    else:
        logger.error(f"[DEBUG] No asset data found in asset_data: {asset_data}")
        return {'error': 'No asset data found'}