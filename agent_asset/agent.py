from google.adk import Agent

import json

from conf import model_config, prompt
from conf.config import Config
from util.logger.logger_utils import setup_logger
#from agent_asset.query_asset import query_user_asset
# from agent_trade.agent import get_trading_agent_data
from agent_convert.agent import get_trading_agent_data
from util.logger.logger_utils import setup_logger
from util.tools.memory import get_session_context, get_current_session_id
from google.adk.agents.callback_context import CallbackContext
from agent_asset import prompt

logger = setup_logger(__name__)




async def create_asset_agent():
    """
    Create asset agent with resolved instruction using actual data.
    """
    # Get the data directly
    user_asset, hot_coin_convert_pair = await get_trading_agent_data()
    
    # Resolve the instruction with actual data
    resolved_instruction = prompt.ASSET_AGENT_INSTR.replace(
        "{user_asset_data}", str(user_asset)
    )
    
    logger.info(f"Creating asset agent with resolved instruction. resolved_instruction: {resolved_instruction}")
    
    # Create a session-aware callback for the asset agent
    def asset_session_callback(callback_context: CallbackContext) -> None:
        """Session-aware callback for asset agent to ensure session context is available"""
        try:
            # Get session_id from current context
            session_id = get_current_session_id()
            if session_id:
                logger.info(f"[DEBUG] Trading agent session callback - session_id: {session_id}")
                # Set session_id in callback context state
                callback_context.state["session_id"] = session_id
                
                # Get user context
                from util.tools.memory import get_user_context
                user_id, user_language = get_user_context(session_id)
                if user_id:
                    callback_context.state["user_id"] = user_id
                    callback_context.state["user_language"] = user_language or "english"
                    logger.info(f"[DEBUG] Trading agent session callback - set user_id: {user_id}, language: {user_language}")
            else:
                logger.warning("[DEBUG] Trading agent session callback - no session_id found")
        except Exception as e:
            logger.error(f"[DEBUG] Error in trading agent session callback: {e}")
    
    return Agent(
        name="AssetAgent",
        description="查询用户资产余额/资产收益",
        instruction=resolved_instruction,
        # model=model_config.MODEL_GEMINI_2_0_FLASH,
        # generate_content_config=model_config.create_generate_content_config(model_config.ASSET_AGENT_CONFIG),
        model=model_config.MODEL,
        planner=model_config.create_planner(model_config.DEFAULT_AGENT_CONFIG),
        tools=[],
        before_agent_callback=asset_session_callback,
    )
