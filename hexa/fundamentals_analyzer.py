"""
Fundamentals analysis interface leveraging Hexa API.
"""
from __future__ import annotations

import json
import os
from typing import Dict, Any, Optional, List

from .hexa_api_client import call_hexa, HexaAPIError

# Path to products cache (Binance-like symbols metadata)
PRODUCTS_CACHE_PATH = os.getenv(
    "PRODUCTS_CACHE_PATH",
    os.path.join(os.path.dirname(os.path.dirname(__file__)), "products_cache.json"),
)

# In-memory cache of symbol -> metadata to avoid repeated file reads
_SYMBOL_CACHE: Optional[Dict[str, Dict[str, Any]]] = None


def _load_products_cache(path: str) -> Optional[Dict[str, Any]]:
    try:
        with open(path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception:
        return None


def _index_symbols(cache: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
    """Aggregate products into a base-symbol keyed index.

    Heuristics:
    - b: base symbol (token ticker)
    - an: asset/project name
    - q/qs: quotes traded against; qn: quote asset name
    - cs: circulating supply (approx, take max across markets if multiple)
    - tags: category/chain hints (e.g., BSC, Layer1_Layer2)
    """
    products = cache.get("products", []) if isinstance(cache, dict) else []
    idx: Dict[str, Dict[str, Any]] = {}
    for p in products:
        b = p.get("b")
        if not b:
            continue
        e = idx.setdefault(b, {
            "symbol": b,
            "name": None,
            "quotes": set(),
            "quote_names": set(),
            "tags": set(),
            "circulating_supply": None,
        })
        if not e["name"] and p.get("an"):
            e["name"] = p.get("an")
        q = p.get("q")
        if q:
            e["quotes"].add(q)
        qn = p.get("qn")
        if qn:
            e["quote_names"].add(qn)
        tags = p.get("tags") or []
        if isinstance(tags, list):
            e["tags"].update(tags)
        cs = p.get("cs")
        try:
            if cs is not None:
                cs_val = float(cs)
                cur = e["circulating_supply"]
                e["circulating_supply"] = max(cs_val, cur) if cur is not None else cs_val
        except Exception:
            pass
    # Convert sets to sorted lists for stable output
    for b, e in idx.items():
        e["quotes"] = sorted(list(e["quotes"]))
        e["quote_names"] = sorted(list(e["quote_names"]))
        e["tags"] = sorted(list(e["tags"]))
    return idx


def _get_symbol_metadata(symbol: str) -> Optional[Dict[str, Any]]:
    global _SYMBOL_CACHE
    if _SYMBOL_CACHE is None:
        cache = _load_products_cache(PRODUCTS_CACHE_PATH)
        if not cache:
            _SYMBOL_CACHE = {}
        else:
            _SYMBOL_CACHE = _index_symbols(cache)
    return _SYMBOL_CACHE.get(symbol) if _SYMBOL_CACHE else None


def build_fundamentals_prompt(token: str) -> str:
    """
    Build a concise mobile-friendly prompt to summarize fundamentals for a stock/crypto token.

    Also incorporates metadata (symbol, project name, tags, common quote pairs) from products_cache.json
    to improve search precision while keeping scope broad.
    """
    meta = _get_symbol_metadata(token)
    # Compose a light-weight context line
    context_lines: List[str] = []
    if meta:
        name = meta.get("name")
        quotes = ", ".join(meta.get("quotes") or [])
        tags = ", ".join(meta.get("tags") or [])
        cs = meta.get("circulating_supply")
        context_lines.append(f"Symbol: {token}")
        if name:
            context_lines.append(f"Project name: {name}")
        if quotes:
            context_lines.append(f"Common quote pairs: {quotes}")
        if tags:
            context_lines.append(f"Tags: {tags}")
        if cs:
            context_lines.append(f"Circulating supply (approx): {int(cs):,}")
    context = "\n".join(context_lines)

    return (
        f"""
You are an expert web3 financial analyst. Use the following identification hints to avoid symbol confusion and improve precision (do not over-constrain; still gather broadly relevant info):
{context if context else f"Symbol: {token}"}
Avoid confusing similarly spelled tokens (e.g., CAKE vs BAKE). Focus on the exact ticker given.

Summarize the fundamentals of $ {token} in English for a novice user, optimized for mobile display. Follow this structure exactly with short bullets (max 3-4 bullets each):

- TLDR
  - 2-5 sentences: what $ {token} is and the current investment stance (e.g., neutral/positive/risks).
- Key metrics
  - Supply/float or shares outstanding, circulating, market cap, valuation multiples if available.
  - Token/stock distribution highlights; liquidity or volume trends.
- Utility & Governance
  - Core use cases or revenue drivers; governance or economic model.
- Project Updates
  - Recent notable events with dates if possible (official announcements, releases, fundraising, regulatory).

Constraints:
- Prefer official sources first (website, official social accounts). If official information is sufficient, third-party sources are optional.
- All output must be verifiably sourced and must exactly refer to the queried token, not similarly named ones.
- Answer in English, concise, avoid hype; use factual tone.
- Use markdown bullets only; no long paragraphs; avoid emojis.
- If data is uncertain, say "Data is limited; needs verification".
- This is a one-time response. Do not ask follow-up questions or suggest next actions. Provide a complete, standalone answer.

```json
{
  "sources": [
    {
      "title": "Source Title",
      "url": "https://example.com",
      "type": "official|news|documentation|other"
    }
  ]
}
```
"""
    ).strip()


def analyze_fundamentals(token: str) -> Dict:
    """
    High-level API for fundamentals summary.

    Args:
        token: Stock or token identifier/name (e.g., "AAPL", "TSLA", "CAKE").

    Returns:
        A dict with the Hexa API response. On success, includes the model's text.

    Raises:
        HexaAPIError: Re-raises underlying Hexa errors with context.
    """
    prompt = build_fundamentals_prompt(token)
    try:
        return call_hexa(prompt)
    except HexaAPIError as e:
        raise HexaAPIError(f"Fundamentals analysis failed for {token}: {e}") from e

