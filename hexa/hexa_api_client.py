"""
Hexa API client
- Provide a small, reusable function call_hexa(msg: str) -> dict
- Fixed configuration inside this module (URL, token, user, response_mode)
- Robust error handling and JSON parsing
"""
from __future__ import annotations

import json
import os
from typing import Any, Dict

import requests

# Fixed configuration; can be customized via env variables if needed
HEXA_API_URL = os.getenv("HEXA_API_URL", "https://hexa.toolsfdg.net/v1/workflows/run")
HEXA_API_TOKEN = os.getenv("HEXA_API_TOKEN", "app-KKI7AyPtof3wMLQwYwl63yQ3")
HEXA_API_USER = os.getenv("HEXA_API_USER", "sven.z")
HEXA_API_RESPONSE_MODE = os.getenv("HEXA_API_RESPONSE_MODE", "blocking")
DEFAULT_TIMEOUT = int(os.getenv("HEXA_API_TIMEOUT", "60"))


class HexaAPIError(Exception):
    """Custom error for Hexa API call failures."""


def call_hexa(msg: str, return_citations: bool = True, return_search_results: bool = True) -> Dict[str, Any]:
    """
    Call the Hexa API with a message payload and return parsed JSON.

    Args:
        msg: The message content to send to the API.
        return_citations: Whether to request citation data in the response.
        return_search_results: Whether to request search results in the response.

    Returns:
        Parsed JSON response as a Python dict.

    Raises:
        HexaAPIError: When request fails, non-2xx HTTP status, or JSON parsing fails.
    """
    headers = {
        "Authorization": f"Bearer {HEXA_API_TOKEN}",
        "Content-Type": "application/json",
    }

    # Build query parameters based on API documentation
    params = {
        "returnCitations": str(return_citations).lower(),
        "returnSearchResults": str(return_search_results).lower(),
        "message": msg,
        "deepsearch": "true" if return_search_results else "false",
        "eagerTweets": "true",
        "enableSideBySide": "true"
    }

    payload = {
        "inputs": {"msg": msg},
        "response_mode": HEXA_API_RESPONSE_MODE,
        "user": HEXA_API_USER,
    }

    try:
        resp = requests.post(
            HEXA_API_URL, headers=headers, json=payload, params=params, timeout=DEFAULT_TIMEOUT
        )
    except requests.Timeout as e:
        raise HexaAPIError(f"Request timeout: {e}") from e
    except requests.ConnectionError as e:
        raise HexaAPIError(f"Connection error: {e}") from e
    except requests.RequestException as e:
        raise HexaAPIError(f"Request failed: {e}") from e

    # Check HTTP status
    if not (200 <= resp.status_code < 300):
        # Try to include body for debugging
        text = resp.text
        raise HexaAPIError(
            f"HTTP {resp.status_code}. Body: {text[:1000]}"
        )

    # Parse JSON
    try:
        data: Dict[str, Any] = resp.json()
    except ValueError as e:
        # Fall back to text
        raise HexaAPIError("Response is not valid JSON") from e

    return data

