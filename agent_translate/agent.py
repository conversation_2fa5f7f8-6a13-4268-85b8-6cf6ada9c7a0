from google.adk.agents import Agent

import json

from conf import model_config, prompt
from conf.config import Config
from util.logger.logger_utils import setup_logger
from util.tools.memory import get_session_context, get_current_session_id
from google.adk.agents.callback_context import CallbackContext
from agent_translate import prompt
logger = setup_logger(__name__)

def create_translate_agent()-> Agent:
    """
    Create translate agent for handling text translations.
    """
    logger.info("Creating translate agent")
    
    # Create a session-aware callback for the translate agent
    def translate_session_callback(callback_context: CallbackContext) -> None:
        """Session-aware callback for translate agent to ensure session context is available"""
        try:
            # Get session_id from current context
            session_id = get_current_session_id()
            if session_id:
                logger.info(f"[DEBUG] Translate agent session callback - session_id: {session_id}")
                # Set session_id in callback context state
                callback_context.state["session_id"] = session_id
                
                # Get user context
                from util.tools.memory import get_user_context
                user_id, user_language = get_user_context(session_id)
                if user_id:
                    callback_context.state["user_id"] = user_id
                    callback_context.state["user_language"] = user_language or "english"
                    logger.info(f"[DEBUG] Translate agent session callback - set user_id: {user_id}, language: {user_language}")
            else:
                logger.warning("[DEBUG] Translate agent session callback - no session_id found")
        except Exception as e:
            logger.error(f"[DEBUG] Error in translate agent session callback: {e}")
    
    return Agent(
        name="TranslateAgent",
        description="Translate text to user's preferred language",
        instruction=prompt.TRANSLATE_AGENT_INSTR,
        model=model_config.MODEL_GEMINI_2_0_FLASH,
        # planner=model_config.create_planner(model_config.DEFAULT_AGENT_CONFIG),
        generate_content_config=model_config.create_generate_content_config(model_config.FLASH_AGENT_CONFIG),
        tools=[],
        before_agent_callback=translate_session_callback,
    ) 