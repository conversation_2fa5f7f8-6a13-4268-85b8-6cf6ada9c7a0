import asyncio
import json
from typing import AsyncGenerator

from google.adk import Agent
from google.adk.agents import RunConfig
from google.adk.agents.callback_context import CallbackContext
from google.adk.models import LlmRequest, LlmResponse, BaseLlm
from google.genai import types

from agent_YMAL.reply_service import route_question
from endpoints.ai_report.conf import model_config, prompt
from endpoints.ai_report.sequential_agents import create_sequential_agent_runner
from util.logger.logger_utils import setup_logger
from util.shared_config import APP_NAME
from util.tools.memory import set_session_context, get_current_session_id, get_session_conversation_history
from util.tools.unified_session_service import unified_session_service

logger = setup_logger(__name__)


class EchoLlm(BaseLlm):
    model: str = "echo-llm"

    async def generate_content_async(
            self, llm_request: LlmRequest, stream: bool = False
    ) -> AsyncGenerator[LlmResponse, None]:
        # 打印 contents 的详细信息
        dump = []
        for i, c in enumerate(llm_request.contents or []):
            parts_info = []
            for j, p in enumerate(getattr(c, 'parts', []) or []):
                parts_info.append(f"Part[{j}] text={getattr(p, 'text', None)}")
            dump.append(f"Content[{i}] role={getattr(c, 'role', None)} parts=[{'; '.join(parts_info)}]")
        logger.info("[EchoLlm] Dumping LlmRequest.contents ==> " + " | ".join(dump))

        session_id = get_current_session_id()
        if not session_id:
            logger.warning("[EchoLlm] No session_id found in context")
            text = ""
        else:
            # 获取历史记录
            history = get_session_conversation_history(session_id)
            if not history:
                logger.info(f"[EchoLlm] Empty history for session {session_id}")
                text = ""
            else:
                # 取最后一条
                history_entry = history[-1]
                # 安全取 text 字段
                text = history_entry.get("query", "")
                logger.info(
                    f"[EchoLlm] Using last history entry for session {session_id}: {text!r}"
                )

        final_str = f"echo: {text}" if text else "echo: (no input)"
        # if not stream:
        #     yield LlmResponse(content=types.Content(parts=[types.Part(text=final_str)]))
        #     return

        chunk_size = 1  # 字符模式下每次输出1个字符
        delay_sec = 0.1  # 每次输出之间的间隔

        if " " in final_str:
            # 按空格切分
            pieces = final_str.split(" ")
            for idx, piece in enumerate(pieces):
                logger.info(f"[EchoLlm] streaming word: {piece!r}")
                yield LlmResponse(
                    content=types.Content(parts=[types.Part(text=piece + (" " if idx < len(pieces) - 1 else ""))])
                )
                if delay_sec > 0:
                    await asyncio.sleep(delay_sec)
        else:
            # 按字符切分
            for i in range(0, len(final_str), chunk_size):
                piece = final_str[i:i + chunk_size]
                logger.info(f"[EchoLlm] streaming char: {piece!r}")
                yield LlmResponse(content=types.Content(parts=[types.Part(text=piece)]))
                if delay_sec > 0:
                    await asyncio.sleep(delay_sec)


async def create_echo_agent():
    echo_model = EchoLlm()
    return Agent(
        name="EchoAgent",
        description="回显用户输入，不做任何推理。",
        instruction="You are an echo agent. Always repeat the user's last message verbatim.",
        model=echo_model,
        planner=None,
        tools=[],
    )


class YmalWrapperLLM(BaseLlm):
    """Wraps route_question() and streams its chunks as LlmResponses."""
    model: str = "ymal-wrapper-llm"

    async def generate_content_async(
            self, llm_request: LlmRequest, stream: bool = False
    ) -> AsyncGenerator[LlmResponse, None]:

        session_id = get_current_session_id()
        history = get_session_conversation_history(session_id)
        if history:
            history_entry = history[-1]
            text = history_entry.get("query", "")
            logger.info(
                f"[YmalWrapperLLM] Using last history entry for session {session_id}: {text!r}"
            )
            data = json.loads(text)
            query = data.get("query", "")
            base_query = data.get("baseQuery", None)
            target_lang = data.get("targetLang", "")
            reply_id = None

            async for chunk in route_question(query, target_lang, base_query, reply_id):
                if not chunk:
                    continue
                if isinstance(chunk, dict):
                    llm_reply_msg = json.dumps(chunk, ensure_ascii=False)
                elif isinstance(chunk, str):
                    llm_reply_msg = chunk
                else:
                    continue
                llm_reply_msg_log = llm_reply_msg.replace("\r", "\\r").replace("\n", "\\n").replace("\t", "\\t")
                logger.info(f"[YamlWrapperLlm] streaming chunk message: {llm_reply_msg_log}")

                yield LlmResponse(content=types.Content(parts=[types.Part(text=llm_reply_msg)]))


async def create_ymal_wrapper_agent():
    wrapper_model = YmalWrapperLLM()
    return Agent(
        name="YmalWrapperAgent",
        description="Ymal route_question 函数的包装器。",
        instruction="You are a wrapper agent.",
        model=wrapper_model,
        planner=None,
        tools=[],
    )


async def start_agent_session(session_id: str):
    """Starts a sequential agent session with proper session management"""
    logger.info(f"[DEBUG] start_agent_session called for session: {session_id}")

    # Try to acquire a session slot using unified session service
    logger.info(f"[DEBUG] Attempting to acquire session slot for: {session_id}")
    if not await unified_session_service.acquire_session(session_id):
        raise Exception("RESOURCE_EXHAUSTED: Maximum concurrent sessions exceeded. Please try again later.")
    logger.info(f"[DEBUG] Session slot acquired for: {session_id}")

    try:
        # Set session context for agents
        logger.info(f"[DEBUG] Setting session context for: {session_id}")
        # Get user context to pass to set_session_context
        from util.tools.memory import get_user_context
        user_id, user_language = get_user_context(session_id)
        set_session_context(session_id, user_id=user_id, language=user_language)
        logger.info(f"[DEBUG] Session context set for: {session_id}")

        # Create a Session using unified session service
        logger.info(f"[DEBUG] Creating session service session for: {session_id}")
        await unified_session_service.create_session(app_name=APP_NAME, user_id=user_id, session_id=session_id)
        logger.info(f"[DEBUG] Session service session created for: {session_id}")

        # Set the current session ID for agent tools
        from util.tools.memory import set_current_session_id
        set_current_session_id(session_id)
        logger.info(f"[DEBUG] Current session ID set for agent tools: {session_id}")

        # Create an echo agent for demo
        # logger.info(f"[DEBUG] Creating echo agent for: {session_id}")
        # echo_agent = await create_echo_agent()
        # logger.info(f"[DEBUG] Echo agent created for: {session_id}")

        # Create ymal wrapper agent
        logger.info(f"[DEBUG] Creating ymal wrapper agent for: {session_id}")
        ymal_wrapper_agent = await create_ymal_wrapper_agent()
        logger.info(f"[DEBUG] Ymal wrapper agent created for: {session_id}")

        def session_aware_callback(callback_context: CallbackContext) -> None:
            """Session-aware callback that loads user profile for this specific session"""
            logger.info(f"[DEBUG CALLBACK] Session-aware callback called for session: {session_id}")
            try:
                # Set the current session ID in thread-local storage for agent tools
                from util.tools.memory import set_current_session_id, get_user_context
                set_current_session_id(session_id)

                # Get the user context for this specific session
                user_id, user_language = get_user_context(session_id)
                logger.info(
                    f"[DEBUG CALLBACK] Retrieved user context - ID: {user_id}, Language: {user_language}, Session: {session_id}")

                # Set both user_id and language in the callback context state
                if user_id:
                    callback_context.state["user_id"] = user_id
                    logger.info(f"[CALLBACK] User ID set in callback_context.state: {user_id}")
                else:
                    logger.warning(f"[CALLBACK] No user_id found for session {session_id}")
                    logger.warning(f"[CALLBACK] WARNING: No user_id found for session {session_id}")

                callback_context.state["user_language"] = user_language or "english"
                callback_context.state["session_id"] = session_id  # Store session_id for agent tools

                # Log all state values for debugging
                logger.info(f"[CALLBACK] callback_context.state 已设置:")
                logger.info(f"           user_id: {callback_context.state.get('user_id')}")
                logger.info(f"           user_language: {callback_context.state.get('user_language')}")
                logger.info(f"           session_id: {callback_context.state.get('session_id')}")

                logger.info(
                    f"[DEBUG CALLBACK] Set callback context state - user_id: {callback_context.state.get('user_id')}, user_language: {callback_context.state.get('user_language')}, session_id: {callback_context.state.get('session_id')}")
                logger.info(
                    f"[User Profile] Loaded user profile - ID: {user_id}, Language: {user_language or 'english'}, Session: {session_id}")
                logger.info(f"[User Profile] User context set - ID: {user_id}, Language: {user_language or 'english'}")

            except Exception as e:
                logger.error(f"[User Profile Error] Failed to load user profile for session {session_id}: {str(e)}")
                logger.error(f"[DEBUG CALLBACK] Exception in callback: {e}")
                import traceback
                logger.error(f"[DEBUG CALLBACK] Callback traceback: {traceback.format_exc()}")
                # Set default values on error
                callback_context.state["user_id"] = None
                callback_context.state["user_language"] = "english"
                callback_context.state["session_id"] = session_id  # Still store session_id even on error
                # Still set the session ID in thread-local storage even on error
                from util.tools.memory import set_current_session_id
                set_current_session_id(session_id)
                logger.info(f"[DEBUG CALLBACK] Set default values - user_language: english")

            return None

        logger.info(f"[DEBUG] Session-aware callback created for: {session_id}")

        # Resolve the root agent instruction with user language
        def resolve_root_agent_instruction(session_id: str) -> str:
            """Resolve the user_language placeholder in the root agent instruction template"""
            try:
                from util.tools.memory import get_user_context
                user_id, user_language = get_user_context(session_id)

                return prompt.ROOT_AGENT_INSTR.replace("{user_language}", user_language)
            except Exception as e:
                logger.warning(f"[DEBUG] Failed to resolve user language for root agent: {e}, using default 'English'")
                return prompt.ROOT_AGENT_INSTR.replace("{user_language}", "English")

        # Resolve the instruction with user language
        resolved_root_instruction = resolve_root_agent_instruction(session_id)
        logger.info(f"[DEBUG] Root agent instruction resolved for: {session_id}")
        logger.info(f"[DEBUG] Resolved instruction preview_part1: {resolved_root_instruction[:500]}...")
        logger.info(f"[DEBUG] Resolved instruction preview_part2: ... {resolved_root_instruction[-100:0]}")

        coordinator = Agent(
            name="rootAgent",
            model=model_config.MODEL,
            planner=model_config.create_planner(model_config.DEFAULT_AGENT_CONFIG),
            instruction=resolved_root_instruction,  # Use resolved instruction instead of raw template
            description="判断用户意图并分发任务",
            sub_agents=[ymal_wrapper_agent],
            before_agent_callback=session_aware_callback,
        )
        logger.info(f"[DEBUG] Coordinator Agent created for: {session_id}")

        # Create Sequential Agent Runner instead of single Runner
        logger.info(f"[DEBUG] Creating Sequential Agent Runner for: {session_id}")
        sequential_runner = create_sequential_agent_runner(
            root_agent=coordinator,
            session_service=unified_session_service,
            session_id=session_id,
            user_id=session_id
        )
        logger.info(f"[DEBUG] Sequential Agent Runner created for: {session_id}")

        # Set response modality = TEXT
        logger.info(f"[DEBUG] Creating RunConfig for: {session_id}")
        run_config = RunConfig(response_modalities=["TEXT"])
        logger.info(f"[DEBUG] RunConfig created for: {session_id}")

        # Return the sequential runner and run_config instead of live_events and live_request_queue
        logger.info(f"Starting new sequential agent session: {session_id}")
        return sequential_runner, run_config
    except Exception as e:
        logger.error(f"[DEBUG] Exception in start_agent_session for {session_id}: {str(e)}")
        # If session creation fails, release the session slot using unified session service
        await unified_session_service.release_session(session_id)
        raise
