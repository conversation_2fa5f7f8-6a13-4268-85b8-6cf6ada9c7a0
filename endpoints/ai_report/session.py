import hashlib
import json
import uuid

from google.genai.types import Content, Part
from starlette.websockets import WebSocketState, WebSocketDisconnect

from endpoints.ai_report.query_helper import append_query
from endpoints.ai_report.ttl_cache import cache
from util.logger.logger_utils import setup_logger, log_exception_as_single_line
from util.tools import memory
from util.tools.memory import get_user_context

logger = setup_logger(__name__)

CACHE_PREFIX = "ymal"
CACHE_VERSION = "v1"


def _to_sha256_hex(s: str) -> str:
    sha256 = hashlib.sha256(s.encode("utf-8"))
    return sha256.hexdigest()


def _generate_cache_key(prefix: str, version: str, source: str, query: str, audience: str, exp: str) -> str:
    return f"{prefix}:{version}:{source}:{_to_sha256_hex(query)}:{audience}:{exp}"


async def client_to_agent_messaging(websocket, sequential_runner, session_id, session_service=None):
    """Client to agent communication using run_async approach"""
    logger.info(f"[DEBUG] client_to_agent_messaging started for session: {session_id}")
    logger.info(f"[DEBUG] WebSocket state: {websocket.client_state}")

    # Session context is now initialized in main.py before tasks are created
    logger.info(f"[DEBUG] Session context should already be initialized for session: {session_id}")

    # Get user context from session
    user_id, user_language = get_user_context(session_id)

    try:
        while True:
            # Check WebSocket state before attempting to receive
            if websocket.client_state in [WebSocketState.DISCONNECTED, WebSocketState.CONNECTING]:
                logger.info \
                    (f"[DEBUG] WebSocket in invalid state ({websocket.client_state}), stopping client_to_agent_messaging for session: {session_id}")
                break

            logger.info(f"[DEBUG] Waiting for message from client for session: {session_id}")
            try:
                text = await websocket.receive_text()
                logger.info(f"[DEBUG] Received raw message for session {session_id}: {text[:100]}...")
            except WebSocketDisconnect:
                logger.info(f"[DEBUG] WebSocket disconnected while receiving message for session: {session_id}")
                break
            except RuntimeError as e:
                if "WebSocket is not connected" in str(e):
                    logger.info \
                        (f"[DEBUG] WebSocket not connected, stopping client_to_agent_messaging for session: {session_id}")
                    break
                else:
                    raise

            data = json.loads(text)
            message_type = data.get("type")

            gray_flag = websocket.headers.get("x-gray-env")
            reply_id = str(uuid.uuid4())
            reply_id = data.get("id", reply_id)

            logger.info(f"[DEBUG] Parsed message - Type: {message_type}, Text: {text[:50]}...")
            logger.info(f"[DEBUG] Message data structure: {json.dumps(data, indent=2)}")

            if message_type == 20:
                logger.info(f"[DEBUG] Processing live message type 20 for session: {session_id}, reply id: {reply_id}")

                message_body = data.get("message", {})
                base_token = message_body.get("base_token", None)
                query_text = message_body.get("query_text", "")
                query_base_text = message_body.get("query_base_text", None)
                query_source = message_body.get("source", "")
                query_audience = message_body.get("audience", "all")
                query_exp = message_body.get("exp", "base")
                query_ttl = message_body.get("ttl", 0)
                message_content = message_body.get("content", {})
                text_context = message_content.get("text_context", "")
                logger.info(
                    f"[DEBUG] Message content has textContext: {text_context}, session: {session_id}, reply id: {reply_id}")

                # Append query and get history queries
                history_queries = await append_query(session_id, base_token, query_text, gray_flag)
                logger.info(
                    f"[DEBUG] Get history queries: {history_queries}, session: {session_id}, reply id: {reply_id}")

                message_json = {
                    "query": query_text,
                    "baseQuery": query_base_text,
                    "targetLang": user_language,
                    "replyId": reply_id
                }
                message_text = json.dumps(message_json, ensure_ascii=False)
                message_ttl = query_ttl

                # Validate that we're not processing system instructions as user messages
                if message_text and (
                        "CRITICAL RESPONSE HANDLING" in message_text or "绝对禁止的行为" in message_text or "RESPONSE PASSTHROUGH PROTOCOL" in message_text):
                    logger.warning(
                        f"[DEBUG] Detected system instruction in user message for session {session_id}, skipping: {message_text[:100]}...")
                    continue

                # Cache hit, return cached text
                cache_key = _generate_cache_key(CACHE_PREFIX, CACHE_VERSION, query_source, query_text, query_audience,
                                                query_exp)
                logger.info(f"[CACHE] Generate key: {cache_key} for session: {session_id}, reply id: {reply_id}")
                cache_value = cache.get(cache_key)
                if cache_value is not None:
                    try:
                        if isinstance(cache_value, list):
                            logger.info(
                                f"[CACHE] Hit key: {cache_key}, value size: {len(cache_value)} for session: {session_id}, reply id: {reply_id}")
                            for resp in cache_value:
                                # refresh id
                                resp = {**resp, "id": reply_id}
                                # turn_complete
                                if resp.get("type") == 3:
                                    resp = {**resp, "hit_cache": True}
                                await memory.queue_websocket_message(session_id, resp)
                            continue
                        else:
                            logger.warning(
                                f"[CACHE] Invalid cache_value type: {type(cache_value)} for key: {cache_key} in session: {session_id}, reply id: {reply_id}")
                    except Exception as e:
                        logger.error(
                            f"[CACHE] Error reading cache for key: {cache_key} in session: {session_id}, reply id: {reply_id}, err: {e}")

                # Create content for the message
                content = Content(role="user", parts=[Part.from_text(text=message_text)])
                logger.info(f"[CLIENT TO AGENT]: Processing message with run_async: {message_text}")

                # Add to our custom conversation history tracking
                memory.add_query_to_session_history(session_id, message_text)

                # Process the message using sequential runner
                try:
                    from google.adk.agents.run_config import RunConfig
                    run_config = RunConfig(response_modalities=["TEXT"])

                    # Initialize a list to hold response data
                    response_data_list = []

                    # Call run_sequential with the new message
                    live_events = sequential_runner.run_sequential(
                        new_message=content,
                        run_config=run_config
                    )

                    # Process the events and queue responses
                    async for event in live_events:
                        logger.info(
                            f"[DEBUG] Processing event from sequential runner for session: {session_id}, reply id: {reply_id}")

                        # Handle regular content events (from rootAgent or other agents)
                        if event.content and event.content.parts:
                            # Check if this event has function calls (indicating intermediate thinking)
                            has_function_call = False
                            for part in event.content.parts:
                                # function_call=None function_response=None for text parts
                                if part.function_call or part.function_response:
                                    logger.info(f"[DEBUG] Found function call/response in part: {part}")
                                    has_function_call = True
                                    break
                            logger.info(f"[DEBUG] has_function_call: {has_function_call}")
                            text_parts = [part for part in event.content.parts if
                                          part.text is not None and part.text.strip()]
                            logger.info(f"[DEBUG] text_parts: {text_parts}")
                            logger.info(
                                f"[DEBUG] Event has {len(event.content.parts)} parts, has_function_call: {has_function_call}, text_parts: {len(text_parts)}")

                            # Only process text if there's no function call (meaning this is the final response)
                            if not has_function_call and text_parts:
                                for part in text_parts:
                                    # Skip parts that are marked as thinking content
                                    if hasattr(part, 'thought') and part.thought:
                                        logger.info(
                                            f"[DEBUG] Skipping thinking content for session {session_id}: {part.text if part.text else 'No text'}...")
                                        continue

                                    # Queue the response
                                    response_data = {
                                        "type": 2,
                                        "message": {
                                            "sub_type": 200,
                                            "msg": part.text
                                        },
                                        "id": reply_id
                                    }
                                    await memory.queue_websocket_message(session_id, response_data)
                                    response_data_list.append(response_data)
                                    logger.info(f"[AGENT TO CLIENT]: Queued response chunk: {part.text[:100]}...")

                            else:
                                logger.info(
                                    f"[DEBUG] Skipping text processing because event has function_call or no valid text parts - this is intermediate thinking, not final response")

                        # Handle turn complete
                        if event.turn_complete:
                            # Queue type 3 message only after all content has been queued
                            turn_complete_data = {
                                "type": 3,
                                "turn_complete": True,
                                "id": reply_id
                            }
                            await memory.queue_websocket_message(session_id, turn_complete_data)
                            response_data_list.append(turn_complete_data)
                            logger.info(f"[AGENT TO CLIENT]type3: Turn complete message queued")

                            is_fallback_event = False
                            if type(event).__name__ == "FallbackEvent":
                                is_fallback_event = True

                            if response_data_list and not is_fallback_event:
                                # Cache the response if it's a turn complete message
                                cache.set_with_ttl(cache_key, response_data_list, message_ttl)
                                logger.info(
                                    f"[CACHE] Set key: {cache_key}, value size: {len(response_data_list)}, ttl: {message_ttl} for session: {session_id}, reply id: {reply_id}")
                            break

                    logger.info(f"[DEBUG] Finished processing message for session: {session_id}, reply id: {reply_id}")

                except Exception as e:
                    logger.error(
                        f"[DEBUG] Error processing message with sequential runner for session {session_id}, reply id: {reply_id}: {e}")
                    log_exception_as_single_line(logger, e, "[DEBUG] Full error exception details")


            else:
                logger.warning(
                    f"[DEBUG] Unknown message type {message_type} for session: {session_id}, reply id: {reply_id}")

    except WebSocketDisconnect as e:
        logger.info(f"[DEBUG] WebSocket disconnected for session {session_id}: {e}")
    except Exception as e:
        logger.error(f"[DEBUG] Error in client_to_agent_messaging for session {session_id}: {e}")
        log_exception_as_single_line(logger, e, "[DEBUG] Full exception details for client_to_agent_messaging")
