import time

import pytest

from endpoints.ai_report.ttl_cache import TTL<PERSON><PERSON>


def test_set_and_get_with_default_ttl():
    cache = TTLCache(maxsize=10, default_ttl=1)
    cache["a"] = 123
    assert cache["a"] == 123
    assert "a" in cache


def test_set_with_ttl_override():
    cache = TTLCache(maxsize=10)
    cache.set_with_ttl("x", 42, ttl=0.5)
    assert cache["x"] == 42
    time.sleep(0.6)
    with pytest.raises(KeyError):
        _ = cache["x"]


def test_get_with_default_value():
    cache = TTLCache(maxsize=10, default_ttl=1)
    assert cache.get("missing", default="fallback") == "fallback"


def test_contains_and_pop():
    cache = TTLCache(maxsize=10, default_ttl=1)
    cache["k"] = "v"
    assert "k" in cache
    assert cache.pop("k") == "v"
    assert "k" not in cache
    assert cache.pop("missing", default="fallback") == "fallback"


def test_len_and_clear():
    cache = TTLCache(maxsize=10, default_ttl=1)
    cache["a"] = 1
    cache["b"] = 2
    assert len(cache) == 2
    cache.clear()
    assert len(cache) == 0


def test_items_and_values_skip_expired():
    cache = TTLCache(maxsize=10, default_ttl=0.5)
    cache["a"] = 100
    cache.set_with_ttl("b", 200, ttl=0.1)
    time.sleep(0.2)
    keys = [k for k, _ in cache.items()]
    values = list(cache.values())
    assert "a" in keys and 100 in values
    assert "b" not in keys and 200 not in values
