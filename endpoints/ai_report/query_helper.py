import os
from typing import Optional, Dict, Any, List

import httpx


def get_append_query_url(gray_flag: Optional[str]) -> str:
    env = os.getenv("ENV", "qa")
    env_domain = "qa.local" if env == "qa" else "local"

    base = "http://bdp-search-recall"
    port = 9982
    suffix = "/ai-report/online/appendQuery"

    gray = (gray_flag or "").strip()
    if gray:
        return f"{base}.{gray}.eureka.{env_domain}:{port}{suffix}"
    return f"{base}.eureka.{env_domain}:{port}{suffix}"


async def _post_json(url: str, data: dict, headers: dict | None = None) -> dict:
    async with httpx.AsyncClient() as client:
        response = await client.post(url, json=data, headers=headers)
        response.raise_for_status()  # 如果响应状态码是 4xx 或 5xx 会抛出异常
        try:
            return response.json()
        except Exception:
            return {"text": response.text}


def _extract_history_list(api_resp: Dict[str, Any]) -> List[str]:
    status = api_resp.get("status")
    data = api_resp.get("data")

    if status != "OK" or data is None:
        # Typical error shape: {'code': '000002', 'errorData': {...}, 'status': 'ERROR', ...}
        code = api_resp.get("code")
        err = api_resp.get("errorData")
        raise ValueError(f"API status not OK (code={code}): {err}")

    history = data.get("historyQueryTexts")
    if history is None:
        raise ValueError("Missing field: data.historyQueryTexts")
    if not isinstance(history, list):
        raise ValueError(f"Invalid type for historyQueryTexts: {type(history).__name__}")
    return [str(x) for x in history]


# ---------- Public API ----------

async def append_query(session_id: str, token: str, query: str, gray_flag: Optional[str]) -> List[str]:
    url = get_append_query_url(gray_flag)
    payload = {"body": {"token": token, "sessionId": session_id, "lastQueryText": query}}
    resp = await _post_json(url, payload)
    return _extract_history_list(resp)


async def get_history_query(session_id: str, token: str, gray_flag: Optional[str]) -> List[str]:
    url = get_append_query_url(gray_flag)
    payload = {"body": {"token": token, "sessionId": session_id}}
    resp = await _post_json(url, payload)
    return _extract_history_list(resp)
