# MODEL = "gemini-2.0-flash-exp" # "gemini-2.5-pro-preview-03-25"  # "gemini-2.5-flash-exp"
# MODEL_LIVE = "gemini-2.0-flash-live-preview-04-09"
#MODEL = "gemini-2.0-flash-exp"
MODEL_GEMINI_2_0_FLASH = "gemini-2.0-flash"
#MODEL = "claude-3-5-sonnet-20241022"
#MODEL = "claude-3-7-sonnet-20250219" # "claude-sonnet-4-20250514"


# Temperature configuration for model generation
# Range: 0.0 to 2.0
# - 0.0: Most deterministic/focused responses (best for factual, consistent answers)
# - 0.3: Low creativity, high consistency (good for analytical tasks)
# - 0.7: Balanced creativity and consistency (default, good for general use)
# - 1.0: Higher creativity, more varied responses (good for creative tasks)
# - 1.5+: Very creative, highly varied responses (use sparingly)
TEMPERATURE = 0.5
from google.adk.models.lite_llm import LiteLlm
# DeepSeek-V3-0324
# Qwen3-235B-A22B
DEEPSEEK_MODEL=LiteLlm(
                model="hosted_vllm/DeepSeek-V3-0324",
                api_base="http://qa-ds.qa1fdg.net/v1",
                api_key="test_API_KEY"
            )
QWEN_MODEL=LiteLlm(
                model="hosted_vllm/Qwen3-235B-A22B",
                api_base="http://qa-ds.qa1fdg.net/v1",
                api_key="test_API_KEY"
            )


MODEL =  "gemini-2.5-pro" #"gemini-2.5-flash"
MODEL_FLASH = "gemini-2.5-flash"  # Use flash model for faster responses


# Import types for generate_content_config and planners
from google.genai import types
from google.adk.planners import BuiltInPlanner

# Default configuration dictionary for Google ADK Agent
DEFAULT_CONFIG = {
    "model": MODEL_FLASH,
    "temperature": TEMPERATURE,
    "max_output_tokens": 8192,
    "top_p": 0.9,
    "top_k": 40,
    "include_thoughts": False,  # Enable Gemini's thinking process
    "thinking_budget": 128,  # Set thinking budget to 1000
}

# for google search agent 
SEARCH_AGENT_CONFIG = {
    "model": MODEL_FLASH,  # Use consistent flash model
    "temperature": 0.3,  # Low temperature for focused responses
    "max_output_tokens": 2048,  # Limit tokens for speed
    "top_p": 0.9,  # Slightly more focused
    "top_k": 10,   # Reduced for faster processing
    "include_thoughts": False,  # Skip thinking for speed
    "thinking_budget": 0, # 100ms
}

NEWS_AGENT_CONFIG = {
    "model": MODEL_FLASH,  # Use consistent flash model
    "temperature": 0.5,  # Low temperature for focused responses
    "max_output_tokens": 8192,  # Limit tokens for speed
    "top_p": 0.9,  # Slightly more focused
    "top_k": 30,   # Reduced for faster processing
    "include_thoughts": False,  # Skip thinking for speed
    "thinking_budget": 0, # 100ms
}

MARKET_AGENT_CONFIG = {
    "model": MODEL_FLASH,
    "temperature": TEMPERATURE,
    "max_output_tokens": 8192,
    "top_p": 0.9,
    "top_k": 40,
    "include_thoughts": False,  # output Gemini's thinking process
    "thinking_budget": 128,  # Set thinking budget to 1000
}

# for news agent, use lower temperature and max output tokens
FLASH_AGENT_CONFIG = {
    "model": MODEL_FLASH,
    "temperature": 0.3,
    "max_output_tokens": 2048,
    "top_p": 0.9,
    "top_k": 10,
    "include_thoughts": False,  # Enable Gemini's thinking process
    "thinking_budget": 0,  # Set thinking budget to 1000
}

DEFAULT_THINKING_CONFIG = {
    "model": MODEL_FLASH,
    "temperature": TEMPERATURE,
    "max_output_tokens": 8192,
    "top_p": 0.9,
    "top_k": 40,
    "include_thoughts": True,
    "thinking_budget": 128,
}

# Configuration for different agent types
DEFAULT_AGENT_CONFIG = {
    **DEFAULT_CONFIG,
    "temperature": TEMPERATURE,  # Zero temperature for most consistent responses
}

# configuration for the a2a root agent configuration
RAG_ROOT_AGENT_CONFIG = {
    "model": MODEL_FLASH,
    "temperature": 0.3,
    "max_output_tokens": 4096,
    "top_p": 0.9,
    "top_k": 80,
    "include_thoughts": False,  # Enable Gemini's thinking process
    "thinking_budget": 128,  # Set thinking budget to 1000
    # "temperature": TEMPERATURE,  # Low temperature for analytical tasks
}


ANALYTICAL_AGENT_CONFIG = {
    **DEFAULT_CONFIG,
    "temperature": TEMPERATURE,  # Low temperature for analytical tasks
}

CREATIVE_AGENT_CONFIG = {
    **DEFAULT_CONFIG,
    "temperature": TEMPERATURE,  # High temperature for creative tasks
}


# Function to create generate_content_config from config dictionary
def create_generate_content_config(config_dict: dict) -> types.GenerateContentConfig:
    """
    Create a GenerateContentConfig from a configuration dictionary.
    
    Args:
        config_dict: Configuration dictionary with temperature, max_output_tokens, etc.
    
    Returns:
        types.GenerateContentConfig object
    """
    return types.GenerateContentConfig(
        temperature=config_dict.get("temperature", TEMPERATURE),
        max_output_tokens=config_dict.get("max_output_tokens", 8192),
        top_p=config_dict.get("top_p", 0.9),
        top_k=config_dict.get("top_k", 40),
    )

# Function to create thinking config for planner
def create_thinking_config(config_dict: dict) -> types.ThinkingConfig:
    """
    Create a ThinkingConfig from a configuration dictionary.
    
    Args:
        config_dict: Configuration dictionary with thinking parameters
    
    Returns:
        types.ThinkingConfig object or None
    """
    if config_dict.get("include_thoughts") is not None or config_dict.get("thinking_budget") is not None:
        return types.ThinkingConfig(
            include_thoughts=config_dict.get("include_thoughts", False),
            thinking_budget=config_dict.get("thinking_budget", 128),
        )
    return None

# Function to create planner with thinking config
def create_planner(config_dict: dict):
    """
    Create a planner with thinking configuration.
    
    Args:
        config_dict: Configuration dictionary with thinking parameters
    
    Returns:
        BuiltInPlanner object or None
    """
    thinking_config = create_thinking_config(config_dict)
    if thinking_config:
        return BuiltInPlanner(thinking_config=thinking_config)
    return None

