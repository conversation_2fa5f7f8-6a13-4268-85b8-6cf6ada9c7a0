import contextlib
from typing import AsyncGenerator, Any

from google.adk.agents.run_config import RunConfig
from google.adk.models import LlmResponse
from google.genai.types import Content

from util.logger.logger_utils import setup_logger, log_exception_as_single_line
from util.sequential_agents import SequentialAgent<PERSON>unner as BaseSequentialAgentRunner
from util.shared_config import APP_NAME

logger = setup_logger(__name__)


class SequentialAgentRunner(BaseSequentialAgentRunner):

    async def run_sequential(self, new_message: Content, run_config: RunConfig) -> AsyncGenerator[Any, None]:
        """
        Run both agents sequentially:
        1. First run rootAgent to get main response
        2. Then run followUpAgent to generate follow-up suggestions
        """
        logger.info(f"[DEBUG] Starting sequential agent execution for session {self.session_id}")
        logger.info(f"[DEBUG] new_message type: {type(new_message)}")
        logger.info(f"[DEBUG] run_config type: {type(run_config)}")

        # Use proper async context management
        @contextlib.asynccontextmanager
        async def execution_context():
            try:
                # Wait for session to be ready before proceeding
                await self._wait_for_session()
                logger.info(f"[DEBUG] Session {self.session_id} is ready for execution")
                yield
            except Exception as e:
                logger.error(f"[DEBUG] Failed to prepare session {self.session_id}: {e}")
                raise

        async with execution_context():
            # Ensure session exists before proceeding
            try:
                session = await self.session_service.get_session(
                    app_name=APP_NAME,
                    user_id=self.user_id,
                    session_id=self.session_id
                )
                if not session:
                    logger.warning(
                        f"[DEBUG] Session {self.session_id} not found during verification, attempting to create it")
                    # Try to create the session as a fallback
                    session = await self.session_service.create_session(
                        app_name=APP_NAME,
                        user_id=self.user_id,
                        session_id=self.session_id
                    )
                    if not session:
                        raise ValueError(f"Session {self.session_id} not found and could not be created")
                    logger.info(f"[DEBUG] Successfully created session {self.session_id} during verification")
                logger.info(f"[DEBUG] Session {self.session_id} verified before execution")
            except Exception as e:
                logger.error(f"[DEBUG] Error verifying session {self.session_id}: {e}")
                raise

            # Phase 1: Run rootAgent
            logger.info(f"[DEBUG] Phase 1: Running rootAgent for session {self.session_id}")

            try:
                root_events = self.root_runner.run_async(
                    session_id=self.session_id,
                    user_id=self.user_id,
                    new_message=new_message,
                    run_config=run_config,
                )
                logger.info(f"[DEBUG] root_events generator created for session {self.session_id}")

                # Collect rootAgent response
                accumulated_response = ""
                original_query = ""
                event_count = 0

                logger.info(f"[DEBUG] Starting to iterate over root_events for session {self.session_id}")

                # Use proper async context management for the generator
                try:
                    async for event in root_events:
                        event_count += 1
                        logger.info(f"[DEBUG] Received root event #{event_count} for session {self.session_id}")

                        # Forward all rootAgent events to client
                        yield event

                        # Collect response text for followUpAgent
                        if event.content and event.content.parts:
                            for part in event.content.parts:
                                if hasattr(part, 'text') and part.text:
                                    accumulated_response += part.text
                                    logger.info(
                                        f"[DEBUG] Accumulated response length: {len(accumulated_response)} for session {self.session_id}")

                        # Handle usage metadata if available
                        if hasattr(event, 'usage_metadata') and event.usage_metadata:
                            if hasattr(event.usage_metadata, 'candidates_token_count'):
                                logger.info(
                                    f"[DEBUG] API token count: {event.usage_metadata.candidates_token_count} for session {self.session_id}")
                            elif hasattr(event.usage_metadata, 'total_token_count'):
                                logger.info(
                                    f"[DEBUG] Total token count: {event.usage_metadata.total_token_count} for session {self.session_id}")

                        # Check if this is the turn complete for rootAgent
                        if event.turn_complete:
                            logger.info(f"[DEBUG] turn_complete: rootAgent finished for session {self.session_id}")
                            break
                except GeneratorExit:
                    logger.debug(f"[DEBUG] Generator exit detected for session {self.session_id}, cleaning up context")
                except Exception as e:
                    logger.error(f"[DEBUG] Error iterating over root_events for session {self.session_id}: {e}")
                    raise

                logger.info(
                    f"[DEBUG] Finished iterating over root_events. Total events: {event_count} for session {self.session_id}")

                # Yield a response indicating the turn is complete
                yield LlmResponse(turn_complete=True)

                if event_count == 0:
                    logger.error(f"[DEBUG] NO EVENTS from root_runner.run_async for session {self.session_id}!")
                    # This indicates the root agent is not processing the message
                    return

            except Exception as e:
                logger.error(f"[DEBUG] Error in Phase 1 (rootAgent) for session {self.session_id}: {e}")
                log_exception_as_single_line(logger, e, "[DEBUG] Full exception details for Phase 1")

                # Check if this is a quota exhaustion error
                if "429" in str(e) or "RESOURCE_EXHAUSTED" in str(e) or "Quota exceeded" in str(e):
                    logger.error(
                        f"[DEBUG] API Quota exhausted for session {self.session_id}. Providing fallback response.")

                    # Yield a fallback response event
                    from google.genai.types import Content, Part
                    fallback_text = "抱歉，由于API请求限制，暂时无法提供详细分析。请稍后再试。\n\nSorry, due to API request limits, detailed analysis is temporarily unavailable. Please try again later."

                    fallback_event = type('FallbackEvent', (), {
                        'content': Content(role="assistant", parts=[Part.from_text(text=fallback_text)]),
                        'turn_complete': True,
                        'interrupted': False,
                        'partial': False,
                        'author': 'rootAgent'
                    })()

                    logger.info(f"[DEBUG] Yielding fallback response for session {self.session_id}")
                    yield fallback_event

                elif "Maximum concurrent sessions exceeded" in str(e):
                    logger.error(
                        f"[DEBUG] Session limit exceeded for session {self.session_id}. Providing fallback response.")

                    # Yield a session limit fallback response
                    from google.genai.types import Content, Part
                    fallback_text = "当前会话数量已达上限，请稍后再试。\n\nMaximum concurrent sessions reached. Please try again in a moment."

                    fallback_event = type('FallbackEvent', (), {
                        'content': Content(role="assistant", parts=[Part.from_text(text=fallback_text)]),
                        'turn_complete': True,
                        'interrupted': False,
                        'partial': False,
                        'author': 'rootAgent'
                    })()

                    logger.info(f"[DEBUG] Yielding session limit fallback response for session {self.session_id}")
                    yield fallback_event
                else:
                    # For other errors, still provide a generic fallback
                    logger.error(
                        f"[DEBUG] Unexpected error for session {self.session_id}. Providing generic fallback response.")

                    from google.genai.types import Content, Part
                    fallback_text = "系统遇到了一些问题，请稍后再试。\n\nThe system encountered an issue. Please try again later."

                    fallback_event = type('FallbackEvent', (), {
                        'content': Content(role="assistant", parts=[Part.from_text(text=fallback_text)]),
                        'turn_complete': True,
                        'interrupted': False,
                        'partial': False,
                        'author': 'rootAgent'
                    })()

                    logger.info(f"[DEBUG] Yielding generic fallback response for session {self.session_id}")
                    yield fallback_event

                return

        logger.info(f"[DEBUG] Sequential agent execution completed for session {self.session_id}")

        # The sequential processing is complete. The WebSocket connection will remain open
        # and new messages will trigger a new sequential processing cycle.
        logger.info(f"[DEBUG] Sequential agent processing finished for session {self.session_id}")

        # End the generator - the WebSocket connection will handle new messages
        # by creating a new sequential processing cycle when needed
        return


def create_sequential_agent_runner(root_agent, session_service, session_id: str, user_id: str) -> SequentialAgentRunner:
    """
    Factory function to create a SequentialAgentRunner instance.
    """
    return SequentialAgentRunner(root_agent, session_service, session_id, user_id)
