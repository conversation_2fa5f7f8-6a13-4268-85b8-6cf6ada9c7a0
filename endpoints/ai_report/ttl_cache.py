from __future__ import annotations

import threading
import time
from typing import Any, Callable, <PERSON><PERSON><PERSON>, Optional, Iterator, Tuple

from cachetools import LRUCache

_MISSING = object()


def _wrap_getsizeof(getsizeof: Optional[Callable[[Any], int]]):
    if getsizeof is None:
        return None

    def _inner(stored):
        # Only count the size of the value, ignore expire_at
        if isinstance(stored, tuple) and len(stored) == 2:
            return getsizeof(stored[0])
        return getsizeof(stored)

    return _inner


class TTLCache:
    """Thread-safe LRU with per-key TTL (composition over cachetools.LRUCache)."""

    def __init__(
            self,
            maxsize: int = 128,
            *,
            default_ttl: Optional[float] = None,
            getsizeof: Optional[Callable[[Any], int]] = None,
            timer: Callable[[], float] = time.time,
            thread_safe: bool = True,
    ):
        # Store (value, expire_at) in the underlying LRU
        self._cache = LRUCache(maxsize=maxsize, getsizeof=_wrap_getsizeof(getsizeof))
        self.default_ttl = default_ttl
        self.timer = timer
        self._lock: Optional[threading.RLock] = threading.RLock() if thread_safe else None

    def _now(self) -> float:
        return self.timer()

    def _pack(self, value: Any, ttl: float) -> Tuple[Any, float]:
        # Pack value with absolute expire timestamp
        return value, self._now() + float(ttl)

    def _expired(self, expire_at: float) -> bool:
        return self._now() > expire_at

    def _with_lock(self):
        # Context manager for optional thread-safety
        class _Noop:
            def __enter__(self): return None

            def __exit__(self, *exc): return False

        return self._lock if self._lock is not None else _Noop()

    def set_with_ttl(self, key: Hashable, value: Any, ttl: float) -> None:
        # Write with explicit TTL
        pair = self._pack(value, ttl)
        with self._with_lock():
            self._cache[key] = pair

    def get(self, key: Hashable, default: Any = None) -> Any:
        # Safe read with default fallback
        try:
            return self[key]
        except KeyError:
            return default

    def __setitem__(self, key: Hashable, value: Any) -> None:
        # Write using default TTL
        if self.default_ttl is None:
            raise TypeError("default_ttl is not set; use set_with_ttl(key, value, ttl)")
        self.set_with_ttl(key, value, self.default_ttl)

    def __getitem__(self, key: Hashable) -> Any:
        # Read and update LRU order, remove expired entries
        with self._with_lock():
            value, expire_at = self._cache[key]
            if self._expired(expire_at):
                del self._cache[key]
                raise KeyError(key)
            return value

    def __contains__(self, key: object) -> bool:
        # Containment check with TTL validation
        try:
            _ = self[key]
            return True
        except KeyError:
            return False

    def pop(self, key: Hashable, default: Any = _MISSING) -> Any:
        # Remove and return an item, respect TTL
        with self._with_lock():
            try:
                value = self[key]
            except KeyError:
                if default is _MISSING:
                    raise
                return default
            del self._cache[key]
            return value

    def clear(self) -> None:
        # Clear all items
        with self._with_lock():
            self._cache.clear()

    def __len__(self) -> int:
        # May count expired entries until touched
        with self._with_lock():
            return len(self._cache)

    def items(self) -> Iterator[Tuple[Hashable, Any]]:
        # Yield only non-expired (key, value) pairs
        with self._with_lock():
            keys = list(self._cache.keys())
        for k in keys:
            try:
                yield k, self[k]
            except KeyError:
                continue

    def values(self) -> Iterator[Any]:
        # Yield only non-expired values
        for _, v in self.items():
            yield v


cache = TTLCache(
    maxsize=1024 * 512,
    default_ttl=60 * 60 * 24,  # 1 day
)
