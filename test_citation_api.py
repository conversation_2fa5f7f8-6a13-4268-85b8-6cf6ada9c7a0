#!/usr/bin/env python3
"""
Test script to investigate citation functionality in the Hexa API.
This will test various API parameters to see if we can enable citation data.
"""
from __future__ import annotations

import json
import os
import sys
import requests
from typing import Any, Dict, Optional

# Ensure project root on sys.path
ROOT = os.path.dirname(os.path.abspath(__file__))
if ROOT not in sys.path:
    sys.path.insert(0, ROOT)

from hexa.hexa_api_client import HEXA_API_URL, HEXA_API_TOKEN, HEXA_API_USER, DEFAULT_TIMEOUT, Hexa<PERSON><PERSON><PERSON><PERSON>


def call_hexa_with_citations(msg: str, enable_citations: bool = True, **extra_params) -> Dict[str, Any]:
    """
    Enhanced API call that tests various citation-related parameters.
    
    Args:
        msg: The message content to send to the API
        enable_citations: Whether to try to enable citations
        **extra_params: Additional parameters to test
    
    Returns:
        Parsed JSON response as a Python dict
    """
    headers = {
        "Authorization": f"Bearer {HEXA_API_TOKEN}",
        "Content-Type": "application/json",
    }
    
    # Base payload
    payload = {
        "inputs": {"msg": msg},
        "response_mode": "blocking",
        "user": HEXA_API_USER,
    }
    
    # Test various citation-related parameters
    if enable_citations:
        # Try different possible parameter names for enabling citations
        citation_params = {
            "include_citations": True,
            "citations": True,
            "enable_citations": True,
            "include_references": True,
            "references": True,
            "include_sources": True,
            "sources": True,
            "citation_mode": "enabled",
            "reference_mode": "enabled",
        }
        payload.update(citation_params)
    
    # Add any extra parameters
    payload.update(extra_params)
    
    print(f"Testing API call with payload keys: {list(payload.keys())}")
    
    try:
        resp = requests.post(
            HEXA_API_URL, headers=headers, json=payload, timeout=DEFAULT_TIMEOUT
        )
    except requests.Timeout as e:
        raise HexaAPIError(f"Request timeout: {e}") from e
    except requests.ConnectionError as e:
        raise HexaAPIError(f"Connection error: {e}") from e
    except requests.RequestException as e:
        raise HexaAPIError(f"Request failed: {e}") from e

    # Check HTTP status
    if not (200 <= resp.status_code < 300):
        text = resp.text
        raise HexaAPIError(f"HTTP {resp.status_code}. Body: {text[:1000]}")

    # Parse JSON
    try:
        data: Dict[str, Any] = resp.json()
    except ValueError as e:
        raise HexaAPIError("Response is not valid JSON") from e

    return data


def analyze_response_for_citations(response: Dict[str, Any], test_name: str) -> None:
    """Analyze a response for citation-related fields."""
    print(f"\n{'='*80}")
    print(f"ANALYSIS: {test_name}")
    print('='*80)
    
    # Save response for inspection
    filename = f"citation_test_{test_name.lower().replace(' ', '_')}.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(response, f, indent=2, ensure_ascii=False)
    print(f"Response saved to: {filename}")
    
    # Check top-level structure
    print(f"Top-level keys: {list(response.keys())}")
    
    # Deep search for citation-related fields
    def find_citation_fields(data, path=""):
        findings = []
        if isinstance(data, dict):
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key
                if any(keyword in key.lower() for keyword in ['citation', 'reference', 'source', 'link', 'url']):
                    findings.append((current_path, key, type(value).__name__, value))
                if isinstance(value, (dict, list)):
                    findings.extend(find_citation_fields(value, current_path))
        elif isinstance(data, list):
            for i, item in enumerate(data):
                findings.extend(find_citation_fields(item, f"{path}[{i}]"))
        return findings
    
    citation_fields = find_citation_fields(response)
    
    if citation_fields:
        print(f"\nFound {len(citation_fields)} citation-related fields:")
        for path, key, type_name, value in citation_fields:
            print(f"  {path}: {type_name}")
            if isinstance(value, (str, int, float)):
                print(f"    Value: {value}")
            elif isinstance(value, list):
                print(f"    List with {len(value)} items")
                if len(value) > 0 and isinstance(value[0], dict):
                    print(f"    First item keys: {list(value[0].keys())}")
            elif isinstance(value, dict):
                print(f"    Dict keys: {list(value.keys())}")
    else:
        print("\nNo citation-related fields found")
    
    # Check message content
    message = response.get("data", {}).get("outputs", {}).get("message", "")
    if message:
        citation_count = message.count('<grok:render')
        url_count = message.count('http')
        markdown_links = message.count('[](http')
        
        print(f"\nMessage analysis:")
        print(f"  Length: {len(message)} characters")
        print(f"  Citation tags: {citation_count}")
        print(f"  URLs: {url_count}")
        print(f"  Markdown links: {markdown_links}")
        
        if citation_count > 0:
            # Extract citation IDs
            import re
            citation_ids = re.findall(r'citation_id">(\d+)</argument>', message)
            print(f"  Citation IDs: {set(citation_ids)}")
            
            # Show first citation tag
            first_tag = re.search(r'<grok:render[^>]*>.*?</grok:render>', message, re.DOTALL)
            if first_tag:
                print(f"  First citation tag: {first_tag.group()}")
    
    return citation_fields


def test_citation_configurations():
    """Test different API configurations to find citation functionality."""
    
    # Test query that we know previously had citation tags
    test_query = "Who are SOL's investors and team?"
    
    print("TESTING CITATION API CONFIGURATIONS")
    print("="*80)
    print(f"Test query: {test_query}")
    
    # Test 1: Standard API call (baseline)
    print(f"\n{'='*80}")
    print("TEST 1: Standard API call (baseline)")
    print('='*80)
    
    try:
        from hexa.hexa_api_client import call_hexa
        standard_response = call_hexa(test_query)
        analyze_response_for_citations(standard_response, "Standard API Call")
    except Exception as e:
        print(f"Standard API call failed: {e}")
    
    # Test 2: API call with citation parameters
    print(f"\n{'='*80}")
    print("TEST 2: API call with citation parameters")
    print('='*80)
    
    try:
        citation_response = call_hexa_with_citations(test_query, enable_citations=True)
        citation_fields = analyze_response_for_citations(citation_response, "With Citation Parameters")
        
        if citation_fields:
            print("*** CITATION FIELDS FOUND! ***")
            return citation_response
        
    except Exception as e:
        print(f"Citation API call failed: {e}")
    
    # Test 3: Try different parameter combinations
    test_params = [
        {"inputs": {"msg": test_query, "include_citations": True}},
        {"inputs": {"msg": test_query, "citations": "enabled"}},
        {"inputs": {"msg": test_query, "format": "with_citations"}},
        {"inputs": {"msg": test_query}, "output_format": "with_references"},
        {"inputs": {"msg": test_query}, "grok_citations": True},
    ]
    
    for i, params in enumerate(test_params, 3):
        print(f"\n{'='*80}")
        print(f"TEST {i}: Custom parameters - {params}")
        print('='*80)
        
        try:
            headers = {
                "Authorization": f"Bearer {HEXA_API_TOKEN}",
                "Content-Type": "application/json",
            }
            payload = {
                "response_mode": "blocking",
                "user": HEXA_API_USER,
                **params
            }
            
            resp = requests.post(HEXA_API_URL, headers=headers, json=payload, timeout=DEFAULT_TIMEOUT)
            
            if 200 <= resp.status_code < 300:
                response = resp.json()
                citation_fields = analyze_response_for_citations(response, f"Custom Test {i}")
                
                if citation_fields:
                    print("*** CITATION FIELDS FOUND! ***")
                    return response
            else:
                print(f"HTTP {resp.status_code}: {resp.text[:200]}")
                
        except Exception as e:
            print(f"Custom test {i} failed: {e}")
    
    print(f"\n{'='*80}")
    print("CONCLUSION")
    print('='*80)
    print("No citation fields found in any of the tested configurations.")
    print("The API may not support citation data, or it requires different parameters.")
    
    return None


def main():
    """Main function to test citation functionality."""
    result = test_citation_configurations()
    
    if result:
        print("\n*** SUCCESS: Found citation functionality! ***")
        print("Check the saved JSON files for details.")
    else:
        print("\n*** No citation functionality found in current API configuration ***")
        print("Consider checking with the API maintainer about citation parameters.")


if __name__ == "__main__":
    main()
