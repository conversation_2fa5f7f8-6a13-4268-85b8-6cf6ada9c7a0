"""
Data fetching SubGraph
"""
import requests
import urllib.parse
import json
import base64
import pandas as pd
import re

from agent_token_report.agents import PostFilterAgent
from agent_token_report.common.data_processor import DataNodeProcessor
from agent_token_report.prompts.manager_prompt import get_default_prompt
from ..models import DataNode, FetchedData, AgentState, DataType, ChartType, CitationMeta, FilterPostList
from ..mock_data.mock_data import get_mock_data
from langgraph.graph import StateGraph, START
from langgraph.graph.state import CompiledStateGraph
import time
from datetime import datetime
from ..conf.config import TokenReportConfig
import os
from util.logger.logger_utils import setup_logger, log_exception_as_single_line
from functools import partial
from langchain.output_parsers import PydanticOutputParser

logger = setup_logger(__name__)
    
class DataFetcher:
    def __init__(self, langfuse, cfg: TokenReportConfig = None):
        self.cfg = cfg
        self.langfuse = langfuse

    def _extract_service_id_from_url(self, url: str) -> str:
        """Extract service ID from URL. Default to 'bdp-search-mcp' for current URLs."""
        # For current URLs like 'http://bdp-search-mcp.eureka.qa.local:13106/querySquareKb/dsl'
        # we extract 'bdp-search-mcp' from the domain
        from urllib.parse import urlparse
        parsed = urlparse(url)
        hostname = parsed.hostname or ""
        
        # Extract service name from hostname (e.g., "bdp-search-mcp" from "bdp-search-mcp.eureka.qa.local")
        if "." in hostname:
            service_id = hostname.split(".")[0]
        else:
            service_id = hostname
            
        return service_id

    def _extract_endpoint_from_url(self, url: str) -> str:
        """Extract endpoint path from URL."""
        from urllib.parse import urlparse
        parsed = urlparse(url)
        return parsed.path or "/"

    def _build_query_string(self, params: dict) -> str:
        """Convert parameters dictionary to query string."""
        if not params:
            return ""
        return '&'.join(f'{k}={v}' for k, v in params.items())

    def _calculate_time_range(self, end_timestamp_ms: int, days: int) -> tuple[int, int]:
        """Calculate non-overlapping time ranges based on days parameter.
        
        Args:
            end_timestamp_ms: End timestamp in milliseconds
            days: Number of days (1 for recent, >1 for historical)
            
        Returns:
            tuple: (start_timestamp_ms, end_timestamp_ms)
            
        Logic:
            - days = 1: (end_time - 1 day, end_time) - recent data
            - days > 1: (end_time - (days+1) days, end_time - 1 day) - historical data excluding today
        """
        one_day_ms = 86400000
        
        if days == 1:
            # Recent data: today only
            start_time = end_timestamp_ms - one_day_ms * days
            end_time = end_timestamp_ms
        else:
            # Historical data: exclude today
            start_time = end_timestamp_ms - one_day_ms * (days + 1)
            end_time = end_timestamp_ms - one_day_ms
            
        return start_time, end_time

    def _create_typed_data(self, raw_data: FetchedData, data_path: str, default_value) -> FetchedData:
        """Create new FetchedData with all keys except 'data', then add extracted data."""
        # Copy everything except the original "data" key
        result = {k: v for k, v in raw_data.items() if k != "data"}
        # Add the new extracted "data" value
        result["data"] = DataNodeProcessor.safe_nested_get(raw_data, "data", data_path, default=default_value)
        return result

    def _wrap_with_conan_api(self, direct_data_node: DataNode, state: AgentState) -> DataNode:
        """Convert a direct API DataNode to use Conan API as proxy"""
        if not state["conan_key"]:
            return direct_data_node  # Return as-is if no conan key
        
        # Extract components from direct API DataNode
        service_id = self._extract_service_id_from_url(direct_data_node.url)
        endpoint = self._extract_endpoint_from_url(direct_data_node.url)
        service_method = direct_data_node.method.lower()
        
        # Build Conan API input_params based on original method
        if direct_data_node.method == "GET":
            # For GET: params go in endpoint as query string
            query_string = self._build_query_string(direct_data_node.input_params)
            conan_params = {
                "serviceId": service_id,
                "method": service_method,
                "endpoint": f"{endpoint}?{query_string}" if query_string else endpoint
            }
        else:
            # For POST: params go in requestBody
            conan_params = {
                "serviceId": service_id,
                "method": service_method,
                "endpoint": endpoint,
                "requestBody": direct_data_node.input_params
            }
        
        # Create new Conan API DataNode
        return DataNode(
            url=self.cfg.get("conan_api"),
            method="POST",
            data_type=direct_data_node.data_type,
            headers={
                "test-key": state["conan_key"],
                "Content-Type": "application/json"
            },
            input_params=conan_params,
            post_processor=direct_data_node.post_processor
        )

    def build_data_graph(self) -> CompiledStateGraph:
        """Build the data subgraph of the LangGraph"""
        workflow = StateGraph(AgentState)

        workflow.add_node("get_news", self.get_news)
        workflow.add_node("get_posts", self.get_posts)
        workflow.add_node("get_tweets", self.get_tweets)
        workflow.add_node("get_charts", self.get_charts)
        workflow.add_node("get_official_posts", self.get_official_posts)
        workflow.add_node("get_basic_info", self.get_basic_info)
        workflow.add_node("filter_posts", self.filter_posts)
        
        workflow.add_edge(START, "get_charts")
        workflow.add_edge(START, "get_basic_info")
        workflow.add_edge("get_basic_info", "get_news")
        workflow.add_edge("get_basic_info", "get_posts")
        workflow.add_edge("get_basic_info", "get_tweets")
        workflow.add_edge("get_basic_info", "get_official_posts")
        workflow.add_edge("get_posts", "filter_posts")
        workflow.add_edge("get_tweets", "filter_posts")
        workflow.add_edge("get_official_posts", "filter_posts")
        
        return workflow.compile()

    def get_basic_info(self, state: AgentState) -> AgentState:
        """Fetch token basic info, like token numbers, description, official x info"""

        basic_node = self._wrap_with_conan_api(
            DataNode(
                url=f"{self.cfg.get('bdp_search_mcp')}/tradingInsight/v2/queryData",
                method="POST",
                data_type=DataType.PROJECT_INFO,
                input_params={
                    "token": state["token"]
                },
                post_processor=DataNodeProcessor._post_process_basic_data
            ),
            state
        )
        # Fetch all data
        raw_data = self._fetch_single_data(state["token"], basic_node, state["mock_data"])
        
        # Create typed data objects with specific data extractions
        fetched_data_x_data = self._create_typed_data(raw_data, "official_x_data", {})
        project_info = self._create_typed_data(raw_data, "project_info", {})

        return {
            "fetched_data": {
                DataType.TWEETS_PROJECT.value: fetched_data_x_data,
                DataType.PROJECT_INFO.value: project_info
            }
        }

    def get_news(self, state: AgentState) -> AgentState:
        try:
            pplx_prompt = self.langfuse.get_prompt("pplx_prompt", type="chat", cache_ttl_seconds=0)
        except Exception as e:
            logger.warning("Langfuse prompt not available, using default config")
            pplx_prompt = get_default_prompt("pplx_prompt")
        model_cfg = pplx_prompt.config
        fullname = DataNodeProcessor.safe_nested_get(state, "fetched_data", "Project_Info", "data", "content", "fullname")
        website = DataNodeProcessor.safe_nested_get(state, "fetched_data", "Project_Info", "data", "content", "website")

        data_node = DataNode(
            url="https://api.perplexity.ai/chat/completions",
            method="POST",
            data_type=DataType.NEWS,
            headers={
                "Authorization": f"Bearer {self.cfg.get('PPLX_KEY')}",
                "Content-Type": "application/json",
                "Referer": "https://www.perplexity.ai/",
            },
            input_params={
                "search_after_date_filter":  datetime.fromtimestamp(state["end_timestamp_ms"]/1000 - 86400 * 7).strftime("%-m/%-d/%Y"),
                "search_before_date_filter": datetime.fromtimestamp(state["end_timestamp_ms"]/1000).strftime("%-m/%-d/%Y"),
                "messages": pplx_prompt.compile(token=state["token"], fullname=fullname, website=website),
                **model_cfg.get("pplx_params", {})
            },
            post_processor=DataNodeProcessor._post_process_news_data
        )
        return {
            "fetched_data": {
                data_node.data_type.value: 
                self._fetch_single_data(state["token"], data_node, state["mock_data"]) if len(model_cfg.get("pplx_params", {})) > 0 and state["is_core_token"] else {}
            }
        }
    
    def _compose_post_query_dsl(self, state, doc_cnt, days) -> str:
        """Return url encoded elastic search dsl from sqaure post query"""
        # Test dsl at: https://kibana-bdp-feed.prod.toolsfdg.net/app/dev_tools#/console
        # ```code
        # GET /feed_pgc_contents_agent/_search
        # {query_dict}
        # ```
        start_time, end_time = self._calculate_time_range(state["end_timestamp_ms"], days)
        query_dict = {
            "size": doc_cnt,
            "query": {
                "bool": {
                    "must": [
                        {"terms": {"f136.keyword": [state["token"]]}},
                        {"term": {"status": 2}},
                        {"term": {"exposure_level": 1}},
                        {"terms": {"content_type": [1, 2]}},
                        {
                            "nested": {
                                "path": "f269",
                                "query": {
                                    "bool": {
                                        "must": [
                                        {
                                            "term": {
                                            "f269.token": state["token"]
                                            }
                                        },
                                        {
                                            "range": {
                                            "f269.score": {
                                                "gte": 0.8
                                            }
                                            }
                                        }
                                        ]
                                    }
                                }
                            }
                        } # filter token relevence >= 0.8
                    ],
                    "must_not": [
                        {"terms": {"role": [2, 5, 11]}} # not (2: internal ccount, 5: announcement, 11: ugc), announcement will be a seperate query
                    ],
                    "filter": [
                        {"range": {"latest_release_time": {
                            "gte": start_time,
                            "lte": end_time
                        }}}
                    ]
                }
            },
            "collapse": {
                "field": "f184",
                "inner_hits": {
                    "name": "top_f59",
                    "size": 1,
                    "sort": [{"f59": "desc"}]
                }
            },
            "sort": [{"f59": "desc"}]
        }

        dsl = json.dumps(query_dict, separators=(',', ':'))
        return urllib.parse.quote(dsl)

    def get_posts(self, state: AgentState) -> AgentState:
        """Fetch posts data for both 1-day and 7-day periods and merge results"""
        # Create data node generator function - define direct API call and wrap with Conan if needed

        try:
            model_cfg = self.langfuse.get_prompt("pplx_prompt", type="chat", cache_ttl_seconds=0).config
        except Exception as e:
            logger.warning("langfuse prompt fetch failed, using default config")
            model_cfg = get_default_prompt("pplx_prompt").config

        period_count = model_cfg.get("post_params", {}).get("period_count", [{"peroid": 1, "count": 20},{"peroid": 7, "count": 10}])

        create_posts_node = lambda days, doc_cnt: self._wrap_with_conan_api(
            DataNode(
                url=f"{self.cfg.get('bdp_search_mcp')}/querySquareKb/dsl",
                method="GET",
                data_type=DataType.POSTS,
                input_params={
                    "dsl": self._compose_post_query_dsl(state, doc_cnt, days)
                },
                post_processor=DataNodeProcessor._post_process_posts_data
            ),
            state
        )
        # Fetch data from all periods
        fetched_data = [
            self._fetch_single_data(state["token"], create_posts_node(period["peroid"], period["count"]), state["mock_data"])
            for period in period_count
        ]
        # Merge the results
        merged_data = self._merge_fetched_data(*fetched_data)
        
        return {
            "fetched_data": {
                DataType.POSTS.value: merged_data
            }
        }
    
    def filter_posts(self, state: AgentState) -> AgentState:
        # filter post based on token basic info
        #data_key = "Tweets"
        agent_parser = PydanticOutputParser(pydantic_object=FilterPostList)
        filter_agent = PostFilterAgent(agent_parser, self.langfuse, self.cfg, ["Tweets", "Posts", "Posts_Official", "Tweets_Project"])
        filter_posts = filter_agent.process(state['token'], state['end_timestamp_ms'], state['open_market_timestamp_ms'], state['fetched_data'], state['exp_params'])
        
        filtered_fetched_data = {}
        total_num = 0
        remain_num = 0
        filter_detail = {}
        # filter_posts[0] is type of FilterPostList, for loop to get the list of post ids
        if filter_posts[0]:
            for _, v in enumerate(filter_posts[0]):
                data_key = v[0]
                post_ids = set(v[1])
                filter_num = len(post_ids)
                total_num += filter_num

                if filter_num > 0:
                    data_node = DataNodeProcessor.safe_nested_get(state["fetched_data"], data_key, default={})
                    data = DataNodeProcessor.safe_nested_get(data_node, "data",  default={})
                    citation_data = data["citation"]
                    content_data = data["content"]

                    # filter out post id
                    origin_num = len(citation_data)
                    filtered_citation_data = [val for i, val in enumerate(citation_data) if i not in post_ids]
                    filtered_content_data = [val for i, val in enumerate(content_data) if i not in post_ids]
                    
                    data_node["data"] = {
                        "citation": filtered_citation_data,
                        "content": filtered_content_data
                    }
                    filtered_fetched_data[data_key] = data_node
                    filter_detail[data_key] = (origin_num, filter_num)
                    remain_num += origin_num - filter_num
        
        if total_num > 0:
            logger.info(f"{total_num} posts have been filtered out, remain {remain_num} posts, filter detail: {filter_detail}")
        
        return {
            "fetched_data": filtered_fetched_data
        }

    def _compose_official_post_query_dsl(self, state, doc_cnt, days) -> str:
        """Return url encoded elastic search dsl from sqaure post query"""
        # Test dsl at: https://kibana-bdp-feed.prod.toolsfdg.net/app/dev_tools#/console
        # ```code
        # GET /feed_pgc_contents_agent/_search
        # {query_dict}
        # ```
        start_time, end_time = self._calculate_time_range(state["end_timestamp_ms"], days)
        query_dict = {
            "size": doc_cnt,
            "query": {
                "bool": {
                    "must": [
                        {"terms": {"f136.keyword": [state["token"]]}},
                        {"term": {"status": 2}},
                        {"term": {"exposure_level": 1}},
                        {"terms": {"content_type": [1, 2]}},
                        {"terms": {"role": [5]}},  # binance anouncement
                        {"terms": {"lan": ["en"]}} # english only
                    ],
                    "filter": [
                        {"range": {"latest_release_time": {
                            "gte": start_time,
                            "lte": end_time
                        }}}
                    ]
                }
            },
            "collapse": {
                "field": "f184",
                "inner_hits": {
                    "name": "top_f59",
                    "size": 1,
                    "sort": [{"f59": "desc"}]
                }
            },
            "sort": [{"f59": "desc"}]
        }

        dsl = json.dumps(query_dict, separators=(',', ':'))
        return urllib.parse.quote(dsl)

    def get_official_posts(self, state: AgentState) -> AgentState:
        """Fetch official posts data for both 1-day and 3-day periods and merge results"""
        # Create data node generator function - define direct API call and wrap with Conan if needed
        try:
            model_cfg = self.langfuse.get_prompt("pplx_prompt", type="chat", cache_ttl_seconds=0).config
        except Exception as e:
            logger.warning("Langfuse prompt not available, using default config")
            model_cfg = get_default_prompt("pplx_prompt").config
        period_count = model_cfg.get("official_post_params", {}).get("period_count", [{"peroid": 1, "count": 10},{"peroid": 3, "count": 5}])

        create_posts_node = lambda days, doc_cnt: self._wrap_with_conan_api(
            DataNode(
                url=f"{self.cfg.get('bdp_search_mcp')}/querySquareKb/dsl",
                method="GET",
                data_type=DataType.POSTS_OFFICIAL,
                input_params={
                    "dsl": self._compose_official_post_query_dsl(state, doc_cnt, days)
                },
                post_processor=DataNodeProcessor._post_process_posts_data
            ),
            state
        )
        # Fetch data from all periods
        fetched_data = [
            self._fetch_single_data(state["token"], create_posts_node(period["peroid"], period["count"]), state["mock_data"])
            for period in period_count
        ]
        # Merge the results
        merged_data = self._merge_fetched_data(*fetched_data)
        
        return {
            "fetched_data": {
                DataType.POSTS_OFFICIAL.value: merged_data
            }
        }

    def get_tweets(self, state: AgentState) -> AgentState:
        """Fetch tweets data for both 1-day and 7-day periods and merge results"""
        try:
            model_cfg = self.langfuse.get_prompt("pplx_prompt", type="chat", cache_ttl_seconds=0).config
        except Exception as e:
            logger.warning("Langfuse prompt not available, using default config")
            model_cfg = get_default_prompt("pplx_prompt").config
        author_ids = model_cfg.get("x_params", {}).get("authorIds", "")
        # Create data node generator function - define direct API call and wrap with Conan if needed
        create_tweets_node = lambda days, doc_count: self._wrap_with_conan_api(
            DataNode(
                url=f"{self.cfg.get('bdp_search_mcp')}/queryXKb",
                method="GET",
                data_type=DataType.TWEETS,
                input_params={
                    "query": state["token"],
                    "startTime": self._calculate_time_range(state["end_timestamp_ms"], days)[0],
                    "endTime": self._calculate_time_range(state["end_timestamp_ms"], days)[1],
                    "docCount": doc_count,
                    "authorIds": author_ids
                },
                post_processor=DataNodeProcessor._post_process_tweets_data
            ),
            state
        )
        # Fetch data from all periods
        period_count = model_cfg.get("x_params", {}).get("period_count", [{"peroid": 1, "count": 15},{"peroid": 7, "count": 10}])
        fetched_data = [
            self._fetch_single_data(state["token"], create_tweets_node(period["peroid"], period["count"]), state["mock_data"])
            for period in period_count
        ]

        # Merge the results
        merged_data = self._merge_fetched_data(*fetched_data)
        return {
            "fetched_data": {
                DataType.TWEETS.value: merged_data
            }
        }

    def get_charts(self, state: AgentState) -> AgentState:
        """Fetch charts data using wrapper function"""
        # Define direct API call and wrap with Conan if needed
        end_time = state["end_timestamp_ms"]
        open_market_timestamp_ms = state["open_market_timestamp_ms"]
        live_time = end_time - open_market_timestamp_ms

        data_node = self._wrap_with_conan_api(
            DataNode(
                url=f"{self.cfg.get('bdp_search_mcp')}/ai-report/offline/charts",
                method="GET",
                data_type=DataType.CHARTS,
                input_params={
                    "token": state["token"],
                    "timestamp": state["end_timestamp_ms"]
                },
                post_processor=partial(DataNodeProcessor._post_process_charts_data, live_time=live_time)
            ),
            state
        )
        return {
            "fetched_data": {
                data_node.data_type.value: 
                self._fetch_single_data(state["token"], data_node, state["mock_data"])
            }
        }

    def _merge_fetched_data(self, *data_objects: FetchedData) -> FetchedData:
        """Merge multiple FetchedData objects, combining their data and handling duplicates"""
        if not data_objects:
            return FetchedData(
                url=None,
                data_type="",
                request_params=[],
                data={},
                status="error",
                error="No data objects provided"
            )
        
        # Filter successful and failed data objects
        successful_data = [data for data in data_objects if data["status"] == "success"]
        failed_data = [data for data in data_objects if data["status"] != "success"]
        
        # If no successful data, return combined errors
        if not successful_data:
            combined_error = "; ".join([data.get('error', '') for data in failed_data])
            first_failed = failed_data[0]
            return FetchedData(
                url=first_failed.get("url"),
                data_type=first_failed["data_type"],
                request_params=first_failed.get("request_params"),
                data={},
                status="error",
                error=combined_error
            )
        
        # Merge all successful data
        merged_data = {"citation": [], "content": []}
        seen_ids = set()
        all_request_params = []
        
        # Process each successful data source
        for data in successful_data:
            data_content = data.get("data", {})
            citations = data_content.get("citation", [])
            contents = data_content.get("content", [])
            
            # Collect request params
            if data.get("request_params"):
                all_request_params.extend(data["request_params"])
            
            # Create lookup dict for content by ID for faster access
            content_by_id = {content["id"]: content for content in contents}
            
            for citation in citations:
                citation_id = citation["id"]
                if citation_id not in seen_ids:
                    merged_data["citation"].append(citation)
                    # Add corresponding content if it exists
                    if citation_id in content_by_id:
                        merged_data["content"].append(content_by_id[citation_id])
                    seen_ids.add(citation_id)
        
        # Use first successful data object for metadata
        first_successful = successful_data[0]
        return FetchedData(
            url=first_successful.get("url"),
            data_type=first_successful["data_type"],
            request_params=all_request_params,
            data=merged_data,
            status="success",
            error=""
        )

    def _fetch_single_data(self, token: str, data_node: DataNode, mock_data: bool = False) -> FetchedData:
        """Fetch data from HTTP endpoint or return mock data if in mock mode"""
        if mock_data:
            logger.info(f"Successfully fetched {data_node.data_type.value} data from MOCK DATA")  
            return FetchedData(
                **get_mock_data(token, data_node.data_type)
            )
        
        processed_params = data_node.input_params
        
        for attempt in range(data_node.retries):
            try:
                if data_node.method == "GET":
                    response = requests.get(
                        data_node.url,
                        params=processed_params,
                        headers=data_node.headers or {},
                        timeout=data_node.timeout
                    )
                else:
                    response = requests.post(
                        data_node.url,
                        json=processed_params,
                        headers=data_node.headers or {},
                        timeout=data_node.timeout
                    )
                
                error_trace = None
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"Successfully fetched {data_node.data_type.value} data from {data_node.url}")                     
                    return FetchedData(
                                url=data_node.url,
                                data_type=data_node.data_type.value,
                                request_params=[processed_params],
                                data=data_node.post_processor(data) if data_node.post_processor else data,
                                status="success",
                                error="")
                else:
                    logger.warning(f"HTTP {response.status_code} for {data_node.url}")
                        
            except Exception as e:
                error_trace = log_exception_as_single_line(logger, e, f"Exception occurred in data fetching, Attempt {attempt + 1} failed for {data_node.url}")

            if attempt == data_node.retries - 1:
                logger.info(f"return fetched data, attempt #{attempt}")                      
                return FetchedData(
                            url=data_node.url,
                            data_type=data_node.data_type.value,
                            request_params=[processed_params],
                            data={},
                            status="error",
                            error=error_trace or (str(response.status_code) if 'response' in locals() else str(e))
                        )
            # Exponential backoff
            time.sleep(2 ** attempt)