"""
Report Generation Subgraph

This subgraph contains all the agent logic for generating reports:
- Single agent mode: Uses one agent for analysis
- Multi-agent mode: Coordinates news, technical, and manager agents
"""
from langgraph.graph import StateGraph, END, START
from langgraph.graph.state import CompiledStateGraph
from langchain.output_parsers import PydanticOutputParser

from ..models import AgentState, AnalysisReport
from ..agents import NewsAgent, TechnicalAgent, ManagerAgent, SingleAgent
from util.logger.logger_utils import setup_logger, log_exception_as_single_line

logger = setup_logger(__name__)

class ReportGeneration:
    """Subgraph for report generation containing all agent logic"""
    
    def __init__(self, langfuse, cfg):
        # Initialize parser
        self.agent_parser = PydanticOutputParser(pydantic_object=AnalysisReport)
        
        # Initialize agents
        self.news_agent = NewsAgent(self.agent_parser, langfuse, cfg)
        self.technical_agent = TechnicalAgent(self.agent_parser, langfuse, cfg)
        self.manager_agent = ManagerAgent(self.agent_parser, langfuse, cfg)
        self.single_agent = SingleAgent(self.agent_parser, langfuse, cfg)
        
    def build_subgraph(self) -> CompiledStateGraph:
        """Build the report generation subgraph"""
        workflow = StateGraph(AgentState)
        
        # Add all agent nodes
        workflow.add_node("single_agent", self._single_agent_node)
        workflow.add_node("multi_start", self._multi_start_node)
        workflow.add_node("news_agent", self._news_analysis_node)
        workflow.add_node("technical_agent", self._technical_analysis_node)
        workflow.add_node("manager_agent", self._manager_synthesis_node)
        
        # Define routing function for mode selection
        def route_based_on_mode(state: AgentState) -> str:
            """Route to single-agent or multi-agent flow based on mode"""
            return "single_agent" if state.get('mode') == "single-agent" else "multi_start"
        
        # Add conditional routing from start
        workflow.add_conditional_edges(
            START,
            route_based_on_mode,
            {
                "single_agent": "single_agent",
                "multi_start": "multi_start"
            }
        )
        
        # Single-agent flow goes directly to END
        workflow.add_edge("single_agent", END)
        
        # Multi-agent parallel flow
        workflow.add_edge("multi_start", "news_agent")
        workflow.add_edge("multi_start", "technical_agent")
        workflow.add_edge("news_agent", "manager_agent")
        workflow.add_edge("technical_agent", "manager_agent")
        workflow.add_edge("manager_agent", END)
        
        return workflow.compile()
    
    def _single_agent_node(self, state: AgentState) -> AgentState:
        """Single agent node that handles both news and technical analysis"""
        error = []
        try:
            analysis, error = self.single_agent.process(state['token'], state['end_timestamp_ms'], state['open_market_timestamp_ms'], state['fetched_data'], state['exp_params'])
            
        except Exception as e:
            log_exception_as_single_line(logger, e, "Exception occurred in single agent")
        
        return {
            "before_report": analysis.model_dump() if analysis else None,
            "errors": error
        }
    
    def _multi_start_node(self, state: AgentState) -> AgentState:
        """Pass-through node for multi-agent start"""
        return state
    
    def _news_analysis_node(self, state: AgentState) -> AgentState:
        """News analysis node using fetched data"""
        error = []
        try:
            news_analysis, error = self.news_agent.process(state['token'], state['end_timestamp_ms'], state['open_market_timestamp_ms'], state['fetched_data'], state['exp_params'])
            
        except Exception as e:
            log_exception_as_single_line(logger, e, "Exception occurred in news agent")
        
        return {
            "news_analysis": news_analysis.model_dump() if news_analysis else None,
            "errors": error
        }
    
    def _technical_analysis_node(self, state: AgentState) -> AgentState:
        """Technical analysis node using fetched data"""
        error = []
        try:
            technical_analysis, error = self.technical_agent.process(state['token'], state['end_timestamp_ms'], state['open_market_timestamp_ms'], state['fetched_data'], state['exp_params'])
            
        except Exception as e:
            log_exception_as_single_line(logger, e, "Exception occurred in technical agent")
        
        return {
            "technical_analysis": technical_analysis.model_dump() if technical_analysis else None,
            "errors": error
        }
    
    def _manager_synthesis_node(self, state: AgentState) -> AgentState:
        """Manager synthesis node"""
        error = []
        try:
            report_data = {
                "news_analysis": state['news_analysis'],
                "technical_analysis": state['technical_analysis']
            }
            before_report, error = self.manager_agent.process(state['token'], state['end_timestamp_ms'], state['open_market_timestamp_ms'], report_data, state['exp_params'])
            
        except Exception as e:
            log_exception_as_single_line(logger, e, "Exception occurred in manager agent")
        
        return {
            "before_report": before_report.model_dump() if before_report else None,
            "errors": error
        }