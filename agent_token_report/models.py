"""
Data models and enums for the multi-agent system.
"""

from typing import Dict, Any, List, Optional, Callable, TypedDict, Annotated
from pydantic import BaseModel, Field, RootModel
from enum import Enum
from dataclasses import dataclass
import operator
from langgraph.channels.binop import BinaryOperatorAggregate

class HttpMethod(Enum):
    GET = "GET"
    POST = "POST"

class DataType(Enum):
    NEWS = "News"
    POSTS = "Posts"
    POSTS_OFFICIAL = "Posts_Official"
    TWEETS = "Tweets"
    TWEETS_PROJECT = "Tweets_Project"
    PROJECT_INFO = "Project_Info"
    CHARTS = "Charts"

class ChartType(Enum):
    TECHNICALS = "technicals"
    UNLOCK = "unlock"
    MONEY_FLOW = "money_flow"

@dataclass
class DataNode:
    """DataNode for HTTP data fetching"""
    url: str
    data_type: DataType
    method: HttpMethod = HttpMethod.GET
    headers: Optional[Dict[str, str]] = None
    timeout: int = 30
    retries: int = 2
    input_params: Optional[Dict[str, Any]] = None
    post_processor: Optional[Callable[[Any], Any]] = None

class FetchedData(TypedDict):
    url: Optional[str] = None
    data_type: str
    request_params: Optional[List[Dict[str, Any]]] = None
    data: Optional[Dict[str, Any]] = Field(default={}, description="store data")
    status: str
    error: str

class CitationMeta(TypedDict):
    id: str
    url: str
    avatar: Optional[str] = Field(default=None, description="avatar url")
    author: Optional[str] = Field(default=None, description="post author name")
    title: str

class GuardrailResult(TypedDict):
    status: str = Field(description="FINISHED/ERROR/RETRY")
    issues: Optional[List[str]] = Field(description="Issues found", default=None)
    filtered_content: Optional[Dict[str, Any]] = Field(description="Filtered content", default=None)

# LLM output models: Token Report
class Citation(BaseModel):
    type: str
    subtype: Optional[str] = Field(default='')
    signals: List[str]

class TextWithCitation(BaseModel):
    text: str
    citation: Optional[List[Citation]] = Field(default=[])

class AnalysisReport(BaseModel):
    opportunities: Optional[List[TextWithCitation]] = Field(default=[])
    risks: Optional[List[TextWithCitation]] = Field(default=[])
    community_sentiment: Optional[List[TextWithCitation]] = Field(default=[])
    TLDR: Optional[List[TextWithCitation]] = Field(default=[])
    overview: Optional[List[TextWithCitation]] = Field(default=[])

def dict_reducer(a: dict, b: dict | None) -> dict:
    """Combine two dictionaries for result aggregation.

    Args:
        a: First dictionary
        b: Second dictionary (may be None)

    Returns:
        Combined dictionary with values from both inputs

    Examples:
        >>> dict_reducer({'a': 1}, {'b': 2})
        {'a': 1, 'b': 2}
        >>> dict_reducer({'a': 1}, None)
        {'a': 1}
    """
    if b is None:
        return a
    return {**a, **b}


# LangGraph state
class AgentState(TypedDict):
    """State object that flows through the graph"""
    is_core_token: bool
    mock_data: bool
    mode: str
    token: str
    end_timestamp_ms: int
    open_market_timestamp_ms: int
    market_status: str
    conan_key: str
    retry_count: int
    needs_retry: bool
    fetched_data: Annotated[Dict[str, Any], BinaryOperatorAggregate(Dict[str, Any], dict_reducer)]
    news_analysis: Optional[AnalysisReport]
    technical_analysis: Optional[AnalysisReport]
    before_report: Optional[AnalysisReport]
    guardrail_result: Optional[GuardrailResult]
    output_report: Optional[AnalysisReport]
    output_citation: Optional[List]
    output_charts: Optional[Dict]
    success: bool
    errors: Annotated[list, operator.add]
    exp_params: Optional[Dict[str, Any]]


# For filter post agent output use
class FilterPostList(BaseModel):
    Tweets_Project: Optional[List[int]] = Field(default=[])
    Tweets: Optional[List[int]] = Field(default=[])
    Posts: Optional[List[int]] = Field(default=[])
    Posts_Official: Optional[List[int]] = Field(default=[])