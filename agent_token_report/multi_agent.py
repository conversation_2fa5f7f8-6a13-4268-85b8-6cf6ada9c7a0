"""
Main LangGraph multi-agent system implementation.
"""

from typing import Dict, Any
from langgraph.graph import StateGraph, END, START
from langgraph.graph.state import CompiledStateGraph
import re
from datetime import datetime

from agent_token_report.subgraphs.data_fetcher import DataNodeProcessor
from .models import AgentState, DataType, ChartType
from .subgraphs import DataFetcher, ReportGeneration, DifyGuardrail
from .conf.config import TokenReportConfig

from util.logger.logger_utils import setup_logger, log_exception_as_single_line
from util.logger.log_context import get_trace_id
logger = setup_logger(__name__)

from langfuse.langchain import CallbackHandler
from langfuse import get_client
import json

class LangGraphMultiAgentSystem:
    """Multi-agent system using LangGraph with DataNode and Guardrails"""
    
    def __init__(self):
        self.cfg = TokenReportConfig()
        
        # Initialize Langfuse with error handling
        self.langfuse_enabled = True
        try:
            self.langfuse_handler = CallbackHandler()
            self.langfuse = get_client()
            self.langfuse.get_prompt("pplx_prompt")
            logger.info("Langfuse initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize Langfuse: {e}. Continuing without tracing.")
            self.langfuse_enabled = False
            self.langfuse_handler = None
            self.langfuse = None
        
        self.data_fetcher = DataFetcher(langfuse=self.langfuse, cfg=self.cfg)
        self.report_generation = ReportGeneration(self.langfuse, self.cfg)
        self.api_guardrail = DifyGuardrail(self.cfg)

        # Build the graph
        self.graph = self._build_graph()
        logger.info("LangGraph Multi-Agent System initialized")

    def _build_graph(self) -> CompiledStateGraph:
        """Build the simplified LangGraph workflow with 4 main nodes"""
        # Get configured data nodes
        sub_datagraph = self.data_fetcher.build_data_graph()
        report_subgraph = self.report_generation.build_subgraph()

        workflow = StateGraph(AgentState)
        
        # Add the 4 main nodes
        workflow.add_node("data_fetcher", sub_datagraph)
        workflow.add_node("report_generation", report_subgraph)
        workflow.add_node("guardrail_agent", self._guardrail_node)
        workflow.add_node("post_processor", self._post_processor_node)
        
        # Define routing function after guardrail
        def route_after_guardrail(state: AgentState) -> str:
            """Route after guardrail - check if retry is needed"""
            return "data_fetcher" if state.get('needs_retry', False) else "post_processor"
        
        # Build the linear flow with retry logic
        workflow.add_edge(START, "data_fetcher")
        workflow.add_edge("data_fetcher", "report_generation")
        workflow.add_edge("report_generation", "guardrail_agent")
        
        # Add conditional routing after guardrail to handle retry
        workflow.add_conditional_edges(
            "guardrail_agent",
            route_after_guardrail,
            {
                "data_fetcher": "data_fetcher",  # Retry from data fetcher
                "post_processor": "post_processor"  # Normal flow to post processor
            }
        )
        
        # End point
        workflow.add_edge("post_processor", END)
        
        return workflow.compile()
    
    
    def _guardrail_node(self, state: AgentState) -> AgentState:
        """Guardrail filtering node"""
        errors = []
        try:
            if not state['before_report']:
                return {
                    "errors": ["No final report available for guardrail check"]
                }
            
            # Use API version (guardrail.py)
            content_data = self.api_guardrail.compose_input(state)
            guardrail_result, errors = self.api_guardrail.validate_content(content_data)
                        
            logger.info(f"Guardrail processing completed - Status: {guardrail_result['status']}")
            
            # Check if retry is needed and allowed
            if guardrail_result['status'] == "RETRY" and state['retry_count'] < 1:
                logger.info(f"Retry needed due to high issue count, current retry_count: {state['retry_count']}")
                # Clear previous analysis results for retry
                return {
                    "retry_count": state['retry_count'] + 1,
                    "guardrail_result": guardrail_result,
                    "needs_retry": True,
                    "news_analysis": None,
                    "technical_analysis": None,
                    "before_report": None,
                    "output_report": None,
                    "fetched_data": {},
                    "errors": errors
                }
            
        except Exception as e:
            log_exception_as_single_line(logger, e, "Exception occurred in guardrail")
        
        return {
            "before_report": state['before_report'],
            "output_report": guardrail_result['filtered_content'] if guardrail_result['status'] == "FINISHED" else None,
            "guardrail_result": guardrail_result,
            "needs_retry": False,
            "errors": errors
        }

    def _post_processor_node(self, state: AgentState) -> AgentState:
        # if guardrail_result is not available, use the before_report as output_report
        def replace_timestamps_to_time(text):
            """
            识别文本中的13位数字，尝试将有效的时间戳替换为HH:MM格式
            无效的时间戳保持原样
            """
            def timestamp_replacer(match):
                timestamp_str = match.group(0)
                try:
                    # 将13位毫秒时间戳转换为秒
                    timestamp_ms = int(timestamp_str)
                    timestamp_s = timestamp_ms / 1000
                    dt = datetime.fromtimestamp(timestamp_s)
                    # 检查年份是否在合理范围内（避免无意义的时间）
                    if 2025 <= dt.year <= 2100:
                        return dt.strftime('%m-%d %H:%S') + "(UTC+0)"
                    else:
                        return timestamp_str  # 保持原样
                except (ValueError, OSError, OverflowError):
                    # 转换失败，保持原数字
                    return timestamp_str
            # 使用正则表达式匹配13位数字，使用word boundary确保完整匹配
            pattern = r'\b\d{13}\b'
            result = re.sub(pattern, timestamp_replacer, text)
            return result

        errors = []
        try:
            if "guardrail_result" not in state or "output_report" not in state or not state["guardrail_result"] or not state["output_report"]:
                logger.warning("Guardrail fail, skip for now")
                output_report = state["before_report"]
            else:
                output_report = state["output_report"]
            
            if not output_report or not output_report.get("TLDR", []) or not output_report.get("overview", []):
                error = "Empty or not enough report"
                logger.error(error)
                errors.append(error)
                return {
                    "output_report": output_report,
                    "output_charts": None,
                    "output_citation": None,
                    "errors": errors,
                    "success": False
                }
            
            def dedupe_to_dict(citations):
                result = {}
                for citation in citations:
                    cid = citation.get("id")
                    if cid and cid not in result:
                        result[cid] = citation
                return result

            # Process all citations into ID-keyed dicts with deduplication
            news_citations = DataNodeProcessor.safe_nested_get(state["fetched_data"], DataType.NEWS.value, "data", "citation", default=[])
            tweets_citations = (
                DataNodeProcessor.safe_nested_get(state["fetched_data"], DataType.TWEETS.value, "data", "citation", default=[]) +
                DataNodeProcessor.safe_nested_get(state["fetched_data"], DataType.TWEETS_PROJECT.value, "data", "citation", default=[])
            )
            
            posts_citations = (
                DataNodeProcessor.safe_nested_get(state["fetched_data"], DataType.POSTS.value, "data", "citation", default=[]) +
                DataNodeProcessor.safe_nested_get(state["fetched_data"], DataType.POSTS_OFFICIAL.value, "data", "citation", default=[])
            )

            origin_citations = {
                DataType.NEWS.value: dedupe_to_dict(news_citations),
                DataType.TWEETS.value: dedupe_to_dict(tweets_citations), 
                DataType.POSTS.value: dedupe_to_dict(posts_citations)
            }
            
            filter_citations = {DataType.NEWS.value: [],
                        DataType.TWEETS.value: [],
                        DataType.POSTS.value: []}

            if output_report:
                # Collect all citation signals (IDs) by type from the report
                report_signals = {
                    DataType.NEWS.value: set(),
                    DataType.TWEETS.value: set(),
                    DataType.POSTS.value: set()
                }
                
                # Dynamically check all fields in the report
                for section in output_report.values():
                    if isinstance(section, list):
                        for item in section:
                            # 处理text
                            item_text = item.get("text", "")
                            item["text"] = replace_timestamps_to_time(item_text)
                            if item_text != item["text"]:
                                logger.warning(f"Replaced timestamp in text: {item_text} -> {item['text']}")
                            # 处理citation
                            all_citation = item.get("citation", [])
                            citations_to_remove = []
                            for citation in all_citation:
                                citation_type = citation.get("type","")
                                if citation_type in set(['Posts', 'News', 'Tweets']):
                                    # leave only valid_citations to ensure it's a valid citation
                                    llm_citations = set(citation["signals"])
                                    remain_citations = llm_citations & set(origin_citations[citation_type].keys())
                                    if len(remain_citations) == 0:
                                        logger.warning(f"Invalid citation detected: {citation_type}/{llm_citations}")
                                        citations_to_remove.append(citation)
                                    elif len(remain_citations) < len(llm_citations):
                                        logger.warning(f"Invalid citation detected: {citation_type}/{llm_citations - set(origin_citations[citation_type].keys())}")
                                        citation["signals"] = list(remain_citations)
                                    else:
                                        citation["signals"] = list(remain_citations)
                                    # update report_signals with valid citations
                                    if len(remain_citations) > 0:
                                        report_signals[citation_type].update(remain_citations)
                                elif citation_type == 'Technicals':
                                    # deduplicate signals
                                    llm_citations = set(citation["signals"])
                                    citation["signals"] = list(llm_citations)
                                    
                                    # check citation subtype
                                    # this fix is not optimal, though it works for now
                                    if citation["subtype"] not in set(['Vol', 'EMA', 'MACD', 'RSI', 'ATR', 'BollBands', 'STDEV']):
                                        logger.warning(f"Invalid citation subtype detected: {citation['subtype']}")
                                        citation["subtype"] = 'Vol'
                                elif citation_type == 'Money Flow':
                                    # force Money Flow, subtype to be empty and signals to be empty
                                    citation["subtype"] = ""
                                    citation["signals"] = []
                                elif citation_type == 'Token Unlock':
                                    unlock_data = DataNodeProcessor.safe_nested_get(state["fetched_data"], DataType.CHARTS.value, "data", ChartType.UNLOCK.value, default=[])
                                    # check if unlock_data is empty
                                    if len(unlock_data) == 0:
                                        logger.warning(f"Invalid citation detected: {citation_type}/{citation}")
                                        citations_to_remove.append(citation)
                                else:
                                    logger.warning(f"Invalid citation detected: {citation_type}/{citation}")
                                    citations_to_remove.append(citation)
                            
                            # Remove citations after the for loop iteration
                            for citation in citations_to_remove:
                                all_citation.remove(citation)
                
                # Filter origin_citations to only include citations with matching signals (IDs)
                for data_type in report_signals:
                    filter_citations[data_type] = [
                        origin_citations[data_type][citation_id]
                        for citation_id in report_signals[data_type]
                        if citation_id in origin_citations[data_type]
                    ]
        
            # Extract the final report and it's citation
            charts_origin = DataNodeProcessor.safe_nested_get(state, "fetched_data", DataType.CHARTS.value, "data", "origin", default={})

            return {
                "output_report": output_report,
                "output_charts": charts_origin,
                "output_citation": filter_citations,
                "success": True
            }
        except Exception as e:
            error = log_exception_as_single_line(logger, e, "Exception occurred in post process")
            errors.append(error)
            return {
                "output_report": None,
                "output_charts": None,
                "output_citation": None,
                "errors": errors,
                "success": False
            }

    def _run_graph(self, initial_state, trace_id, token, end_timestamp_ms):
        """Helper method to run the graph with conditional callbacks"""
        final_state = {}
        for final_state in self.graph.with_config({
            "run_name": "token_report",
            "metadata": {
                "trace_id": trace_id,
                "token": token,
                "end_timestamp_ms": end_timestamp_ms
            }
        }).stream(
            initial_state,
            stream_mode="values",
            config={"callbacks": [self.langfuse_handler]} if self.langfuse_enabled else {}
        ):
            pass
        
        return final_state
            
    def run(self, token, end_timestamp_ms: int = None, mode: str = None, open_market_timestamp_ms: int = 0, market_status: str = None, conan_key: str = None, mock_data: bool = False, is_core_token: bool = False, exp_params: dict = None) -> Dict[str, Any]:
        """Process data through the LangGraph workflow"""
        logger.info("Starting LangGraph multi-agent processing")
        
        if exp_params:
            logger.info(f"Hit experiment, params: {exp_params}")
        # mode priority exp_params["mode"] > mode
        # support mode as experiment params
        exp_mode = DataNodeProcessor.safe_nested_get(exp_params, "mode", default=None)
        if exp_mode:
            logger.info(f"Hit experiment, agent running mode: {exp_mode}")
        mode = exp_mode or mode

        # Initialize state
        initial_state = AgentState(
            is_core_token=is_core_token,
            mock_data=mock_data,
            mode=mode if mode else "single-agent",
            token=token,
            end_timestamp_ms=end_timestamp_ms,
            open_market_timestamp_ms=open_market_timestamp_ms if open_market_timestamp_ms is not None else 0,
            market_status=market_status,
            conan_key=conan_key or self.cfg.get("conan_key", None),
            retry_count=0,
            needs_retry=False,
            fetched_data={},
            errors=[],
            exp_params=exp_params
        )
        
        try:
            # Handle trace ID creation
            if self.langfuse_enabled and get_trace_id() == "-":
                trace_id = self.langfuse.create_trace_id()
            else:
                trace_id = get_trace_id()

            # Run with or without Langfuse tracing
            if self.langfuse_enabled:
                with self.langfuse.start_as_current_span(
                    name="spot_token_generation",
                    trace_context={"trace_id": trace_id.replace("-", "")}
                ) as span:
                    final_state = self._run_graph(initial_state, trace_id, token, end_timestamp_ms)
            else:
                logger.info("Running without Langfuse tracing")
                final_state = self._run_graph(initial_state, trace_id, token, end_timestamp_ms)

            result = {
                "report": final_state["output_report"],
                "citation": final_state["output_citation"],
                "charts": final_state["output_charts"],
                "success": final_state["success"],
                "trace_id": trace_id
            }
            logger.info("LangGraph processing completed successfully")
            return result
            
        except Exception as e:
            log_exception_as_single_line(logger, e, "Exception occurred in LangGraph")
            return {
                "report": None,
                "citation": {},
                "charts": {},
                "success": False,
                "trace_id": trace_id
            }
