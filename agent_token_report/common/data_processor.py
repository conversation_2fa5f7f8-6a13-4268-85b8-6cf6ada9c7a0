import urllib.parse
import base64
import pandas as pd
import re
from ..models import ChartType, CitationMeta
from datetime import datetime

class DataNodeProcessor:

    @staticmethod
    def _post_process_news_data(data: dict) -> dict:
        citations = data["citations"]
        
        if len(citations) > 0:
            for idx, citation in enumerate(data["search_results"]):
                citation["id"] = str(idx + 1)
            
            converted_citation = []
            for citation in data["search_results"]:
                converted_citation.append(
                    CitationMeta(
                        id = citation["id"],
                        url = citation["url"],
                        title = citation["title"],
                        author = urllib.parse.urlparse(citation["url"]).netloc
                    )
                )
            return {
                "citation": converted_citation,
                "content": [data["choices"][0]["message"]["content"]]
            }
        else:
            return {
                "citation": [],
                "content": []
            }

    @staticmethod
    def _post_process_posts_data(data: dict) -> dict:
        def generate_url(id, title):
            if len(title) == 0:
                query_string = f'postId={id}'
                encoded_query = base64.b64encode(query_string.encode('utf-8')).decode('utf-8')
                return f'bnc://app.binance.com/mp/app?appId=znf9fpiMh6ufdU3vDtAvi4&startPagePath=cGFnZXMvYml0cy1idXp6L2luZGV4&startPageQuery={encoded_query}'
            else:
                query_string = f'articleId={id}'
                encoded_query = base64.b64encode(query_string.encode('utf-8')).decode('utf-8')
                return f'bnc://app.binance.com/mp/app?appId=znf9fpiMh6ufdU3vDtAvi4&startPagePath=cGFnZXMvYXJ0aWNsZS1idXp6L2luZGV4&startPageQuery={encoded_query}'

        def clean_text(text):
            """Clean text by removing special characters and {xxx}(xxxx) patterns"""
            if text is None:
                return None
            # Remove special characters
            text = re.sub(r'[\x00-\x1F\x7F\u00A0\u1680\u180E\u2000-\u200B\u202F\u205F\u3000]', ' ', text)
            # Remove {xxx}(xxxx) patterns
            text = re.sub(r'\{[^}]*\}\([^)]*\)', '', text)
            return text

        citation = []
        content = []
        for post_json in data["data"]:
            clean_title = clean_text(post_json['title'])
            clean_content = clean_text(post_json['bodyTextOnly'])
            
            if len(clean_title) > 0:
                title = clean_title[:100] + ("..." if len(clean_title) > 100 else "")
            else:
                title = clean_content[:100] + ("..." if len(clean_content) > 100 else "")

            citation.append(
                CitationMeta(
                    id = post_json["id"],
                    url = generate_url(post_json["id"], post_json["title"]),
                    avatar = post_json["avatar"],
                    author = post_json["authorName"],
                    title = title
                )
            )
            # current latestReleaseTime is buggy, all 0 values
            if post_json["latestReleaseTime"] > 0:
                content.append({
                    "id": post_json["id"],
                    "create": datetime.fromtimestamp(post_json["latestReleaseTime"] / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                    "content": f"{clean_title} {clean_content}"
                })
            else:
                content.append({
                    "id": post_json["id"],
                    "content": f"{clean_title} {clean_content}"
                })

        return {
            "citation": citation,
            "content": content
        }

    @staticmethod
    def _filter_tweet_content(text: str) -> str:
        # filter http
        text = re.sub(r'https?://\S+', '', text)
        # 替换@用户（@后跟任意非空白字符）
        text = re.sub(r'@\S+', '', text)
        # 替换 HTML 实体（如 &amp;）
        text = re.sub(r'&\w+;', '', text)
        # 去除多余空白和换行
        text = re.sub(r'\s+', ' ', text).strip()
        return text

    @staticmethod
    def _post_process_tweets_data(data: dict) -> dict:
        """Process and transform tweets data structure"""
        def generate_tweet_url(handler, tweet_id):
            return f"https://x.com/{handler}/status/{tweet_id}"

        citation = []
        content = []
        for post_json in data["data"]:
            filter_content = DataNodeProcessor._filter_tweet_content(post_json['text'])
            if len(filter_content) > 10:
                citation.append(
                    CitationMeta(
                        id = post_json["id"],
                        url = generate_tweet_url(post_json["authorHandle"], post_json["id"]),
                        avatar = post_json["profilePic"],
                        author = post_json["authorHandle"],
                        title = post_json["text"][:100] + ("..." if len(post_json["text"]) > 100 else "")
                    )
                )
                content.append({
                    "id": post_json["id"],
                    #"create": datetime.fromtimestamp(post_json["createdAt"] / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                    "content": filter_content
                })

        return {
            "citation": citation,
            "content": content
        }
    @staticmethod
    def safe_nested_get(obj, *keys, default=""):
      if obj is None:
          return default
      for key in keys:
          if not isinstance(obj, dict):
              return default
          obj = obj.get(key)
          if obj is None:
              return default
      return obj or default

    @staticmethod
    def _post_process_basic_data(data: dict) -> dict:
        """Process basic data from the API response."""

        # return official x data if available
        official_x_data = None
        row_x_data = DataNodeProcessor.safe_nested_get(data, "data", "sentimentSummary", "officialXInfoList", default=[])
        if row_x_data:
            official_x_data = DataNodeProcessor._post_process_tweets_data({"data": row_x_data})

        # process project basic info
        basic_dict = DataNodeProcessor.safe_nested_get(data, "data", "fundamentalSummary", "cmcSymbolInfoV2", default={})
        project_info = {}
        # mapping for project info
        column_map = {
            "sb": "fullname",
            "ws": "website",
            "mc": "marketcap_usd",
            "cs": "circulating_supply",
            "ts": "total_supply",
            "athpu": "all_time_high_usd",
            "atlpu": "all_time_low_usd",
            "athd": "all_time_high_date",
            "ald": "all_time_low_date",
            "xhn": "x_author_name"
        }
        for k, v in column_map.items():
            ret = DataNodeProcessor.safe_nested_get(basic_dict, k, default="NA")
            if ret != "NA" and ret != None:
                if k in {"athd", "ald"}:
                    ret = datetime.strftime(datetime.fromtimestamp(int(ret/1000)), "%Y-%m-%d")
                project_info[v] = ret
        
        project_description = DataNodeProcessor.safe_nested_get(data, "data", "fundamentalSummary", "cmsSymbolDescription", default="") + "\n" + DataNodeProcessor.safe_nested_get(data, "data", "fundamentalSummary", "cmcMetadataInfo", "description", default="")
        if project_description != "\n":
            project_info["description"] = project_description

        tags = DataNodeProcessor.safe_nested_get(data, "data", "fundamentalSummary", "productItemInfo", "tags", default="")
        project_info["tags"] = tags

        return {
            "project_info": {
                "content": project_info
            },
            "official_x_data": official_x_data
        }

    @staticmethod
    def _convert_chart_data(input_data: list, output_columns: list) -> dict:
        """
        使用pandas将输入的数据格式转换为指定的输出格式
        
        Args:
            input_data: 包含多个数据源的列表，每个数据源包含title、labels和data
            output_columns: 需要输出的列名列表
        
        Returns:
            包含header和data的字典
        """
        # 第一步：将每个数据源转换为DataFrame
        dataframes = []
        
        for source in input_data:
            title = source["title"]
            labels = source["labels"]
            data = source["data"]
            
            # 创建DataFrame
            df = pd.DataFrame(data, columns=labels)
            
            # 添加title前缀到列名（除了timestamp, open_time, open_price）
            new_columns = {}
            for col in df.columns:
                if col in ["timestamp", "open_time", "close_time"]:
                    new_columns[col] = col
                else:
                    new_columns[col] = f"{title}/{col}"
            
            df = df.rename(columns=new_columns)
            dataframes.append(df)
        
        # 第二步：根据timestamp/open_time/close_time进行join
        join_keys = ["timestamp", "open_time", "close_time"]
                
        # 执行join操作
        result_df = dataframes[0]
        for df in dataframes[1:]:
            result_df = result_df.merge(df, on=join_keys, how="outer")
        
        # 第三步：按时间戳排序（优先使用timestamp，其次open_time）
        sort_key = "timestamp" if "timestamp" in join_keys else join_keys[0]
        result_df = result_df.sort_values(by=sort_key)
        result_df['timestamp'] = result_df['timestamp'].astype(str)
        
        # 第四步：选择需要的列，忽略不存在的列
        existing_columns = [col for col in output_columns if col in result_df.columns]
        selected_df = result_df[existing_columns]
        
        # 第五步：转换为目标格式
        return {
            "header": existing_columns,
            "data": selected_df.values.tolist()
        }

    @staticmethod
    def _post_process_charts_data(data: dict, live_time: int = None) -> dict:
        """Process and transform charts data structure"""
        processed = {
            ChartType.TECHNICALS.value: {},
            ChartType.UNLOCK.value: {},
            ChartType.MONEY_FLOW.value: {},
            "origin": data.get("data", {})
        }

        all_chart_data = DataNodeProcessor.safe_nested_get(data, "data", "charts", default=[])

        if len(all_chart_data) == 0:
            return processed
        
        for chart in all_chart_data:
            chart_type = chart["bizType"]
            # unlock data is only available for the 7 days after token open market
            if chart_type == "unlock" and live_time <= 86400000 * 7:
                processed[ChartType.UNLOCK.value] = chart["data"]
            elif chart_type == "technicals":
                # if end_time - open_market_time is less than 1d, we only output price and volumn data
                if live_time < 86400000:
                    processed[ChartType.TECHNICALS.value] = DataNodeProcessor._convert_chart_data(chart["data"], ["timestamp", "kline/close_price", "vol/vol_usdt"])
                else:
                    processed[ChartType.TECHNICALS.value] = DataNodeProcessor._convert_chart_data(chart["data"], ["timestamp", "kline/close_price", "vol/vol_usdt", "ema/ema_7","ema/ema_25","ema/ema_99","boll/boll_upper","boll/boll_mid","boll/boll_lower","macd/macd","macd/macd_signal","macd/macd_hist","rsi/rsi_6","rsi/rsi_12","rsi/rsi_24","atr/atr_14","stdev/stdev_5"])
            elif chart_type == "flow":
                processed[ChartType.MONEY_FLOW.value] = DataNodeProcessor._convert_chart_data(chart["data"], ["timestamp", "total_inflow/inflow_usdt", "large_inflow/inflow_usdt", "large_inflow/inflow_ratio", "concentration/concentration"])
            else:
                continue

        return processed