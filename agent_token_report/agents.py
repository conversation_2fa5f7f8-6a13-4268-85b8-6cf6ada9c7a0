"""Agent processing logic for news and technical analysis."""

from typing import Dict, Any, Optional
import re
from datetime import datetime
from langchain_core.prompts import ChatPromptTemplate
from langchain.output_parsers import PydanticOutputParser

from agent_token_report.common.data_processor import DataNodeProcessor
from agent_token_report.prompts.manager_prompt import get_default_prompt
from .models import DataType, ChartType, AnalysisReport

import vertexai
import os
from langchain_google_vertexai.chat_models import ChatVertexAI
from langchain_google_vertexai.model_garden import ChatAnthropicVertex
from langchain_openai import ChatOpenAI
from util.logger.logger_utils import setup_logger, log_exception_as_single_line
from json_repair import repair_json

logger = setup_logger(__name__)

vertexai.init(project=os.environ["GOOGLE_CLOUD_PROJECT"],location=os.environ["GOOGLE_CLOUD_LOCATION"])

class AgentDataProcessor:
    """Handles data extraction and processing for all agents."""
    
    @staticmethod
    def extract_news_data(fetched_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and combine news-related data from fetched data."""
        combined = {}
        for data_type in DataType:
            if data_type == DataType.CHARTS:
                continue
                
            # Use safe_nested_get to safely extract data
            status = DataNodeProcessor.safe_nested_get(fetched_data, data_type.value, "status", default="")
            content = DataNodeProcessor.safe_nested_get(fetched_data, data_type.value, "data", "content", default=[])
            
            if status == "success" and len(content) > 0:
                combined[data_type.value] = content
        return combined
    
    @staticmethod
    def extract_technical_data(fetched_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and combine technical data from fetched data."""
        combined = {}
        
        # Use safe_nested_get to safely extract chart data
        status = DataNodeProcessor.safe_nested_get(fetched_data, DataType.CHARTS.value, "status", default="")
        if status == "success":
            chart_map = DataNodeProcessor.safe_nested_get(fetched_data, DataType.CHARTS.value, "data", default={})
            for chart_type in ChartType:
                chart_data = DataNodeProcessor.safe_nested_get(chart_map, chart_type.value, default={})
                if chart_data:
                    combined[chart_type.value] = chart_data
        return combined
    
    @staticmethod
    def extract_all_data(fetched_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and combine all relevant data from fetched data."""
        combined = AgentDataProcessor.extract_news_data(fetched_data)
        combined.update(AgentDataProcessor.extract_technical_data(fetched_data))
        return combined

    @staticmethod
    def extract_news_and_technical_report(fetched_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and combine all relevant data from fetched data."""
        combined = {}
        for analysis_report in ["news_analysis", "technical_analysis"]:
            if fetched_data.get(analysis_report):
                combined[analysis_report] = fetched_data[analysis_report]
        return combined

class BaseAgent:
    """Base agent class with common functionality."""
    
    def __init__(self, parser: PydanticOutputParser, langfuse, cfg):
        self.parser = parser
        self.langfuse = langfuse
        self.cfg = cfg
    
    def _extract_data(self, fetched_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract relevant data for this agent. Must be implemented by subclasses."""
        raise NotImplementedError
    
    def _get_prompt_name(self) -> str:
        """Get the name of the prompt to load. Must be implemented by subclasses."""
        raise NotImplementedError

    def _get_default_model_config(self) -> dict:
        return {"model_type": "openai"}
    
    def _create_model(self, model_config):
            """Create an LLM instance with full parameter support.
            
            Args:
                model_config: Dictionary containing:
                    - model_type: Model type ('openai' or 'vertexai')
                    - other essential parameters for the model
            
            Returns:
                Configured LLM instance
            
            Raises:
                ValueError: If model type is unsupported
            """
            model_type = model_config.get('model_type', 'openai')
            
            if model_type == 'openai':
                return ChatOpenAI(
                    **model_config.get('model_params', {})
                )
            elif model_type == 'vertexai':
                return ChatVertexAI(
                    **model_config.get('model_params', {})
                )
            elif model_type == 'anthropic':
                return ChatAnthropicVertex(
                    project=os.environ.get("GOOGLE_CLOUD_PROJECT", ""),
                    location=os.environ.get("GOOGLE_CLOUD_LOCATION", ""),
                    **model_config.get('model_params', {})
                )
            raise ValueError(f"Unsupported model type: {model_type}")
    
    def process(self, token: str, timestamp: int, open_market_timestamp: int, fetched_data: Dict[str, Any], exp_params: Dict[str, Any] = None):
        """Standard processing pipeline for all agents."""
        errors = []
        try:
            version = DataNodeProcessor.safe_nested_get(exp_params, self._get_prompt_name(), default=None)
            if version:
                logger.info(f"Hit experiment: {self._get_prompt_name()}, version: {version}")

            combined_data = self._extract_data(fetched_data)
            if not combined_data:
                logger.warning(f"No data available for {self._get_prompt_name()} analysis")
                return None, errors
            combined_data["token"] = token
            combined_data["date"] = datetime.fromtimestamp(timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S')
            if open_market_timestamp != None and open_market_timestamp > 0:
                combined_data["open_market_date"] = datetime.fromtimestamp(open_market_timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S')
            
            # Load prompt and config
            try:
                langfuse_prompt = self.langfuse.get_prompt(self._get_prompt_name(), version=version, cache_ttl_seconds=0) if version else self.langfuse.get_prompt(self._get_prompt_name(), cache_ttl_seconds=0)
            except Exception as e:
                langfuse_prompt = get_default_prompt(self._get_prompt_name())
            prompt = ChatPromptTemplate.from_messages(langfuse_prompt.get_langchain_prompt())
            config = langfuse_prompt.config
            
            # Ensure all required prompt variables are present
            for var in prompt.input_variables:
                if var not in combined_data:
                    combined_data[var] = "[]"
                    logger.warning(f"Agent {self._get_prompt_name()} Missing prompt variable '{var}', using empty string")
            
            llm = self._create_model(config or self._get_default_model_config())
            chain = prompt | llm
            result = chain.invoke(combined_data)
            
            # Clean and parse the result
            content = result.content
            # fix issue, since sometimes gemini return multiple result, see: 0d677be2165f45999cbbaae4ec6009f1
            if isinstance(content, list):
                content = content[0] if len(content) > 0 else ""
            cleaned = re.sub(r"<think>.*?</think>", "", content, flags=re.DOTALL)
            
            try:
                parsed_result = self.parser.parse(cleaned)
            except Exception as e:
                logger.warning(f"Fix llm json format with repair_json")
                try_fix_cleaned = repair_json(cleaned)
                parsed_result = self.parser.parse(try_fix_cleaned)
            
            logger.info(f"{self._get_prompt_name()} analysis completed")
            return parsed_result, errors
            
        except Exception as e:
            error = log_exception_as_single_line(logger, e, f"Exception occurred in {self._get_prompt_name()}")
            errors.append(error)
            return None, errors

class NewsAgent(BaseAgent):
    """News analysis agent."""
    
    def _extract_data(self, fetched_data: Dict[str, Any]) -> Dict[str, Any]:
        return AgentDataProcessor.extract_news_data(fetched_data)
    
    def _get_prompt_name(self) -> str:
        return "news_agent"

class TechnicalAgent(BaseAgent):
    """Technical analysis agent."""
    
    def _extract_data(self, fetched_data: Dict[str, Any]) -> Dict[str, Any]:
        return AgentDataProcessor.extract_technical_data(fetched_data)
    
    def _get_prompt_name(self) -> str:
        return "technical_agent"

class SingleAgent(BaseAgent):
    """Combined agent that handles both news and technical analysis."""
    
    def _extract_data(self, fetched_data: Dict[str, Any]) -> Dict[str, Any]:
        return AgentDataProcessor.extract_all_data(fetched_data)
    
    def _get_default_model_config(self) -> dict:
        return {"model_type": "vertexai"}
    
    def _get_prompt_name(self) -> str:
        return "single_agent"

class ManagerAgent(BaseAgent):
    """Manager synthesis agent."""

    def _extract_data(self, fetched_data: Dict[str, Any]) -> Dict[str, Any]:
        return AgentDataProcessor.extract_news_and_technical_report(fetched_data)
    
    def _get_prompt_name(self) -> str:
        return "manager_agent"

class PostFilterAgent(BaseAgent):
    """Post filter agent"""
    def __init__(self, parser, langfuse, cfg, fetched_keys):
        super().__init__(parser, langfuse, cfg)
        self.fetched_keys = fetched_keys

    def _extract_data(self, fetched_data: Dict[str, Any]) -> Dict[str, Any]:
        combined_data = {}
        
        basic = DataNodeProcessor.safe_nested_get(fetched_data, "Project_Info", "data", "content", default={})
        # if basic info is missing
        if len(basic) == 0:
            return combined_data

        for fetched_key in self.fetched_keys:
            post_data = DataNodeProcessor.safe_nested_get(fetched_data, fetched_key, "data", default=None)
            content = DataNodeProcessor.safe_nested_get(post_data, "content", default=[])
            if post_data and len(content) > 0:
                #re_index
                new_content = []
                for idx, value in enumerate(content):
                    new_value = {}
                    new_value["id"] = idx
                    new_value["content"] = value["content"]
                    new_content.append(new_value)
                combined_data[fetched_key] = new_content
        
        # post is not empty and we have grounded basic info
        if len(basic) > 0 and len(combined_data) > 0:
            combined_data["basic"] = basic

        return combined_data
    
    def _get_prompt_name(self) -> str:
        return "post_filter_agent"