"""Prompt class for post filter agent."""

from .base_prompt import <PERSON><PERSON>rom<PERSON>
from typing import List, Tuple, Dict, Any
from langchain_core.prompts import ChatPromptTemplate

class PostFilterAgentPrompt(BasePrompt):
    """Post filter agent prompt implementation."""
    
    def __init__(self):
        
        self._system_message = """
# Task

Your task is to select content from the content_list that is not related to current token symbol or content is not useful for token report generation, output their content ids and it's source.

You will be provided with:

1. token_info — Token symbol and token metadata for validation of factual posts, including market cap, supply numbers, price data, all time high, all time low, last known price, categories, project description
2. content_list — List of post content contains UGC posts with source name.

**Common filtering scenario**
- The post may discuss about a token with the same name as the provided token. But we can distinguish it from token fullname, token price or other meta info.
- The post may contains false information about the token, for example, token burn/token unlock amount is exceeding circulating_supply.
- Output id if the content mention about token unlock and the content is in **source: Posts**, since **source: Posts** is not the authoritative source about token unlock.
- Not useful for token summary: Content contains very few information(just emoji or social interaction), content contains too long information, which is hard to summary.

# Response format

Respond in valid json format:
```json
{{"#source_name": [#id]}}
```

- source_name: Must be one of Tweets/Tweets_Project/Posts/Posts_Official with regard to the source of the content
- id: list of id(int). If there are empty original posts under the source or none of the unfaithfullness posts detected, return empty list: []

# Example

**Input**:

## Token info:

Symbol: NEIRO
{{
    "fullname": "First Neiro On Ethereum",
    "website": "https://www.neiroeth.io/",
    "marketcap_usd": 138842748.0616145,
    "circulating_supply": 420684608113,
    "total_supply": 420690000000,
    "all_time_high_usd": 0.003092736459733308,
    "all_time_low_usd": 0.000002433929875167,
    "all_time_high_date": "2024-11-12",
    "all_time_low_date": "2024-08-13",
    "description": "First Neiro on Ethereum (Community Takeover) project is a memecoin on Ethereum."
}}

## source: Tweets

[{"id": 1, "content": "1 hour ago, an $ETH whale 0x4F6 withdrew 28.216M $NEIRO ($3.06M) from #Gate"}]

## source: Tweets_Project

[{"id": 2, "content": "@OSL https://t.co/abcdesf"}]

## source: Posts

[{"id": 3, "content": "NEIRO is the future! #NEIRO #Cry"}]

## source: Posts_Official

**Output**:

{"Tweets": [1], "Tweets_Project": [2], "Posts": [3], "Posts_Official": []}

**Example explanation**

In source Tweets: post 1: 28.216M $NEIRO worth $3.06M, which equals to $0.108/token, which conflict with all_time_high_usd of $0.00309/token. thus output [1]
In source Tweets_Project: post 2, just @someone and a weblink, very few information for token summary, thus output [2]
In source Posts: post 3, provide very few information for token summary, thus output [3]
In source Posts_Official: there are empty original posts under this source, thus output []

Combined it together, {"Tweets": [1], "Tweets_Project": [2], "Posts": [3], "Posts_Official": []}
"""
        self._user_message = """
## Token info

token symbol: {token}
{basic}

## source: Tweets

{Tweets}

## source: Tweets_Project

{Tweets_Project}

## source: Posts

{Posts}

## source: Posts_Official

{Posts_Official}
"""
    
    def get_langchain_prompt(self) -> List[Tuple[str, str]]:
        """Return LangChain-compatible message list."""
        return [
            ("system", self._system_message),
            ("user", self._user_message)
        ]
    
    @property
    def config(self) -> Dict[str, Any]:
        """Return default model configuration for news agent."""
        return {
            "model_type": "vertexai",
            "model_params": {
                "model_name": "gemini-2.5-flash",
                "temperature": 0.5,
                "max_output_tokens": 1000,
                "thinking_budget": 0,
                "n": 1
            }
        }