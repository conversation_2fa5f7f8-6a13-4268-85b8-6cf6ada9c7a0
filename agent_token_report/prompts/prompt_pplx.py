"""Prompt class for news analysis agent."""

from .base_prompt import <PERSON><PERSON>rom<PERSON>
from typing import List, <PERSON><PERSON>, Dict, Any
from langchain_core.prompts import ChatPromptTemplate

class PplxPrompt(BasePrompt):
    """News analysis agent prompt implementation."""
    
    def __init__(self):

        self._user_message = """
What's the recent news about crpyto token {{token}}({{fullname}})? Project official site: {{website}}.
- Please ignore price prediction or technical analysis content. Focus on project related content or narratives.
- Don't use your knowledge to solve the question, always search online first and then answer the question.
- Strictly follow online search result to answer.
- Output "NA" if no news founded.
"""
    
    def get_langchain_prompt(self) -> List[Tuple[str, str]]:
        """Return LangChain-compatible message list."""
        return [
            ("user", self._user_message)
        ]
    
    def compile(self, **kwargs):
        prompt_template = ChatPromptTemplate(self.get_langchain_prompt())
        prompt_value = prompt_template.invoke(input=kwargs)
        return [{
          "role": "user",
          "content": prompt_value.to_messages()[0].content
        }]

    @property
    def config(self) -> Dict[str, Any]:
        """Return default model configuration for news agent."""
        return {
          "pplx_params": {
              "search_mode": "web",
              "reasoning_effort": "medium",
              "temperature": 0.2,
              "top_p": 0.9,
              "return_images": False,
              "return_related_questions": False,
              "top_k": 0,
              "stream": False,
              "presence_penalty": 0,
              "frequency_penalty": 0,
              "web_search_options": {
                  "search_context_size": "medium"
              },
              "model": "sonar",
              "search_domain_filter": [
                  "coindesk.com",
                  "cointelegraph.com",
                  "u.today",
                  "coingape.com",
                  "ambcrypto.com",
                  "cryptonews.com",
                  "bitcoinist.com",
                  "dailyhodl.com",
                  "beincrypto.com",
                  "decrypt.co"
              ]
          },
          "x_params": {
              "authorIds": "1499656309565657089,1462727797135216641,1503362006191026180,1039833297751302144,1558634109890875392,1800705784789307392",
              "period_count": [
                  {
                      "peroid": 1,
                      "count": 15
                  },
                  {
                      "peroid": 7,
                      "count": 10
                  }
              ]
          },
          "post_params": {
              "period_count": [
                  {
                      "peroid": 1,
                      "count": 20
                  },
                  {
                      "peroid": 7,
                      "count": 10
                  }
              ]
          },
          "official_post_params": {
              "period_count": [
                  {
                      "peroid": 1,
                      "count": 4
                  }
              ]
          }
      }