from .base_prompt import BasePrompt
from .prompt_single_agent import SingleAgentPrompt
from .prompt_news_agent import NewsAgentPrompt
from .prompt_technical_agent import TechnicalAgentPrompt
from .prompt_manager_agent import ManagerAgentPrompt
from .prompt_pplx import PplxPrompt
from .prompt_post_filter_agent import PostFilterAgentPrompt
from typing import Optional

def get_default_prompt(prompt_name: str) -> Optional[BasePrompt]:
    """Get default prompt instance based on prompt name.
    
    Args:
        prompt_name: Name of the prompt (e.g., 'single_agent', 'news_agent')
        
    Returns:
        Prompt instance compatible with Langfuse interface, or None if not found
    """
    if prompt_name == "single_agent":
        return SingleAgentPrompt()
    elif prompt_name == "news_agent":
        return NewsAgentPrompt()
    elif prompt_name == "technical_agent":
        return TechnicalAgentPrompt()
    elif prompt_name == "manager_agent":
        return ManagerAgentPrompt()
    elif prompt_name == "pplx_prompt":
        return PplxPrompt()
    elif prompt_name == "post_filter_agent":
        return PostFilterAgentPrompt()
    return None