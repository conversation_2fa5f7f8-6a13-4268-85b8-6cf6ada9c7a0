"""
Main application entry point and usage example.
"""
import json

from dotenv import load_dotenv
load_dotenv()

from .multi_agent import LangGraphMultiAgentSystem

def main():    
    # Initialize system
    system = LangGraphMultiAgentSystem()
    
    # using mock data
    #results = system.run(token = "ADA", end_timestamp_ms=1751936400000, mode="single-agent", mock_data=True)
    #results = system.run(token = "ADA", end_timestamp_ms=1751936400000, mode="multi-agent", mock_data=True)
    
    # using real data from conan forward
    #results = system.run(token = "ADA", end_timestamp_ms=1753197301209, mode="single-agent", conan_key="")
    #results = system.run(token = "ADA", end_timestamp_ms=1751936400000, mode="multi-agent", conan_key="")

    # using real data from direct api
    #results = system.run(token = "ADA", end_timestamp_ms=1751936400000, mode="single-agent")
    #results = system.run(token = "ADA", end_timestamp_ms=1751936400000, mode="multi-agent")

    # abtest
    results = system.run(token = "BNB", end_timestamp_ms=1757043002361, mode="single-agent", mock_data=True, exp_params='{"manager_agent": 2, "mode": "multi-agent"}')

    
    print(json.dumps(results.get("report", {}), indent = 2, ensure_ascii=False))
    print(json.dumps(results.get("citation", {}), indent = 2, ensure_ascii=False))
    
if __name__ == "__main__":
    main()