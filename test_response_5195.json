{"data": {"created_at": 1757501488, "elapsed_time": 39.991039080545306, "error": null, "finished_at": 1757501528, "id": "193bd524-58b8-4681-a9a0-03593f54e583", "outputs": {"c_id": "1965729834447114370", "message": "Solana’s (SOL) tokenomics governs its native cryptocurrency, SOL, and is designed to balance network security, growth, and sustainability. Below is a detailed overview based on the latest available information:\n\n### **Key Components of SOL Tokenomics**\n\n1. **Total and Circulating Supply**:\n   - **Total Supply**: As of April 2025, Solana’s total supply is approximately 598.58 million SOL, with no hard cap due to its inflationary model.[](https://www.okx.com/learn/solana-tokenomics-2025-unlocks-inflation)\n   - **Circulating Supply**: Around 516.28 million SOL (86.3% of total supply) is in circulation, with the remaining 13.7% (82.3 million SOL) locked and subject to vesting schedules.[](https://www.okx.com/learn/solana-tokenomics-2025-unlocks-inflation)\n   - **Genesis Block**: At launch in March 2020, 500 million SOL were created. The supply has since increased due to inflation but is reduced by token burns.[](https://veridelisi.substack.com/p/solana-tokenomics-and-coin-creation)\n\n2. **Inflation Model**:\n   - Solana employs a **disinflationary model** with an initial annual inflation rate of 8% starting in February 2021, decreasing by 15% annually until it stabilizes at a long-term rate of 1.5%.[](https://tr.okx.com/en/learn/solana-tokenomics-2025-unlocks-inflation)[](https://veridelisi.substack.com/p/solana-tokenomics-and-coin-creation)\n   - As of 2025, the current inflation rate is approximately 4.306% per epoch-year (around 180 epochs, varying from 2.5–3.5 days depending on network performance).[](https://solanacompass.com/tokenomics)\n   - Inflation rewards are primarily distributed to validators and delegators to incentivize staking and secure the network.\n\n3. **Token Burning Mechanism**:\n   - **50% of transaction fees** (base, vote, and priority fees) are burned, introducing a deflationary element to offset inflation and create scarcity. The remaining 50% goes to validators as block rewards.[](https://tr.okx.com/en/learn/solana-tokenomics-2025-unlocks-inflation)[](https://veridelisi.substack.com/p/solana-tokenomics-and-coin-creation)\n   - This burn mechanism aims to stabilize the token supply and potentially increase SOL’s value over time as network activity grows.\n\n4. **Token Allocation and Distribution**:\n   - Solana’s initial token distribution was structured as follows:\n     - **Seed Sale**: 16.23% (initial investors)\n     - **Founding Sale**: 12.92% (founding entities)\n     - **Team**: 12.79%\n     - **Solana Foundation**: 10.46%\n     - **Validator Sale**: 5.07%\n     - **Strategic Sale**: 1.84%\n     - **Other**: Remaining tokens were allocated to community, inflation, grants, and public/private sales.[](https://coinmarketcap.com/currencies/solana/)[](https://coincodecap.com/solana-sol-tokenomics)\n   - Approximately 10.74% of the total supply is tied to FTX and Alameda Research, subject to bankruptcy proceedings, with scheduled unlocks in 2025 (e.g., 472,990 SOL in May 2025 and 11.2 million SOL later in the year). These unlocks could impact market dynamics.[](https://tr.okx.com/en/learn/solana-tokenomics-2025-unlocks-inflation)\n\n5. **Vesting and Unlock Schedules**:\n   - Solana uses **cliff vesting** for many allocations, where tokens are released all at once after a set period, and **linear vesting** for gradual releases to minimize market disruptions.[](https://tokenomist.ai/solana)[](https://tokenomist.ai/)\n   - Locked SOL is typically held in stake accounts, often resulting from investments or Solana Foundation grants, with specific unlock dates. Once unlocked, staked SOL can be delegated or undelegated, with a 2–3 day deactivation “cool-down” period.[](https://solanacompass.com/tokenomics)\n   - Vesting schedules align stakeholder incentives with long-term network growth, preventing rapid sell-offs.[](https://tr.okx.com/en/learn/solana-tokenomics-2025-unlocks-inflation)\n\n6. **Staking and Incentives**:\n   - **Staking**: SOL holders can stake tokens to secure the network and earn rewards based on the inflation rate, validator performance, and network activity. There’s no minimum staking amount, but validators incur voting fees (around 1 SOL per day, roughly $70,000 annually).[](https://coincodecap.com/solana-sol-tokenomics)\n   - **Purpose**: Staking incentivizes network participation, with rewards distributed to validators and delegators. Non-stakers indirectly “pay” stakers through inflation, encouraging delegation to secure the network.[](https://solanacompass.com/tokenomics)\n   - **Delegation Program**: The Solana Foundation delegates a large portion of its stake to over 2,000 validators to promote decentralization.[](https://solanacompass.com/tokenomics)\n\n7. **Token Utility**:\n   - **Transaction Fees**: SOL is used to pay for transaction fees and smart contract executions on the network.\n   - **Governance**: SOL will eventually be used for governance voting, though specific mechanisms are still under development.[](https://tokeninsight.com/en/coins/solana/tokenomics)\n   - **Ecosystem Support**: SOL powers decentralized applications (dApps), DeFi protocols, NFT marketplaces, and other projects on Solana’s high-throughput blockchain.\n\n8. **Proposed Changes**:\n   - **Multicoin Capital’s SIMD-0228 (2025)**: Suggests a dynamic, variable-rate token emission model to adjust issuance based on staking participation (targeting 50% participation). If staking falls below 50%, issuance increases to attract stakers; if above, it’s capped to limit dilution. This aims to balance security and token value stability.[](https://coinpaper.com/6955/solana-tokenomics-overhaul-suggested-by-multicoin-capital)\n   - **SIMD-0096 Controversy (2024)**: A proposal to eliminate the 50% fee-burning mechanism for priority fees and redirect them to validators was approved but not yet implemented, raising concerns about increased inflation and dilution for non-stakers.[](https://coinpaper.com/6955/solana-tokenomics-overhaul-suggested-by-multicoin-capital)\n\n### **Market Dynamics and Risks**:\n- **Unlock Risks**: Large token unlocks, especially FTX-related ones, could introduce volatility. For example, 11.2 million SOL unlocks in 2025 could affect price stability depending on whether recipients hold or sell.[](https://tr.okx.com/en/learn/solana-tokenomics-2025-unlocks-inflation)\n- **Inflation Concerns**: The uncapped supply and current 4.3% inflation rate (compared to Ethereum’s ~0.5%) may dilute non-stakers’ holdings over time, though the burn mechanism and reducing inflation rate mitigate this.[](https://crypto.com/us/university/solana-tokenomics)[](https://crypto.com/en/university/solana-tokenomics)\n- **Validator Costs**: High validator voting fees ($70,000/year) may limit validator diversity, though nearly 1,000 validators are active.[](https://coincodecap.com/solana-sol-tokenomics)\n- **Market Sentiment**: Historical data shows medium volatility 7 days post-unlock, with price impact depending on recipients and market conditions.[](https://tokenomist.ai/solana)\n\n### **Summary**:\nSolana’s tokenomics is designed for scalability, security, and ecosystem growth, with a disinflationary model (8% to 1.5%), a 50% transaction fee burn, and vesting schedules to control supply. Staking rewards incentivize network participation, while the burn mechanism introduces deflationary pressure. However, challenges like FTX-related unlocks, high validator costs, and inflation concerns persist. The proposed dynamic emission model (SIMD-0228) could further refine this balance, making Solana’s tokenomics more adaptive to network needs.\n\nFor further details on vesting schedules or real-time data, check platforms like [Tokenomist.ai](https://tokenomist.ai) or [SolanaCompass.com](https://solanacompass.com).[](https://tokenomist.ai/solana)[](https://solanacompass.com/projects/category/governance/tokenomics)"}, "status": "succeeded", "total_steps": 6, "total_tokens": 0, "workflow_id": "352a907d-459d-41a1-b990-01617e40ddba"}, "task_id": "3075569b-5dca-4366-b6b2-7f005b87b583", "workflow_run_id": "193bd524-58b8-4681-a9a0-03593f54e583"}