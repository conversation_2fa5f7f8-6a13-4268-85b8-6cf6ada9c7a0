import asyncio
import base64
import contextlib
import datetime
import json
import logging
import os
import re
import uuid
from pathlib import Path
from typing import Dict, Set, Any
from google.genai import types

from dotenv import load_dotenv
from fastapi import FastAPI, WebSocket
from fastapi import status
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles
from google.adk import Agent
from google.adk.agents.callback_context import CallbackContext
from google.adk.agents.run_config import RunConfig, StreamingMode
from google.genai.types import (
    Part,
    Content,
)
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from starlette.websockets import WebSocketDisconnect, WebSocketState

from agent_asset.agent import create_asset_agent
from common.enum.business_enum import WebSocketBusinessType
from conf import model_config, prompt
from conf.config import Config
from util import web_utils
from util.jwt_utils import validate_token, encode
from util.logger.log_context import set_trace_id, set_session_id, set_user_id
from util.logger.logger_utils import setup_logger
from util.logger.middleware import TraceIdMiddleware
from util.sequential_agents import create_sequential_agent_runner
from util.shared_config import MAX_CONCURRENT_SESSIONS, APP_NAME
from util.tools.unified_session_service import unified_session_service, start_periodic_cleanup, stop_periodic_cleanup, lifespan
from util.tools.memory import set_user_context, clear_session_context, set_session_context, update_session_last_accessed
from util.tools.root import create_traced_agent_tool
from util.tools.session import agent_to_client_messaging, client_to_agent_messaging_for_ws_biz
from util.tools.language_map import map_iso_to_language
import agent_convert.convert
from agent_convert.agent import create_trading_agent
from google.adk import Runner
from util.tools.apollo import apollo_helper
from endpoints.ai_report import ai_report
from agent_YMAL.gen_query_service import generate_suggestions
from agent_YMAL.reply_service import route_question

#
# Setup logging
#
# Create logs directory if it doesn't exist
os.makedirs("logs", exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/agent_trace_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)



# Configure Google ADK logging - Enable detailed debugging for agent events and sessions
logging.getLogger("google_adk").setLevel(logging.INFO)  # Enable INFO level for general ADK
logging.getLogger("google_adk.google.adk.models.google_llm").setLevel(logging.WARNING)  # Keep model calls quiet
logging.getLogger("google_adk.google.adk.agents").setLevel(logging.INFO)  # Enable agent debugging
logging.getLogger("google_adk.google.adk.sessions").setLevel(logging.INFO)  # Enable session debugging
logging.getLogger("google_adk.google.adk.memory").setLevel(logging.INFO)  # Enable memory debugging
logging.getLogger("google_adk.google.adk.artifacts").setLevel(logging.INFO)  # Enable artifact debugging
logging.getLogger("google_adk.google.adk.events").setLevel(logging.INFO)  # Enable event debugging
logging.getLogger("google_adk.google.adk.telemetry").setLevel(logging.INFO)  # Keep telemetry quiet
logging.getLogger("google_adk.google.adk.tools").setLevel(logging.INFO)  # Enable tool debugging

# Configure bdp-search-agent application logging - Enable all application components
logging.getLogger("bdp-search-agent").setLevel(logging.DEBUG)  # Enable all bdp-search-agent namespace logs

# Configure A2A Agent logging - Enable A2A agent debugging
logging.getLogger("a2a_agent").setLevel(logging.WARNING)  # Enable A2A agent debugging
logging.getLogger("rag_root_agent_executor_streaming").setLevel(logging.WARNING)  # Enable streaming executor debugging
logging.getLogger("rag_root_agent_executor_simple").setLevel(logging.WARNING)  # Enable simple executor debugging
logging.getLogger("rag_root_agent").setLevel(logging.WARNING)  # Enable root agent debugging

# Configure A2A Server telemetry logging suppression
logging.getLogger("a2a.utils.telemetry").setLevel(logging.ERROR)  # Suppress A2A telemetry logs
logging.getLogger("a2a.server").setLevel(logging.WARNING)  # Reduce A2A server verbosity
logging.getLogger("a2a-python-sdk").setLevel(logging.ERROR)  # Suppress A2A SDK telemetry logs

# Configure specific A2A server request handlers logging
logging.getLogger("a2a.server.request_handlers").setLevel(logging.ERROR)  # Suppress request handler traces
logging.getLogger("a2a.server.request_handlers.default_request_handler").setLevel(logging.ERROR)  # Suppress DefaultRequestHandler traces

# Configure A2A server events system logging
logging.getLogger("a2a.server.events").setLevel(logging.ERROR)  # Suppress event system traces
logging.getLogger("a2a.server.events.event_queue").setLevel(logging.ERROR)  # Suppress event queue traces
logging.getLogger("a2a.server.events.event_consumer").setLevel(logging.ERROR)  # Suppress event consumer traces

# Custom filter to suppress the specific ClientSession shutdown warning
class ClientSessionWarningFilter(logging.Filter):
    def filter(self, record):
        # Suppress the specific warning about ClientSession missing shutdown method
        if (record.levelno == logging.WARNING and 
            "'ClientSession' object has no attribute 'shutdown'" in record.getMessage()):
            return False
        return True

# Apply the filter to the google_adk.tools.mcp_tool.mcp_toolset logger
mcp_toolset_logger = logging.getLogger("google_adk.google.adk.tools.mcp_tool.mcp_toolset")
mcp_toolset_logger.addFilter(ClientSessionWarningFilter())

logger = setup_logger(__name__)
# Debug mode configuration - set to True to enable enhanced debugging
DEBUG_MODE = apollo_helper.get_apollo_values("debug_mode") == "true"

if DEBUG_MODE:
    logger.info("DEBUG MODE ENABLED - Enhanced agent debugging active")
    # Enable even more detailed debugging for agent events
    logging.getLogger("google_adk.google.adk.agents").setLevel(logging.DEBUG)
    logging.getLogger("google_adk.google.adk.sessions").setLevel(logging.DEBUG)
    logging.getLogger("google_adk.google.adk.events").setLevel(logging.DEBUG)
    logging.getLogger("google_adk.google.adk.tools").setLevel(logging.DEBUG)

    # Enable detailed debugging for our custom components
    logging.getLogger("agent_tracer").setLevel(logging.DEBUG)
    logging.getLogger("bdp-search-agent.util.sequential_agents").setLevel(logging.DEBUG)
    logging.getLogger("bdp-search-agent.rag_agent").setLevel(logging.DEBUG)
    
    # Enable enhanced A2A agent debugging in DEBUG mode
    logging.getLogger("a2a_agent").setLevel(logging.DEBUG)
    logging.getLogger("rag_root_agent_executor_streaming").setLevel(logging.DEBUG)
else:
    logger.info("Standard logging mode - set DEBUG_MODE=true for enhanced debugging")

# Session Management - now using unified session service
# Old session management variables removed - now handled by unified_session_service

#
# ADK Streaming
#

# Load Gemini API Key
load_dotenv()

# APP_NAME now imported from shared_config
# Use unified session service instead of LimitedHistorySessionService
# unified_session_service is already initialized as a global instance
# No additional initialization needed

# RAG agent is created fresh for each session to ensure updated date values



# Load langfuse module
cfg = Config()
LANGFUSE_ENABLED = cfg.get("langfuse_enabled", False)
LANGFUSE_OTEL_ENDPOINT = cfg.get("langfuse_otel_endpoint")
LANGFUSE_PK = cfg.get("langfuse_public_key")
LANGFUSE_SK = cfg.get("langfuse_secret_key")

from opentelemetry import trace

if LANGFUSE_ENABLED and LANGFUSE_OTEL_ENDPOINT and LANGFUSE_PK and LANGFUSE_SK:
    logger.info("Langfuse is enabled - initializing Langfuse tracing")
    LANGFUSE_AUTH = base64.b64encode(f"{LANGFUSE_PK}:{LANGFUSE_SK}".encode()).decode()

    os.environ["OTEL_EXPORTER_OTLP_ENDPOINT"] = LANGFUSE_OTEL_ENDPOINT
    os.environ["OTEL_EXPORTER_OTLP_HEADERS"] = f"Authorization=Basic {LANGFUSE_AUTH}"

    # Setup langfuse env
    os.environ["LANGFUSE_PUBLIC_KEY"] = LANGFUSE_PK
    os.environ["LANGFUSE_SECRET_KEY"] = LANGFUSE_SK
    os.environ["LANGFUSE_HOST"] = LANGFUSE_OTEL_ENDPOINT.split("api")[0]

    logger.info(f"configured OTEL endpoint {LANGFUSE_OTEL_ENDPOINT}")
    # initialise OTel with proper error handling
    try:
        provider = TracerProvider(resource=Resource.create({"service.name": APP_NAME}))
        exporter = OTLPSpanExporter()

        # Create A2A filtering span processor to block A2A spans at OpenTelemetry level
        from opentelemetry.sdk.trace.export import SpanProcessor

        class A2AFilteringSpanProcessor(SpanProcessor):
            def __init__(self, wrapped_processor):
                self.wrapped_processor = wrapped_processor

            def on_start(self, span, parent_context=None):
                # Block spans from a2a-python-sdk
                if (hasattr(span, 'instrumentation_scope') and
                    span.instrumentation_scope and
                    span.instrumentation_scope.name == "a2a-python-sdk"):
                    # logger.debug(f"Blocking A2A span: {span.name}")
                    return
                self.wrapped_processor.on_start(span, parent_context)

            def on_end(self, span):
                # Block spans from a2a-python-sdk
                if (hasattr(span, 'instrumentation_scope') and
                    span.instrumentation_scope and
                    span.instrumentation_scope.name == "a2a-python-sdk"):
                    return
                self.wrapped_processor.on_end(span)

            def shutdown(self):
                return self.wrapped_processor.shutdown()

            def force_flush(self, timeout_millis=30000):
                return self.wrapped_processor.force_flush(timeout_millis)

        # Wrap the batch processor with A2A filtering
        original_processor = BatchSpanProcessor(exporter)
        filtered_processor = A2AFilteringSpanProcessor(original_processor)
        provider.add_span_processor(filtered_processor)
        trace.set_tracer_provider(provider)
        tracer = trace.get_tracer("app")
        logger.info("Langfuse tracing initialized successfully with A2A span filtering")
    except Exception as e:
        logger.error(f"Failed to initialize Langfuse tracing: {e}")
        # Disable tracing if initialization fails
        LANGFUSE_ENABLED = False
elif not LANGFUSE_ENABLED:
    logger.info("Langfuse is disabled in configuration - skipping Langfuse initialization")
else:
    logger.warning("Langfuse is enabled but missing required configuration (endpoint, public_key, or secret_key) - skipping Langfuse initialization")

# Initialize Langfuse client and GoogleADK instrumentation
from langfuse import get_client
from langfuse import Langfuse

# Filter out A2A telemetry spans using blocked_instrumentation_scopes
langfuse = Langfuse(
    blocked_instrumentation_scopes=["a2a-python-sdk"]
)

# Verify connection
if langfuse.auth_check():
    logger.info("Langfuse client is authenticated and ready!")
else:
    logger.warning("Langfuse authentication failed. Please check your credentials and host.")

# Setup GoogleADK instrumentation for tracing
from openinference.instrumentation.google_adk import GoogleADKInstrumentor
GoogleADKInstrumentor().instrument()
logger.info("GoogleADK instrumentation initialized successfully")

# Add a global exception handler for OpenTelemetry context errors
import sys
import traceback

def handle_opentelemetry_context_error(exc_type, exc_value, exc_traceback):
    """Handle OpenTelemetry context errors gracefully"""
    error_str = str(exc_value)
    
    # Handle OpenTelemetry context errors
    if exc_type == ValueError and "was created in a different Context" in error_str:
        logger.warning("OpenTelemetry context error detected and handled gracefully")
        return
    # Handle task context cancel scope errors
    elif exc_type == RuntimeError and "Attempted to exit cancel scope in a different task" in error_str:
        logger.warning("Task context error detected and handled gracefully")
        return
    # Handle BaseExceptionGroup with cancel scope errors
    elif exc_type == BaseExceptionGroup and any("cancel scope" in str(e) for e in exc_value.exceptions):
        logger.warning("Task group cancel scope error detected and handled gracefully")
        return
    # Handle GeneratorExit errors that can occur during context cleanup
    elif exc_type == GeneratorExit:
        logger.warning("GeneratorExit during context cleanup - handled gracefully")
        return
    # Handle anyio TaskGroup errors
    elif "TaskGroup" in error_str and "cancel scope" in error_str:
        logger.warning("AnyIO TaskGroup cancel scope error detected and handled gracefully")
        return
    # Handle MCP client errors
    elif "mcp/client" in str(exc_traceback) and ("cancel scope" in error_str or "Context" in error_str):
        logger.warning("MCP client context error detected and handled gracefully")
        return
    
    # For all other exceptions, use the default handler
    sys.__excepthook__(exc_type, exc_value, exc_traceback)

# Set the custom exception handler
sys.excepthook = handle_opentelemetry_context_error

# Init token report agent
from agent_token_report import multi_agent
token_report_agent =multi_agent.LangGraphMultiAgentSystem()
os.environ["OPENAI_API_KEY"] = "DUMMY_KEY"

# Init A2A Agent Service
import sys
sys.path.append('./a2a_agent')
from AgentService import AgentServiceApp
a2a_service = AgentServiceApp()
a2a_app = a2a_service.get_app()

from util.tools.memory import set_current_session_id, set_session_context, set_user_context
from util.tools.memory import get_user_context

async def setup_session_context(session_id: str, user_id: str, user_language: str, query: str):
    """Setup session context and ADK session service without creating agents"""
    logger.info(f"[DEBUG] setup_session_context called for session: {session_id}")
    if not await unified_session_service.acquire_session(session_id):
        raise Exception("RESOURCE_EXHAUSTED: Maximum concurrent sessions exceeded. Please try again later.")
    logger.info(f"[DEBUG] Session slot acquired for: {session_id}")
    try:
        # Set up local session context first
        set_session_context(session_id, user_id=user_id, language=user_language, query=query)
        logger.info(f"[DEBUG] Local session context set for: {session_id}")
        # Create ADK session service session
        await unified_session_service.create_session(
            app_name=APP_NAME,
            user_id=user_id,
            session_id=session_id
        )
        logger.info(f"[DEBUG] ADK session service session created for: {session_id}")
        set_current_session_id(session_id)
        logger.info(f"[DEBUG] Current session ID set for : {session_id}")
    except Exception as e:
        logger.error(f"[DEBUG] Error in setup_session_context for {session_id}: {str(e)}")
        # Release session slot on error to prevent session slot leaks
        await unified_session_service.release_session(session_id)
        raise

async def start_agent_session_for_ws_biz(session_id: str, ws_biz_type: WebSocketBusinessType):
    """Dispatch to the proper session starter based on business type, and return (runner, run_config)."""
    logger.info(f"[DEBUG] start_agent_session called: session={session_id}, biz={ws_biz_type}")
    if ws_biz_type is WebSocketBusinessType.AI_REPORT:
        return await ai_report.start_agent_session(session_id)
    return await start_agent_session(session_id)

async def start_agent_session(session_id: str):
    """Starts a sequential agent session with proper session management"""
    logger.info(f"[DEBUG] start_agent_session called for session: {session_id}")

    # Try to acquire a session slot using unified session service
    logger.info(f"[DEBUG] Attempting to acquire session slot for: {session_id}")
    if not await unified_session_service.acquire_session(session_id):
        raise Exception("RESOURCE_EXHAUSTED: Maximum concurrent sessions exceeded. Please try again later.")
    logger.info(f"[DEBUG] Session slot acquired for: {session_id}")

    try:
        # Set session context for agents
        logger.info(f"[DEBUG] Setting session context for: {session_id}")
        # Get user context to pass to set_session_context
        user_id, user_language = get_user_context(session_id)
        set_session_context(session_id, user_id=user_id, language=user_language)
        logger.info(f"[DEBUG] Session context set for: {session_id}")

        # Create a Session using unified session service
        logger.info(f"[DEBUG] Creating session service session for: {session_id}")
        await unified_session_service.create_session(app_name=APP_NAME, user_id=user_id, session_id=session_id)
        logger.info(f"[DEBUG] Session service session created for: {session_id}")

        # Set the current session ID for agent tools
        set_current_session_id(session_id)
        logger.info(f"[DEBUG] Current session ID set for agent tools: {session_id}")

        # Create a fresh RAG agent with updated date values for time-limited search
        logger.info(f"[DEBUG] Creating RAG agent for: {session_id}")
        # use Kuanhao's RAG agent
        #rag_agent = create_rag_coordinator_agent()
        # Alpha agent removed per user request
        logger.info(f"[DEBUG] RAG agent setup completed for: {session_id}")
        
        # logger.info(f"[DEBUG] Creating traced RAG agent tool for: {session_id}")
        # alpha_agent_tool = create_traced_agent_tool("AlphaAgent", alpha_agent)
        # logger.info(f"[DEBUG] Traced RAG agent tool created for: {session_id}")

        # Create fresh trading and asset agents for each session AFTER session context is set
        logger.info(f"[DEBUG] Creating trading agent for: {session_id}")
        trading_agent = await create_trading_agent()
        logger.info(f"[DEBUG] Trading agent created for: {session_id}")
        
        # logger.info(f"[DEBUG] Creating traced trading agent tool for: {session_id}")
        # trading_agent_tool = create_traced_agent_tool("TradingAgent", trading_agent)
        # logger.info(f"[DEBUG] Traced trading agent tool created for: {session_id}")

        logger.info(f"[DEBUG] Creating asset agent for: {session_id}")
        asset_agent = await create_asset_agent()
        logger.info(f"[DEBUG] Asset agent created for: {session_id}")
        
        # logger.info(f"[DEBUG] Creating traced asset agent tool for: {session_id}")
        # asset_agent_tool = create_traced_agent_tool("AssetAgent", asset_agent)
        # logger.info(f"[DEBUG] Traced asset agent tool created for: {session_id}")

        
        def session_aware_callback(callback_context: CallbackContext) -> None:
            """Session-aware callback that loads user profile for this specific session"""
            logger.info(f"[DEBUG CALLBACK] Session-aware callback called for session: {session_id}")
            try:
                # Set the current session ID in thread-local storage for agent tools
                from util.tools.memory import set_current_session_id, get_user_context
                set_current_session_id(session_id)
                
                # Get the user context for this specific session
                user_id, user_language = get_user_context(session_id)
                logger.info(f"[DEBUG CALLBACK] Retrieved user context - ID: {user_id}, Language: {user_language}, Session: {session_id}")
                
                # Set both user_id and language in the callback context state
                if user_id:
                    callback_context.state["user_id"] = user_id
                    logger.info(f"[CALLBACK] User ID set in callback_context.state: {user_id}")
                else:
                    logger.warning(f"[CALLBACK] No user_id found for session {session_id}")
                    logger.warning(f"[CALLBACK] WARNING: No user_id found for session {session_id}")
                
                callback_context.state["user_language"] = user_language or "english"
                callback_context.state["session_id"] = session_id  # Store session_id for agent tools
                
                # Log all state values for debugging
                logger.info(f"[CALLBACK] callback_context.state 已设置:")
                logger.info(f"           user_id: {callback_context.state.get('user_id')}")
                logger.info(f"           user_language: {callback_context.state.get('user_language')}")
                logger.info(f"           session_id: {callback_context.state.get('session_id')}")
                
                logger.info(f"[DEBUG CALLBACK] Set callback context state - user_id: {callback_context.state.get('user_id')}, user_language: {callback_context.state.get('user_language')}, session_id: {callback_context.state.get('session_id')}")
                logger.info(f"[User Profile] Loaded user profile - ID: {user_id}, Language: {user_language or 'english'}, Session: {session_id}")
                logger.info(f"[User Profile] User context set - ID: {user_id}, Language: {user_language or 'english'}")
                
            except Exception as e:
                logger.error(f"[User Profile Error] Failed to load user profile for session {session_id}: {str(e)}")
                logger.error(f"[DEBUG CALLBACK] Exception in callback: {e}")
                import traceback
                logger.error(f"[DEBUG CALLBACK] Callback traceback: {traceback.format_exc()}")
                # Set default values on error
                callback_context.state["user_id"] = None
                callback_context.state["user_language"] = "english"
                callback_context.state["session_id"] = session_id  # Still store session_id even on error
                # Still set the session ID in thread-local storage even on error
                from util.tools.memory import set_current_session_id
                set_current_session_id(session_id)
                logger.info(f"[DEBUG CALLBACK] Set default values - user_language: english")
            
            return None
        logger.info(f"[DEBUG] Session-aware callback created for: {session_id}")
        
        # Resolve the root agent instruction with user language
        def resolve_root_agent_instruction(session_id: str) -> str:
            """Resolve the user_language placeholder in the root agent instruction template"""
            try:
                from util.tools.memory import get_user_context
                user_id, user_language = get_user_context(session_id)
                
                return prompt.ROOT_AGENT_INSTR.replace("{user_language}", user_language)
            except Exception as e:
                logger.warning(f"[DEBUG] Failed to resolve user language for root agent: {e}, using default 'English'")
                return prompt.ROOT_AGENT_INSTR.replace("{user_language}", "English")
        
        # Resolve the instruction with user language
        resolved_root_instruction = resolve_root_agent_instruction(session_id)
        logger.info(f"[DEBUG] Root agent instruction resolved for: {session_id}")
        logger.info(f"[DEBUG] Resolved instruction preview_part1: {resolved_root_instruction[:500]}...")
        logger.info(f"[DEBUG] Resolved instruction preview_part2: ... {resolved_root_instruction[-100:0]}")
        
        coordinator = Agent(
            name="rootAgent",
            # model=model_config.MODEL_GEMINI_2_0_FLASH,
            # generate_content_config=model_config.create_generate_content_config(model_config.ROOT_AGENT_CONFIG),
            model=model_config.MODEL,
            planner=model_config.create_planner(model_config.DEFAULT_AGENT_CONFIG),
            instruction=resolved_root_instruction,  # Use resolved instruction instead of raw template
            description="判断用户意图并分发任务",
            sub_agents=[trading_agent, asset_agent],
            before_agent_callback=session_aware_callback,
        )
        logger.info(f"[DEBUG] Coordinator Agent created for: {session_id}")

        # Create Sequential Agent Runner instead of single Runner
        logger.info(f"[DEBUG] Creating Sequential Agent Runner for: {session_id}")
        sequential_runner = create_sequential_agent_runner(
            root_agent=coordinator,
            session_service=unified_session_service,
            session_id=session_id,
            user_id=session_id
        )
        logger.info(f"[DEBUG] Sequential Agent Runner created for: {session_id}")

        # Set response modality = TEXT
        logger.info(f"[DEBUG] Creating RunConfig for: {session_id}")
        run_config = RunConfig(response_modalities=["TEXT"])
        logger.info(f"[DEBUG] RunConfig created for: {session_id}")

        # Return the sequential runner and run_config instead of live_events and live_request_queue
        logger.info(f"Starting new sequential agent session: {session_id}")
        return sequential_runner, run_config
    except Exception as e:
        logger.error(f"[DEBUG] Exception in start_agent_session for {session_id}: {str(e)}")
        # If session creation fails, release the session slot using unified session service
        await unified_session_service.release_session(session_id)
        raise


#
# FastAPI web app
#

app = FastAPI(lifespan=lifespan)
app.add_middleware(TraceIdMiddleware)

STATIC_DIR = Path("static")
app.mount("/static", StaticFiles(directory=STATIC_DIR), name="static")

# Mount A2A Agent Service endpoints
app.mount("/a2a", a2a_app, name="a2a")


@app.get("/")
async def root():
    """Serves the index.html"""
    return FileResponse(os.path.join(STATIC_DIR, "index.html"))


@app.get("/status/health")
async def health_check():
    logger.info("Health check endpoint was hit")
    return {"status": "healthy"}


@app.get("/status/ready")
async def ready_check():
    """Simple endpoint to check if server is ready to handle requests"""
    return {"status": "ready", "timestamp": datetime.datetime.now().isoformat()}


@app.get("/generate/token")
async def generate_token(user_id: str):
    return encode(user_id)

@app.websocket("/bdp-search-agent/ws/ai-report/{session_id}")
async def ws_ai_report_endpoint(websocket: WebSocket, session_id: str):
    await websocket_endpoint(websocket, session_id, WebSocketBusinessType.AI_REPORT)

@app.websocket("/bdp-search-agent/ws/{session_id}")
async def ws_default_endpoint(websocket: WebSocket, session_id: str):
    await websocket_endpoint(websocket, session_id, WebSocketBusinessType.DEFAULT)

async def websocket_endpoint(websocket: WebSocket, session_id: str, ws_biz_type: WebSocketBusinessType):
    """Client websocket endpoint with session management"""

    logger.info(f"[DEBUG] New WebSocket connection attempt for session: {session_id}")
    headers_dict = dict(websocket.headers)
    request_token = websocket.query_params.get("token")
    user_language = websocket.query_params.get("language")
    bnc_location = websocket.headers.get("bnc-location")
    bnc_currency = websocket.headers.get("bnc-currency")
    user_ip = web_utils.get_client_ip_from_websocket(websocket)
    trace_id = websocket.headers.get("x-trace-id", str(uuid.uuid4()))
    user_id_header = websocket.headers.get("x-user-id")
    set_trace_id(trace_id)
    set_session_id(session_id)
    set_user_id(user_id_header)

    # Wait for client connection
    await websocket.accept()
    logger.info(f"[DEBUG] WebSocket connection accepted for session: {session_id}, header param: {headers_dict}")

    logger.info(f"[DEBUG] Token: {request_token[:20] if request_token else 'None'}..., Language: {user_language}, "
                f"bnc_location: {bnc_location}, bnc_currency: {bnc_currency}, user_ip: {user_ip}")

    # check user token first to get user_id
    is_valid, user_id = validate_token(request_token, expected_version="v1")
    if not is_valid:
        logger.error(f"[DEBUG] Invalid token for session: {session_id}")
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return
    set_user_id(user_id)
    logger.info(f"[DEBUG] Valid token for user: {user_id}, session: {session_id}")

    # Set user context (both user_id and language) before starting agent session
    if not user_id:
        logger.error(f"[WEBSOCKET ERROR] Invalid user_id for session {session_id}. Token validation failed or returned empty user_id.")
        await websocket.send_text(json.dumps({
            "error": "Invalid user ID. Authentication failed.",
            "type": "auth_error"
        }))
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return
    
    # Log the user_id we're about to set
    logger.info(f"[WEBSOCKET] Setting user context with user_id: '{user_id}' for session: {session_id}")
    
    if user_language:
        normalized_user_language = map_iso_to_language(user_language)
        logger.info(f"[WEBSOCKET] Normalized user language: {normalized_user_language} from {user_language} for session: {session_id}")
        set_user_context(user_id, normalized_user_language, session_id)
        logger.info(f"User context set for session {session_id} - ID: {user_id}, Language: {normalized_user_language}")
    else:
        set_user_context(user_id, "english", session_id)  # Default to english
        logger.info(f"User context set for session {session_id} - ID: {user_id}, Language: english (default)")
        
    # Verify the user context was set correctly
    from util.tools.memory import get_user_context
    stored_user_id, stored_language = get_user_context(session_id)
    logger.info(f"[WEBSOCKET] Verified user context - ID: '{stored_user_id}', Language: {stored_language}")

    # Send connect success message to client
    connect_success_message = {
        "type": 0,
        "message": "connect success"
    }
    try:
        await websocket.send_text(json.dumps(connect_success_message))
        logger.info(f"[WEBSOCKET] Sent connect success message to client for session: {session_id}")
    except WebSocketDisconnect as ws_disconnect:
        logger.info(f"[WEBSOCKET] Client disconnected before connect success message could be sent for session {session_id}: {ws_disconnect}")
        return
    except Exception as send_error:
        logger.error(f"[WEBSOCKET] Error sending connect success message for session {session_id}: {send_error}")
        return

    # WebSocket setup complete, ready to start agent messaging
    logger.info(f"[DEBUG] WebSocket setup complete for session: {session_id}")

    logger.info(f"Client session: {session_id} connected")
    # Start agent session
    session_started = False

    try:
        # Check current session count before attempting to start
        current_sessions = unified_session_service.get_active_session_count()
        logger.info(f"Current active sessions: {current_sessions}/{unified_session_service.max_sessions}")

        # Check if session already exists and can be reused
        if unified_session_service.can_reuse_session(session_id, max_age_minutes=30):
            logger.info(f"Session {session_id} exists and can be reused")
            # Update the last accessed time
            unified_session_service.update_session_access(session_id)
            # Try to get the existing session
            try:
                session = await unified_session_service.get_session(
                    app_name=APP_NAME,
                    user_id=session_id,
                    session_id=session_id
                )
                if session:
                    logger.info(f"Successfully retrieved existing session {session_id}")
                    # Create a new sequential runner for the existing session
                    sequential_runner, run_config = await start_agent_session_for_ws_biz(session_id, ws_biz_type)
                    session_started = True
                else:
                    logger.warning(f"Failed to retrieve existing session {session_id}, creating new one")
                    sequential_runner, run_config = await start_agent_session_for_ws_biz(session_id, ws_biz_type)
                    session_started = True
            except Exception as e:
                logger.error(f"Error retrieving existing session {session_id}: {e}, creating new one")
                sequential_runner, run_config = await start_agent_session_for_ws_biz(session_id, ws_biz_type)
                session_started = True
        else:
            logger.info(f"Session {session_id} does not exist or is too old, creating new session")
            # Try to start agent session (this will handle session limiting)
            sequential_runner, run_config = await start_agent_session_for_ws_biz(session_id, ws_biz_type)
            session_started = True
            
        logger.info(f"[DEBUG] Agent session started successfully for session: {session_id}")

        # Initialize session context with websocket once at the beginning
        logger.info(f"[DEBUG] Initializing session context for session: {session_id}")
        from util.tools.memory import set_session_context, update_session_last_accessed
        set_session_context(session_id, websocket=websocket)
        update_session_last_accessed(session_id)  # Mark as recently accessed
        logger.info(f"[DEBUG] Session context initialized for session: {session_id}")
        
        # Verify session context was created properly and wait a moment for it to be fully established
        from util.tools.memory import _session_user_contexts
        verification_retries = 0
        max_verification_retries = 5
        while session_id not in _session_user_contexts and verification_retries < max_verification_retries:
            logger.info(f"[DEBUG] Waiting for session context to be established for session {session_id}, retry {verification_retries + 1}/{max_verification_retries}")
            await asyncio.sleep(0.1)
            verification_retries += 1
        
        if session_id in _session_user_contexts:
            logger.info(f"[DEBUG] Session context verified for session {session_id}")
            logger.info(f"[DEBUG] Session context keys: {list(_session_user_contexts[session_id].keys())}")
        else:
            logger.error(f"[DEBUG] Session context not found after initialization for session {session_id}")
            logger.info(f"[DEBUG] Available session contexts: {list(_session_user_contexts.keys())}")
            # Try one more time to set the context
            set_session_context(session_id, websocket=websocket)
            update_session_last_accessed(session_id)
            if session_id in _session_user_contexts:
                logger.info(f"[DEBUG] Session context successfully reinitialized for session {session_id}")
            else:
                logger.error(f"[DEBUG] Failed to initialize session context for session {session_id}")
                raise Exception(f"Failed to initialize session context for session {session_id}")

        # Start tasks - use live_events and live_request_queue directly
        logger.info(f"[DEBUG] WebSocket connection fully established, starting messaging tasks for session: {session_id}")
        logger.info(f"[DEBUG] live_events type: {type(sequential_runner)}")
        logger.info(f"[DEBUG] live_request_queue type: {type(run_config)}")
        
        # Keep track of conversation state
        conversation_active = True
        current_sequential_runner = sequential_runner
        current_run_config = run_config
        
        while conversation_active:
            logger.info(f"[DEBUG] Starting conversation turn for session: {session_id}")
            
            # Verify session context is still available before creating tasks
            if session_id not in _session_user_contexts:
                logger.error(f"[DEBUG] Session context not found at start of conversation turn for session {session_id}")
                logger.info(f"[DEBUG] Available session contexts: {list(_session_user_contexts.keys())}")
                # Try to reinitialize session context
                set_session_context(session_id, websocket=websocket)
                logger.info(f"[DEBUG] Session context reinitialized for session {session_id}")
            
            logger.info(f"[DEBUG] Creating agent_to_client_task for session: {session_id}")
            agent_to_client_task = asyncio.create_task(
                agent_to_client_messaging(websocket, current_sequential_runner, session_id, ws_biz_type)
            )
            logger.info(f"[DEBUG] agent_to_client_task created for session: {session_id}")
            
            logger.info(f"[DEBUG] Creating client_to_agent_task for session: {session_id}")
            client_to_agent_task = asyncio.create_task(
                client_to_agent_messaging_for_ws_biz(websocket, current_sequential_runner, session_id, unified_session_service, ws_biz_type)
            )
            logger.info(f"[DEBUG] client_to_agent_task created for session: {session_id}")
            
            # Give tasks a moment to start and access session context
            await asyncio.sleep(0.2)  # Increased from 0.1 to 0.2 to ensure tasks have time to start
            
            logger.info(f"[DEBUG] Both tasks created, starting asyncio.wait for session: {session_id}")
            logger.info(f"[DEBUG] Task states - agent_to_client: {agent_to_client_task.done()}, client_to_agent: {client_to_agent_task.done()}")
            
            # Use FIRST_COMPLETED but with better error handling
            # This allows us to handle disconnections gracefully
            done, pending = await asyncio.wait(
                [agent_to_client_task, client_to_agent_task],
                return_when=asyncio.FIRST_COMPLETED,
            )
            logger.info(f"[DEBUG] asyncio.wait completed for session: {session_id}, done: {len(done)}, pending: {len(pending)}")
            
            # Check which task completed and why
            should_continue = False
            for task in done:
                if task == agent_to_client_task:
                    logger.info(f"[DEBUG] agent_to_client_task completed for session: {session_id}")
                    if task.exception():
                        logger.error(f"[DEBUG] agent_to_client_task exception: {task.exception()}")
                        conversation_active = False
                    else:
                        logger.info(f"[DEBUG] agent_to_client_task completed normally (agent finished)")
                        logger.info(f"[DEBUG] Agent processing complete, preparing for next user message in session: {session_id}")
                        should_continue = True
                elif task == client_to_agent_task:
                    logger.info(f"[DEBUG] client_to_agent_task completed for session: {session_id}")
                    if task.exception():
                        exception = task.exception()
                        logger.error(f"[DEBUG] client_to_agent_task exception: {exception}")
                        # Check if it's a normal WebSocket disconnect vs an error
                        if isinstance(exception, WebSocketDisconnect):
                            logger.info(f"[DEBUG] Normal WebSocket disconnect for session: {session_id}")
                        else:
                            logger.error(f"[DEBUG] Unexpected error in client_to_agent_task: {exception}")
                        conversation_active = False
                    else:
                        logger.info(f"[DEBUG] client_to_agent_task completed normally")
                        conversation_active = False
            
            # Clean up current tasks
            logger.info(f"[DEBUG] Cleaning up current tasks for session: {session_id}")
            for task in pending:
                task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await task
            
            # If we should continue, prepare for the next conversation turn
            if should_continue:
                logger.info(f"[DEBUG] Preparing for next conversation turn in session: {session_id}")
                try:
                    # Create a new agent session for the next turn
                    current_sequential_runner, current_run_config = await start_agent_session_for_ws_biz(session_id, ws_biz_type)
                    logger.info(f"[DEBUG] New agent session ready for next turn in session: {session_id}")
                except Exception as e:
                    logger.error(f"[DEBUG] Error preparing next conversation turn: {e}")
                    conversation_active = False
            else:
                logger.info(f"[DEBUG] Conversation ended for session: {session_id}")
                conversation_active = False
        
        logger.info(f"[DEBUG] Multi-turn conversation completed for session: {session_id}")

    except Exception as e:
        error_msg = str(e)
        logger.error(f"[WEBSOCKET ERROR]: {error_msg}")

        # Send error message to client if possible
        try:
            if "Maximum concurrent sessions exceeded" in error_msg:
                await websocket.send_text(json.dumps({
                    "error": "Too many concurrent sessions. Please try again in a moment.",
                    "type": "session_limit_exceeded",
                    "active_sessions": unified_session_service.get_active_session_count(),
                    "max_sessions": unified_session_service.max_sessions
                }))
            else:
                await websocket.send_text(json.dumps({
                    "error": f"Session error: {error_msg}",
                    "type": "session_error"
                }))
        except WebSocketDisconnect as ws_disconnect:
            logger.info(f"[WEBSOCKET] Client disconnected before error message could be sent for session {session_id}: {ws_disconnect}")
        except Exception as send_error:
            logger.error(f"[WEBSOCKET] Error sending error message to client for session {session_id}: {send_error}")

    finally:
        logger.info(f"Client #{session_id} disconnected")
        # Clean session using unified session service
        try:
            logger.info("Begin to clean session")

            # Clean up the sequential runner and its toolsets
            if 'current_sequential_runner' in locals():
                try:
                    logger.info(f"Closing sequential runner for session {session_id}")
                    # Use a timeout to prevent hanging during cleanup
                    await asyncio.wait_for(current_sequential_runner.close(), timeout=15.0)
                    logger.info(f"Successfully closed sequential runner for session {session_id}")
                except asyncio.TimeoutError:
                    logger.warning(f"Sequential runner cleanup timed out for session {session_id}")
                except Exception as e:
                    logger.error(f"Error closing sequential runner for session {session_id}: {e}")
                    # Don't let cleanup errors break the session cleanup process

            # Check if session was recently accessed before cleaning up
            if session_started:
                # Get session info to check last accessed time
                session_info = unified_session_service.active_sessions.get(session_id)
                if session_info:
                    last_accessed = session_info.get('last_accessed', session_info['created_at'])
                    age_minutes = (datetime.datetime.now() - last_accessed).total_seconds() / 60
                    logger.info(f"Session {session_id} last accessed: {last_accessed}, age: {age_minutes:.1f} minutes")
                    
                    # Only delete session if it's older than 5 minutes (allowing for reconnection)
                    if age_minutes > 5:
                        logger.info(f"Session {session_id} is older than 5 minutes, deleting")
                        await unified_session_service.delete_session(
                            app_name=APP_NAME,
                            user_id=session_id,
                            session_id=session_id
                        )
                    else:
                        logger.info(f"Session {session_id} is recent ({age_minutes:.1f} minutes), keeping for potential reconnection")
                        # Just update the last accessed time to keep it alive
                        unified_session_service.active_sessions[session_id]['last_accessed'] = datetime.datetime.now()
                else:
                    logger.warning(f"Session {session_id} not found in active sessions during cleanup")
            else:
                # If session was acquired but failed to start, just release it
                logger.info(f"Session {session_id} was acquired but failed to start, releasing")
                await unified_session_service.release_session(session_id)
            
            # Add a small delay to allow any pending operations to complete
            await asyncio.sleep(1)
            # Clean up session context for this session (with delayed cleanup logic)
            clear_session_context(session_id)

            logger.info("Clean success")
        except Exception as e:
            logger.error(f"[SESSION CLEAN ERROR]: {e}")
            # Ensure session cleanup even if delete_session fails
            try:
                await unified_session_service.release_session(session_id)
                # Use force cleanup in error cases to ensure cleanup happens
                from util.tools.memory import force_clear_session_context
                force_clear_session_context(session_id)
            except:
                pass
        try:
            if websocket.client_state != WebSocketState.DISCONNECTED:
                await websocket.close()
        except RuntimeError as e:
            if "Cannot call \"send\" once a close message has been sent" in str(e):
                logger.info(f"[WEBSOCKET] WebSocket already closed for session {session_id}")
            else:
                logger.error(f"[WEBSOCKET] Error closing WebSocket for session {session_id}: {e}")
        except Exception as e:
            logger.error(f"[WEBSOCKET] Unexpected error closing WebSocket for session {session_id}: {e}")


@app.get("/session-status")
async def get_session_status():
    """Get current session status and limits"""
    return {
        "status": "success",
        "active_sessions": unified_session_service.get_active_session_count(),
        "max_sessions": unified_session_service.max_sessions,
        "available_slots": unified_session_service.max_sessions - unified_session_service.get_active_session_count(),
        "session_details": unified_session_service.active_sessions
    }


@app.get("/session-debug")
async def get_session_debug():
    """Get detailed session manager state for debugging"""
    semaphore_state = unified_session_service.get_semaphore_state()
    return {
        "status": "success",
        "session_manager_state": semaphore_state,
        "semaphore_mismatch": semaphore_state['active_sessions_count'] != (unified_session_service.max_sessions - semaphore_state['semaphore_value']),
        "expected_semaphore_value": unified_session_service.max_sessions - semaphore_state['active_sessions_count'],
        "actual_semaphore_value": semaphore_state['semaphore_value']
    }


#
# YMAL utility endpoints
#

@app.post("/ymal/generate-queries")
async def ymal_generate_queries_endpoint(payload: Dict):
    """Generate follow-up queries using YMAL service.

    Request body JSON:
      - token_symbol: str
      - last_reply: str
      - target_lang: str (default 'en')
      - model_name: Optional[str]
      - history: Optional[List[str]] (for dedup against seen queries)
      - rule_queries: Optional[List[str]] (predefined queries to prioritize)
    """
    os.environ["YMAL_LLM_PROVIDER"] = "http"
    os.environ["QWEN_API_BASE"] = "http://qa-qwen.qa1fdg.net"
    os.environ["QWEN_API_KEY"] = "empty"
    os.environ["QWEN_MODEL"] = "Qwen3-30B-A3B-Instruct-2507"
    try:
        token_symbol = (payload.get("token_symbol") or payload.get("token") or "").strip()
        last_reply = (payload.get("last_reply") or "").strip()
        target_lang = (payload.get("target_lang") or "en").strip()
        model_name = payload.get("model_name")
        rule_queries = payload.get("rule_queries") or []
        if not isinstance(rule_queries, list):
            rule_queries = []
        history = payload.get("history") or []
        if not isinstance(history, list):
            history = []

        suggestions = generate_suggestions(
            token_symbol,
            last_reply,
            target_lang,
            model_name,
            history=history,
            rule_queries=rule_queries,
        )

        return suggestions
    except Exception as exc:
        logger.error(f"/ymal/generate-queries error: {exc}")
        return {"error": str(exc)}


@app.post("/ymal/generate-reply")
async def ymal_generate_reply_endpoint(payload: Dict):
    """Route question via YMAL service and return concatenated streamed text.

    Request body JSON:
      - question: str
      - target_lang: str (default 'en')
      - en_query: Optional[str] (exact English template for routing)
    """
    try:
        question = (payload.get("question") or "").strip()
        if not question:
            return {"error": "question is required"}
        target_lang = (payload.get("target_lang") or "en").strip()
        en_query = payload.get("en_query")

        chunks: list[str] = []
        async for chunk in route_question(question, target_lang, en_query):
            if not chunk:
                continue
            if isinstance(chunk, dict):
                msg = chunk.get("message")
                if isinstance(msg, str):
                    chunks.append(msg)
            elif isinstance(chunk, str):
                chunks.append(chunk)
        return {"text": "".join(chunks)}
    except Exception as exc:
        logger.error(f"/ymal/route-question error: {exc}")
        return {"error": str(exc)}




# import agent_trade.convert
import agent_convert.convert
@app.post("/recommend-convert-token")
def recommend_convert_token_endpoint(request: dict):
    """Post user_id and user_query, then check the user's latest spot and convert transaction token, then return recommend_convert_token
    return {
        "from_coin": "BTC",
        "to_coin": "USDT",
        "amount": 100,
        "amount_base": "USDT"
    }"""
    # result = agent_trade.convert.recommend_convert_token_endpoint(request)
    result = agent_convert.convert.recommend_convert_token_endpoint(request)
    logger.info(f"recommend-convert-token result: {result}")
    return result

from rag_agent.news.news_sequential_agent import process_news_query_with_caching, generate_summary_id
from rag_agent.news.optimized_news_agent import create_optimized_news_agent
from rag_agent.market.market_analysis_agent import(
    create_market_analysis_agent, 
    process_market_analysis_with_prefetch
)
from rag_agent.google.google_search_agent import create_google_search_agent
from rag_agent.campaign.campaign_agent import (
    create_campaign_analysis_agent,
    process_campaign_analysis_with_prefetch,
)
from rag_agent.google.google_search_agent import process_google_search_with_caching

async def process_news_query_async(summary_id: str, trace_id: str, user_id: str, query: str, 
                                   user_language: str, scene: str, type: str, sub_type: str):
    """
    Main async function to process news query in the background.
    This function handles all the processing including session setup, agent creation, and cleanup.
    """
    # Generate temp session ID for this request
    import random
    temp_session_id = f"{random.randint(1000000000, 9999999999)}-{random.randint(100000000, 999999999)}"
    
    logger.info(f"[NEWS ASYNC] Starting async processing for summaryId: {summary_id}, session: {temp_session_id}")
    
    try:
        set_session_id(temp_session_id)
        # Normalize user language and set user context
        normalized_user_language = map_iso_to_language(user_language)
        logger.info(f"[NEWS ASYNC] Normalized user language: {normalized_user_language} from {user_language}")
        
        # Set up session context and ADK session service
        await setup_session_context(temp_session_id, user_id, normalized_user_language, query)
        
        # Verify the user context was set correctly
        from util.tools.memory import get_user_context
        stored_user_id, stored_language = get_user_context(temp_session_id)
        logger.info(f"[NEWS ASYNC] Verified user context - ID: '{stored_user_id}', Language: {stored_language}")
        
        # Create news agent and runner
        news_agent = create_optimized_news_agent()
        from google.adk import Runner
        runner = Runner(
            app_name=APP_NAME,
            agent=news_agent,
            session_service=unified_session_service,
        )
        
        # Create run config

        run_config = RunConfig(response_modalities=["TEXT"])
        import time 
        start_time = time.time()
        # Process the news query using the main caching function
        result = await process_news_query_with_caching(
            summary_id=summary_id,
            trace_id=trace_id,
            user_query=query,
            user_id=user_id,
            user_language=user_language,
            scene=scene,
            type=type,
            sub_type=sub_type,
            session_id=temp_session_id,
            agent_runner=runner,
            run_config=run_config
        )
        end_time = time.time()
        logger.info(f"[NEWS ASYNC] Query Time taken: {end_time - start_time} seconds")
        logger.info(f"[NEWS ASYNC] Completed processing for summaryId: {summary_id}, status: {result.get('status')}")
        return result
        
    except Exception as e:
        logger.error(f"[NEWS ASYNC] Error in async processing for summaryId {summary_id}: {str(e)}")
        import traceback
        logger.error(f"[NEWS ASYNC] Traceback: {traceback.format_exc()}")
        
        error_result = {
            "status": "error",
            "response": "An error occurred while processing your news query. Please try again.",
            "error": str(e)
        }
        logger.error(f"[NEWS ASYNC] Error result: {error_result}")
        return error_result
        
    finally:
        # Clean up runner and session context
        try:
            if 'runner' in locals():
                await runner.close()
                logger.info(f"[NEWS ASYNC] News runner closed for session: {temp_session_id}")
        except Exception as e:
            logger.error(f"[NEWS ASYNC] Error closing news runner: {e}")
        
        try:
            from util.tools.memory import clear_session_context
            clear_session_context(temp_session_id)
            logger.info(f"[NEWS ASYNC] Cleaned up session context for summaryId: {summary_id}")
        except Exception as e:
            logger.error(f"[NEWS ASYNC] Error clearing session context for summaryId {summary_id}: {e}")

async def process_market_analysis_async(analysis_id: str, trace_id: str, user_id: str, query: str, 
                                       token: str, user_language: str, analysis_type: str):
    """
    Main async function to process market analysis query in the background.
    This function handles all the processing including session setup, agent creation, and cleanup.
    """
    # Generate temp session ID for this request
    import random
    temp_session_id = f"{random.randint(1000000000, 9999999999)}-{random.randint(100000000, 999999999)}"
    
    logger.info(f"[MARKET ASYNC] Starting async processing for analysisId: {analysis_id}, session: {temp_session_id}")
    
    try:
        set_session_id(temp_session_id)
        # Normalize user language and set user context
        normalized_user_language = map_iso_to_language(user_language)
        logger.info(f"[MARKET ASYNC] Normalized user language: {normalized_user_language} from {user_language}")
        
        # Set up session context and ADK session service
        await setup_session_context(temp_session_id, user_id, normalized_user_language, query)
        
        # Verify the user context was set correctly
        from util.tools.memory import get_user_context
        stored_user_id, stored_language = get_user_context(temp_session_id)
        logger.info(f"[MARKET ASYNC] Verified user context - ID: '{stored_user_id}', Language: {stored_language}")
        
        # Create market analysis agent and runner
        market_agent = create_market_analysis_agent()
        
        from google.adk import Runner
        runner = Runner(
            app_name=APP_NAME,
            agent=market_agent,
            session_service=unified_session_service,
        )
        
        # Create run config
        run_config = RunConfig(response_modalities=["TEXT"])
        
        # Process the market analysis query using the main caching function
        result = await process_market_analysis_with_prefetch(
            analysis_id=analysis_id,
            trace_id=trace_id,
            user_query=query,
            token=token,
            user_id=user_id,
            user_language=user_language,
            analysis_type=analysis_type,
            session_id=temp_session_id,
            agent_runner=runner,
            run_config=run_config
        )
        
        logger.info(f"[MARKET ASYNC] Completed processing for traceId: {trace_id}, sessionId: {temp_session_id}, analysisId: {analysis_id}, status: {result.get('status')}")
        return result
        
    except Exception as e:
        logger.error(f"[MARKET ASYNC] Error in async processing for analysisId {analysis_id}: {str(e)}")
        import traceback
        logger.error(f"[MARKET ASYNC] Traceback: {traceback.format_exc()}")
        
        error_result = {
            "status": "error",
            "response": "An error occurred while processing your market analysis query. Please try again.",
            "error": str(e)
        }
        logger.error(f"[MARKET ASYNC] Error result: {error_result}")
        return error_result
        
    finally:
        # Clean up runner and session context
        try:
            if 'runner' in locals():
                await runner.close()
                logger.info(f"[MARKET ASYNC] Market runner closed for session: {temp_session_id}")
        except Exception as e:
            logger.error(f"[MARKET ASYNC] Error closing market runner: {e}")
        
        try:
            from util.tools.memory import clear_session_context
            clear_session_context(temp_session_id)
            logger.info(f"[MARKET ASYNC] Cleaned up session context for analysisId: {analysis_id}")
        except Exception as e:
            logger.error(f"[MARKET ASYNC] Error clearing session context for analysisId {analysis_id}: {e}")

async def process_google_search_async(search_id: str, trace_id: str, user_id: str, query: str, 
                                     user_language: str, tool: str="google_search"):
    """
    Main async function to process Google search query in the background.
    This function handles all the processing including session setup, agent creation, and cleanup.
    """
    # Generate temp session ID for this request
    import random
    temp_session_id = f"{random.randint(1000000000, 9999999999)}-{random.randint(100000000, 999999999)}"
    
    logger.info(f"[GOOGLE ASYNC] Starting async processing for searchId: {search_id}, session: {temp_session_id}")
    
    try:
        set_session_id(temp_session_id)
        # Normalize user language and set user context
        normalized_user_language = map_iso_to_language(user_language)
        logger.info(f"[GOOGLE ASYNC] Normalized user language: {normalized_user_language} from {user_language}")

        # Set up session context and ADK session service
        await setup_session_context(temp_session_id, user_id, normalized_user_language, query)
        
        # Verify the user context was set correctly
        from util.tools.memory import get_user_context
        stored_user_id, stored_language = get_user_context(temp_session_id)
        logger.info(f"[GOOGLE ASYNC] Verified user context - ID: '{stored_user_id}', Language: {stored_language}")
        
        # Create Google search agent and runner  
        faq_mode = True
        if tool == "google_search":
            logger.info(f"[GOOGLE ASYNC] Creating google agent with faq_mode={faq_mode}")
        search_agent = create_google_search_agent(faq_mode=faq_mode)

        runner = Runner(
            app_name=APP_NAME,
            agent=search_agent,
            session_service=unified_session_service
        )
        # Create run config
        run_config = RunConfig(response_modalities=["TEXT"])
        
        result = await process_google_search_with_caching(
            search_id = search_id,
            trace_id = trace_id,
            user_query = query,
            user_id = user_id,
            user_language = user_language,
            search_tool = "google_search",
            session_id = temp_session_id,
            agent_runner = runner,
            run_config = run_config
        )
        logger.info(f"[GOOGLE ASYNC] Completed processing for searchId: {search_id}, status: {result.get('status')}")
        return result
        
    except Exception as e:
        logger.error(f"[GOOGLE ASYNC] Error in async processing for searchId {search_id}: {str(e)}")
        import traceback
        logger.error(f"[GOOGLE ASYNC] Traceback: {traceback.format_exc()}")
        
        error_result = {
            "status": "error",
            "response": "An error occurred while processing your search query. Please try again.",
            "error": str(e),
            "summaryId": search_id,
            "query": query
        }
        logger.error(f"[GOOGLE ASYNC] Error result: {error_result}")
        return error_result
        
    finally:
        # Clean up runner and session context
        try:
            if 'runner' in locals():
                await runner.close()
                logger.info(f"[GOOGLE ASYNC] Google runner closed for session: {temp_session_id}")
        except Exception as e:
            logger.error(f"[GOOGLE ASYNC] Error closing Google runner: {e}")
        
        try:
            from util.tools.memory import clear_session_context
            clear_session_context(temp_session_id)
            logger.info(f"[GOOGLE ASYNC] Cleaned up session context for searchId: {search_id}")
        except Exception as e:
            logger.error(f"[GOOGLE ASYNC] Error clearing session context for searchId {search_id}: {e}")

async def process_campaign_analysis_async(analysis_id: str, trace_id: str, user_id: str, query: str, user_language: str, domain: str):
    """
    Main async function to process campaign analysis query in the background.
    This function handles all the processing including session setup, agent creation, and cleanup.
    """
    # Generate temp session ID for this request
    import random
    temp_session_id = f"{random.randint(1000000000, 9999999999)}-{random.randint(100000000, 999999999)}"

    logger.info(f"[CAMPAIGN ASYNC] Starting async processing for analysisId: {analysis_id}, session: {temp_session_id}")

    try:
        set_session_id(temp_session_id)
        # Normalize user language and set user context
        normalized_user_language = map_iso_to_language(user_language)
        logger.info(f"[CAMPAIGN ASYNC] Normalized user language: {normalized_user_language} from {user_language}")

        # Set up session context and ADK session service
        await setup_session_context(temp_session_id, user_id, normalized_user_language, query)

        # Verify the user context was set correctly
        from util.tools.memory import get_user_context
        stored_user_id, stored_language = get_user_context(temp_session_id)
        logger.info(f"[CAMPAIGN ASYNC] Verified user context - ID: '{stored_user_id}', Language: {stored_language}")

        # create campaign analysis agent
        agent = create_campaign_analysis_agent(user_query=query, user_language=user_language)
        # Create campaign analysis agent and runner using prefetch approach
        from google.adk import Runner
        runner = Runner(
            app_name=APP_NAME,
            agent=agent,
            session_service=unified_session_service,
        )

        # Create run config
        run_config = RunConfig(response_modalities=["TEXT"])

        # Process the campaign analysis query using the prefetch function
        result = await process_campaign_analysis_with_prefetch(
            analysis_id=analysis_id,
            trace_id=trace_id,
            user_query=query,
            user_id=user_id,
            user_language=user_language,
            session_id=temp_session_id,
            domain=domain,
            agent_runner=runner,
            run_config=run_config
        )

        logger.info(f"[CAMPAIGN ASYNC] Completed processing for analysisId: {analysis_id}, status: {result.get('status')}")
        return result

    except Exception as e:
        logger.error(f"[CAMPAIGN ASYNC] Error in async processing for analysisId {analysis_id}: {str(e)}")
        import traceback
        logger.error(f"[CAMPAIGN ASYNC] Traceback: {traceback.format_exc()}")

        error_result = {
            "status": "error",
            "response": "An error occurred while processing your campaign analysis query. Please try again.",
            "error": str(e)
        }
        logger.error(f"[CAMPAIGN ASYNC] Error result: {error_result}")
        return error_result

    finally:
        # Clean up runner and session context
        try:
            if 'runner' in locals():
                await runner.close()
                logger.info(f"[CAMPAIGN ASYNC] Campaign runner closed for session: {temp_session_id}")
        except Exception as e:
            logger.error(f"[CAMPAIGN ASYNC] Error closing campaign runner: {e}")

        try:
            from util.tools.memory import clear_session_context
            clear_session_context(temp_session_id)
            logger.info(f"[CAMPAIGN ASYNC] Cleaned up session context for analysisId: {analysis_id}")
        except Exception as e:
            logger.error(f"[CAMPAIGN ASYNC] Error clearing session context for analysisId {analysis_id}: {e}")

@app.post("/rag/query/news")
async def news_query_endpoint(request: dict):
    """
    HTTP endpoint for news queries with immediate response and background processing.
    
    Returns summaryId immediately, then processes the query asynchronously.
    """
    try:
        # Load and validate request data
        request_data = json.loads(request) if isinstance(request, str) else request
        trace_id = request_data.get("traceId", "")
        user_id = request_data.get("userId", "")
        query = request_data.get("query", "")
        user_language = request_data.get("lang", "en")
        scene = request_data.get("scene", "SRPSearch")
        type = request_data.get("type", "news")
        sub_type = request_data.get("subType", "online")
        set_trace_id(trace_id)
        set_user_id(user_id)

        # Generate summary ID
        summary_id = generate_summary_id(query, user_id)

        logger.info(f"[NEWS ENDPOINT] Received news query for user {user_id}")
        logger.info(f"[NEWS ENDPOINT] Generated summaryId: {summary_id}")
        logger.info(f"[NEWS ENDPOINT] Trace ID: {trace_id}")
        # Start background processing task (fire and forget)
        asyncio.create_task(
            process_news_query_async(
                summary_id=summary_id,
                trace_id=trace_id,
                user_id=user_id,
                query=query,
                user_language=user_language,
                scene=scene,
                type=type,
                sub_type=sub_type
            )
        )
        
        logger.info(f"[NEWS ENDPOINT] Started background task for summaryId: {summary_id}")
        
        # Return immediate response with summaryId
        return {
            "status": "success",
            "status_code": 200,
            "traceId": trace_id,
            "summaryId": summary_id,
            "message": "News query received and is being processed",

        }
        
    except Exception as e:
        logger.error(f"[NEWS ENDPOINT] Error processing news query request: {str(e)}")
        import traceback
        logger.error(f"[NEWS ENDPOINT] Traceback: {traceback.format_exc()}")
        
        return {
            "status": "error",
            "status_code": 500,
            "error": str(e),
            "message": "An error occurred while receiving your news query. Please try again.",
            "traceId": trace_id,
            "summaryId": summary_id,
        }

@app.post("/rag/query/market")
async def market_analysis_endpoint(request: dict):
    """
    HTTP endpoint for market analysis queries with immediate response and background processing.
    
    Returns analysisId immediately, then processes the market analysis asynchronously.
    """
    try:
        # Load and validate request data
        request_data = json.loads(request) if isinstance(request, str) else request
        trace_id = request_data.get("traceId", "")
        user_id = request_data.get("userId", "")
        query = request_data.get("query", "")
        token = request_data.get("token", "")  # Token symbol to analyze
        user_language = request_data.get("lang", "en")
        analysis_type = request_data.get("analysisType", "comprehensive")  # comprehensive, technical, fundamental
        
        # Generate analysis ID
        analysis_id = generate_summary_id(query, token)
        set_trace_id(trace_id)
        set_user_id(user_id)

        logger.info(f"[MARKET ENDPOINT] Received market analysis query for user {user_id}")
        logger.info(f"[MARKET ENDPOINT] Generated analysisId: {analysis_id}")
        logger.info(f"[MARKET ENDPOINT] Token: {token}")
        logger.info(f"[MARKET ENDPOINT] Analysis Type: {analysis_type}")
        logger.info(f"[MARKET ENDPOINT] Trace ID: {trace_id}")
        
        # Start background processing task (fire and forget)
        asyncio.create_task(
            process_market_analysis_async(
                analysis_id=analysis_id,
                trace_id=trace_id,
                user_id=user_id,
                query=query,
                token=token,
                user_language=user_language,
                analysis_type=analysis_type
            )
        )
        
        logger.info(f"[MARKET ENDPOINT] Started background task for analysisId: {analysis_id}")
        
        # Return immediate response with analysisId
        return {
            "status": "success",
            "status_code": 200,
            "traceId": trace_id,
            "summaryId": analysis_id,
            "token": token,
            "analysisType": analysis_type,
            "message": "Market analysis query received and is being processed",
        }
        
    except Exception as e:
        logger.error(f"[MARKET ENDPOINT] Error processing market analysis query request: {str(e)}")
        import traceback
        logger.error(f"[MARKET ENDPOINT] Traceback: {traceback.format_exc()}")
        
        return {
            "status": "error",
            "status_code": 500,
            "error": str(e),
            "message": "An error occurred while receiving your market analysis query. Please try again.",
            "traceId": trace_id,
            "summaryId": analysis_id,
        }

@app.post("/rag/query/google")
async def google_search_endpoint(request: dict):
    """
    HTTP endpoint for Google search queries with immediate response and processing.
    
    Returns searchId immediately, then processes the search query synchronously.
    """
    try:
        # Load and validate request data
        request_data = json.loads(request) if isinstance(request, str) else request
        trace_id = request_data.get("traceId", "")
        user_id = request_data.get("userId", "")
        query = request_data.get("query", "")
        user_language = request_data.get("lang", "en")
        # tool = request_data.get("tool", "google_search")
        # Generate search ID
        search_id = generate_summary_id(query, user_id)
        set_trace_id(trace_id)
        set_user_id(user_id)

        logger.info(f"[GOOGLE ENDPOINT] Received Google search query for user {user_id}")
        logger.info(f"[GOOGLE ENDPOINT] Generated searchId: {search_id}")
        logger.info(f"[GOOGLE ENDPOINT] Query: {query}")
        logger.info(f"[GOOGLE ENDPOINT] Trace ID: {trace_id}")

        
        # Process the Google search query synchronously and wait for response
        # change to async here 
        asyncio.create_task(
            process_google_search_async(
                search_id=search_id,
                trace_id=trace_id,
                user_id=user_id,
                query=query,
                user_language=user_language,
                tool="google_search"
            )
        )
        
        logger.info(f"[GOOGLE ENDPOINT] Completed processing for traceId: {trace_id}, summaryId: {search_id}")
        
        # Return the result with additional metadata
        return {
            "status": "success",
            "status_code": 200,
            "traceId": trace_id,
            "summaryId": search_id, # 统一返回summaryId
            "message": "Google search query processed successfully",
        }
        
    except Exception as e:
        logger.error(f"[GOOGLE ENDPOINT] Error processing Google search query request: {str(e)}")
        import traceback
        logger.error(f"[GOOGLE ENDPOINT] Traceback: {traceback.format_exc()}")
        
        return {
            "status": "error",
            "status_code": 500,
            "error": str(e),
            "message": "An error occurred while receiving your search query. Please try again.",
            "traceId": trace_id if 'trace_id' in locals() else "",
            "searchId": search_id if 'search_id' in locals() else "",
        }

@app.post("/rag/query/campaign")
async def campaign_analysis_endpoint(request: dict):
    """
    HTTP endpoint for campaign analysis queries with immediate response and background processing.

    Returns analysisId immediately, then processes the campaign analysis asynchronously.
    """
    try:
        # Load and validate request data
        request_data = json.loads(request) if isinstance(request, str) else request
        trace_id = request_data.get("traceId", "")
        user_id = request_data.get("userId", "")
        query = request_data.get("query", "")
        user_language = request_data.get("lang", "en")
        domain = request_data.get("domain", "binance.com")
        set_trace_id(trace_id)
        set_user_id(user_id)

        # Generate analysis ID
        campaignId = generate_summary_id(query, user_id)

        logger.info(f"[CAMPAIGN ENDPOINT] Received campaign analysis query for user {user_id}")
        logger.info(f"[CAMPAIGN ENDPOINT] Generated campaignId: {campaignId}")
        logger.info(f"[CAMPAIGN ENDPOINT] Query: {query}")
        logger.info(f"[CAMPAIGN ENDPOINT] Trace ID: {trace_id}")

        # Start background processing task (fire and forget)
        asyncio.create_task(
            process_campaign_analysis_async(
                analysis_id=campaignId,
                trace_id=trace_id,
                user_id=user_id,
                query=query,
                user_language=user_language,
                domain=domain
            )
        )

        logger.info(f"[CAMPAIGN ENDPOINT] Started background task for analysisId: {campaignId}")

        # Return immediate response with analysisId
        return {
            "status": "success",
            "status_code": 200,
            "traceId": trace_id,
            "summaryId": campaignId,
            "query": query,
            # "token": token,
            # "campaignType": campaign_type,
            "message": "Campaign analysis query received and is being processed",
        }

    except Exception as e:
        logger.error(f"[CAMPAIGN ENDPOINT] Error processing campaign analysis query request: {str(e)}")
        import traceback
        logger.error(f"[CAMPAIGN ENDPOINT] Traceback: {traceback.format_exc()}")

        return {
            "status": "error",
            "status_code": 500,
            "error": str(e),
            "message": "An error occurred while receiving your campaign analysis query. Please try again.",
            "traceId": trace_id if 'trace_id' in locals() else "",
            "summaryId": campaignId if 'campaignId' in locals() else "",
        }

# Generate Spot Token Report
# token:             TOKEN NAME
# end_timestamp_ms:  UNIX_TIMESTAMP IN MILLISECONDS
# mode:              "single-agent" or "multi-agent"
# open_market_timestamp_ms: UNIX_TIMESTAMP IN MILLISECONDS
# market_status:     "TRADING" or "PRE-TRADING"
# conan_key:         API KEY for Conan Forward (For Dev Use Only)
# mock_data:         True or False (For Dev Use Only)
# is_core_token:     True or False (For core token: pplx switch)
# exp_params:        Dict of experiment params
@app.post("/generate_token_report")
def generate_token_report(request: dict):
    result = token_report_agent.run(**request)
    logger.log(logging.INFO if result["success"] else logging.ERROR,
              f"generate_token_report {request.get("token", "")} at {request.get("end_timestamp_ms", "")}, {'success' if result['success'] else 'failed'}")
    return result

@app.get("/generate_spot_token_report")
def generate_spot_token_report(token: str, end_timestamp_ms: int, mode: str = "single-agent", open_market_timestamp_ms: int = 0, market_status: str = None, conan_key: str = None, mock_data: bool = False, is_core_token: bool = False):
    result = token_report_agent.run(token = token, end_timestamp_ms=end_timestamp_ms, open_market_timestamp_ms=open_market_timestamp_ms, market_status=market_status, mode=mode, conan_key=conan_key, mock_data=mock_data, is_core_token=is_core_token)
    logger.log(logging.INFO if result["success"] else logging.ERROR,
              f"generate_spot_token_report {token} at {end_timestamp_ms} with mode {mode}, {'success' if result['success'] else 'failed'}")
    return result

# Add list all services here to show the a2a service url 
# under /a2a/ 
@app.get("/services")
async def list_services():
    """List all available services and endpoints"""
    return {
        "status": "success",
        "services": {
            "websocket": {
                "description": "Main websocket endpoint for chat sessions",
                "endpoint": "/bdp-search-agent/ws/{session_id}",
                "type": "websocket"
            },
            "health": {
                "description": "Health check endpoints",
                "endpoints": ["/status/health", "/status/ready"]
            },
            "session": {
                "description": "Session management endpoints", 
                "endpoints": ["/session-status", "/session-debug"]
            },
            "trading": {
                "description": "Trading related endpoints",
                "endpoints": ["/recommend-convert-token"]
            },
            "rag": {
                "description": "RAG endpoints for news, market analysis, campaign analysis, and Google search",
                "endpoints": ["/rag/query/news", "/rag/query/market", "/rag/query/campaign", "/rag/query/google"]
            },
            "token_report": {
                "description": "Token report generation",
                "endpoints": ["/generate_spot_token_report"]
            },
            "a2a": {
                "description": "Agent-to-Agent protocol endpoints",
                "base_path": "/a2a",
                "endpoints": {
                    "root_info": "/a2a/",
                    "health": "/a2a/health", 
                    "status": "/a2a/status",
                    "rag_agent_card": "/a2a/agent/.well-known/agent.json",
                    "rag_agent_message": "/a2a/agent/",
                    "news_agent_card": "/a2a/news/.well-known/agent.json",
                    "news_agent_message": "/a2a/news/"
                }
            }
        },
        "timestamp": datetime.datetime.now().isoformat()
    }


# Add list all services here to show the a2a service url 
# under /a2a/ 
@app.get("/services")
async def list_services():
    """List all available services and endpoints"""
    return {
        "status": "success",
        "services": {
            "websocket": {
                "description": "Main websocket endpoint for chat sessions",
                "endpoint": "/bdp-search-agent/ws/{session_id}",
                "type": "websocket"
            },
            "health": {
                "description": "Health check endpoints",
                "endpoints": ["/status/health", "/status/ready"]
            },
            "session": {
                "description": "Session management endpoints", 
                "endpoints": ["/session-status", "/session-debug"]
            },
            "trading": {
                "description": "Trading related endpoints",
                "endpoints": ["/recommend-convert-token"]
            },
            "rag": {
                "description": "RAG endpoints for news, market analysis, and campaign analysis",
                "endpoints": ["/rag/query/news", "/rag/query/market", "/rag/query/campaign"]
            },
            "token_report": {
                "description": "Token report generation",
                "endpoints": ["/generate_spot_token_report"]
            },
            "a2a": {
                "description": "Agent-to-Agent protocol endpoints",
                "base_path": "/a2a",
                "endpoints": {
                    "root_info": "/a2a/",
                    "health": "/a2a/health", 
                    "status": "/a2a/status",
                    "rag_agent_card": "/a2a/agent/.well-known/agent.json",
                    "rag_agent_message": "/a2a/agent/",
                    "news_agent_card": "/a2a/news/.well-known/agent.json",
                    "news_agent_message": "/a2a/news/"
                }
            }
        },
        "timestamp": datetime.datetime.now().isoformat()
    }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=13122)
