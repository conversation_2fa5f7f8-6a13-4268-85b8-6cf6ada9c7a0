"""
YMAL Events fetcher: support both environments
- local dev via Conan proxy (requires test-key)
- online/prod direct kb-mgmt
"""
from __future__ import annotations
import requests
from typing import List, Dict, Any, Optional


def _to_ms(ts: Any) -> Optional[int]:
    try:
        if ts is None:
            return None
        ts = int(ts)
        return ts if ts >= 10**12 else ts * 1000
    except Exception:
        return None


def normalize_events(raw: Dict[str, Any]) -> List[Dict[str, Any]]:
    payload = raw.get("data", {}) if isinstance(raw, dict) else {}
    candidates = []
    if isinstance(payload, dict):
        for k in ("results", "items", "data", "list", "documents"):
            if isinstance(payload.get(k), list):
                candidates = payload[k]
                break
    if not candidates and isinstance(raw, dict):
        for k in ("results", "items", "data", "list", "documents"):
            if isinstance(raw.get(k), list):
                candidates = raw[k]
                break

    markers: List[Dict[str, Any]] = []
    for it in candidates or []:
        md = it.get("metadata", {}) if isinstance(it, dict) else {}
        ts = (
            (it.get("timestamp") if isinstance(it, dict) else None)
            or (md.get("timestamp") if isinstance(md, dict) else None)
            or (md.get("time") if isinstance(md, dict) else None)
            or (md.get("publishedAt") if isinstance(md, dict) else None)
            or (md.get("date") if isinstance(md, dict) else None)
            or (it.get("time") if isinstance(it, dict) else None)
        )
        markers.append(
            {
                "timestamp": _to_ms(ts),
                "title": (it.get("title") if isinstance(it, dict) else None)
                or (md.get("title") if isinstance(md, dict) else None)
                or (it.get("text") if isinstance(it, dict) else ""),
                "url": (it.get("url") if isinstance(it, dict) else None)
                or (md.get("url") if isinstance(md, dict) else None)
                or (md.get("source_url") if isinstance(md, dict) else ""),
                "source": (it.get("source") if isinstance(it, dict) else None)
                or (md.get("source") if isinstance(md, dict) else None)
                or "",
                "summary": (it.get("summary") if isinstance(it, dict) else None)
                or (md.get("summary") if isinstance(md, dict) else None)
                or (md.get("content") if isinstance(md, dict) else ""),
            }
        )
    return markers


def fetch_events(token: str, test_key: Optional[str] = None, cfg: Optional[YmalConfig] = None, env: str = "conan") -> Dict[str, Any]:
    """
    env: "conan" (local dev via Conan) or "direct" (online prod via kb-mgmt)
    """
    cfg = cfg or YmalConfig()

    if env == "direct":
        # Direct kb-mgmt
        headers = {"Content-Type": "application/json"}
        body = {
            "collectionName": cfg.events_collection,
            "embeddingModel": cfg.embedding_model,
            "queryText": token,
            "limit": 50,
        }
        resp = requests.post(cfg.kb_mgmt_api, json=body, headers=headers, timeout=20)
        resp.raise_for_status()
        return {"markers": normalize_events(resp.json())}

    # Default: Conan proxy
    headers = {
        "Content-Type": "application/json",
        "test-key": test_key or cfg.default_test_key,
    }
    body = {
        "serviceId": "kb-mgmt",
        "method": "POST",
        "endpoint": "api/vector/search",
        "requestBody": {
            "collectionName": cfg.events_collection,
            "embeddingModel": cfg.embedding_model,
            "queryText": token,
            "limit": 50,
        },
    }
    resp = requests.post(cfg.conan_api, json=body, headers=headers, timeout=20)
    resp.raise_for_status()
    return {"markers": normalize_events(resp.json())}

if __name__=="__main__":
    print(fetch_events("bitcoin", env="conan", test_key='42423db3-df3f-40f6-885d-5ac6a8fd87d7'))