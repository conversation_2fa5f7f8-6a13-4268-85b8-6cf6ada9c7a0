import time
from pathlib import Path
from typing import List, Dict, Optional
import json
import re
import logging
import sys
import os
import argparse
import requests


from dotenv import load_dotenv
sys.path.append(str(Path(__file__).resolve().parent.parent))

# Load env like util/trade_tools/llm.py so GOOGLE_CLOUD_* is available
load_dotenv()

from google import genai
from google.genai import types
from util.logger.logger_utils import setup_logger
import jinja2 
from jinja2 import Environment, FileSystemLoader, select_autoescape

import vertexai
vertexai.init(project=os.environ["GOOGLE_CLOUD_PROJECT"], location=os.environ["GOOGLE_CLOUD_LOCATION"])

logger = setup_logger(__name__)


def _render_jinja(template_filename: str, context: dict) -> str:
    base_dir = Path("agent_YMAL/prompt/gen_query")
    env = Environment(loader=FileSystemLoader(str(base_dir)), autoescape=select_autoescape(["html", "xml"]))
    tmpl = env.get_template(template_filename)
    return tmpl.render(**context)


def _call_llm_vertex(system_instruction: str, user_prompt: str):
    project = (
        os.environ.get("GOOGLE_CLOUD_PROJECT")
        or os.environ.get("GCLOUD_PROJECT")
        or os.environ.get("GCP_PROJECT")
    )
    location = os.environ.get("GOOGLE_CLOUD_LOCATION") or os.environ.get("VERTEX_LOCATION") or "us-central1"
    try:
        client = genai.Client(vertexai=True, project=project, location=location) if project else genai.Client(vertexai=True)
        resp = client.models.generate_content(
            model="gemini-2.5-pro",
            contents=user_prompt,
            config=types.GenerateContentConfig(
                system_instruction=system_instruction
            ),
        )
        return True, resp.text
    except Exception as e:
        return False, str(e)

def _call_llm_qwen_http(system_instruction: str, user_prompt: str, model='Qwen3-4B'):
    api_key = os.getenv("QWEN_API_KEY", "empty")
    api_base = (os.getenv("QWEN_API_BASE") or "http://ds-qwen.devfdg.net").strip().rstrip("/")
    url = f"{api_base}/v1/chat/completions"

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}",
    }
    payload = {
        "model": model,
        "messages": [
            {"role": "system", "content": system_instruction},
            {"role": "user", "content": user_prompt},
        ],
        "temperature": 0.5,
        "max_tokens": 256,
        "enable_thinking": False,
        "thinking": {"type": "disabled"}
    }
    try:
        r = requests.post(url, headers=headers, json=payload, timeout=300)
        data = r.json()
        if "choices" in data:
            choice = data["choices"][0]
            if "message" in choice and "content" in choice["message"]:
                return True, choice["message"]["content"]
            if "text" in choice:
                return True, choice["text"]
        for key in ("output", "content", "text"):
           if isinstance(data.get(key), str):
                return True, data[key]
        return False, f"Unexpected response: {data}"
    except Exception as e:
        return False, str(e)

def _call_llm(system_instruction: str, user_prompt: str, model_name='Qwen3-4B'):
    provider = (os.getenv("YMAL_LLM_PROVIDER") or "").lower()
    if provider == "http" or provider == "qwen":
        return _call_llm_qwen_http(system_instruction, user_prompt, model_name)
    return _call_llm_vertex(system_instruction, user_prompt)


def _normalize_for_dedupe(s: str) -> str:
    """Normalize text for deduplication: casefold, collapse spaces, strip common wrappers."""
    if not isinstance(s, str):
        return ""
    t = s.casefold().strip()
    t = re.sub(r"\s+", " ", t)
    t = re.sub(r"^[\-•\*\t\s]+", "", t)
    t = t.strip('"\'“”‘’')
    return t


def _dedupe_rule_and_suggestions(rule_queries: Optional[List[str]], suggestions: List[str]) -> List[Dict[str, object]]:
    """Merge rule queries and LLM suggestions with deduplication.

    - Keeps rule queries first and marks them via top_show=True
    - Drops suggestions that duplicate any rule query (after normalization)
    - Deduplicates within each source as well
    Returns a list of items: {"text": str, "top_show": bool}
    """
    rule_queries = [q for q in (rule_queries or []) if isinstance(q, str) and q.strip()]
    suggestions = [q for q in (suggestions or []) if isinstance(q, str) and q.strip()]

    seen: set[str] = set()
    items: List[Dict[str, object]] = []

    for q in rule_queries:
        key = _normalize_for_dedupe(q)
        if not key or key in seen:
            continue
        seen.add(key)
        items.append({"text": q.strip(), "top_show": True})

    for q in suggestions:
        key = _normalize_for_dedupe(q)
        if not key or key in seen:
            continue
        seen.add(key)
        items.append({"text": q.strip(), "top_show": False})

    return items


def _filter_against_history(items: List[Dict[str, object]], history: Optional[List[str]]) -> List[Dict[str, object]]:
    """Remove any items whose text matches entries in history (after normalization)."""
    if not history:
        return items
    hist_keys = {_normalize_for_dedupe(h) for h in history if isinstance(h, str)}
    if not hist_keys:
        return items
    out: List[Dict[str, object]] = []
    for it in items:
        txt = (it.get("text") if isinstance(it, dict) else None) or ""
        if _normalize_for_dedupe(txt) in hist_keys:
            continue
        out.append(it)
    return out


def generate_suggestions(token_symbol: str, last_reply: str, target_lang: str = "en", model_name=None, history: List[str] = None, rule_queries: List[str] = None) -> List[Dict[str, object]]:
    """
    Generate "You May Also Like" (YMAL) follow-up questions.

    Args:
        token_symbol: e.g., BTC, ETH
        last_reply: the last response text the user just read
        target_lang: output language code, e.g., en, zh, es
        model_name: optional LLM model name (used for HTTP/Qwen provider)

    Returns:
        A list of items: {"text": str, "top_show": bool}
    """
    t0 = time.time()
    # Render system and user templates via Jinja
    ctx = {
        "token_symbol": token_symbol or "",
        "last_reply": last_reply or "",
        "target_lang": target_lang or "en",
    }
    system_instr = _render_jinja("system_v1.jinja", ctx)
    user_prompt = _render_jinja("user_prompt.jinja", ctx)
    t1 = time.time()

    chosen_model = model_name or os.getenv("QWEN_MODEL") or "Qwen3-4B"
    ok, llm_text = _call_llm(system_instr, user_prompt, chosen_model)
    if not ok:
        logger.error(f"YMAL LLM error: {llm_text}")
        return []
    t2 = time.time()

    # Try to parse a JSON array of strings from the model response
    suggestions: List[str] = []

    # 1) Try direct JSON parse
    try:
        parsed = json.loads(llm_text)
        if isinstance(parsed, list):
            suggestions = [str(x) for x in parsed]
    except Exception:
        pass

    # 2) Fallback: regex extract first JSON array
    if not suggestions:
        m = re.search(r"\[(?:.|\s)*?\]", llm_text)
        if m:
            try:
                parsed = json.loads(m.group(0))
                if isinstance(parsed, list):
                    suggestions = [str(x) for x in parsed]
            except Exception:
                suggestions = []

    # 3) Final fallback: heuristic by lines
    if not suggestions:
        suggestions = [s.strip(" -•\t") for s in llm_text.splitlines() if s.strip()]
        suggestions = suggestions[:5]

    # Merge rule queries and suggestions with deduplication and mark rules
    items = _dedupe_rule_and_suggestions(rule_queries, suggestions)
    t3 = time.time()

    # Filter out items that appear in history
    items = _filter_against_history(items, history)
    t4 = time.time()
    logger.info(
        "generate_suggestions token=%s, timing: render_jinja=%.3fms, call_llm=%.3fms, dedupe=%.3fms, filter=%.3fms, total=%.3fms",
        token_symbol, (t1 - t0) * 1000, (t2 - t1) * 1000, (t3 - t2) * 1000, (t4 - t3) * 1000, (t4 - t0) * 1000,
    )

    return items


def format_reply(reply: dict) -> dict[str, List[str]]:
    """Return dict mapping allowed sections to collected 'text' lists."""

    allowed = {"tldr", "opportunities",  "risks", "community_sentiment"}

    out: dict[str, List[str]] = {k: [] for k in allowed}
    if not isinstance(reply, dict):
        return out

    for k, v in reply.items():
        k_lower = k.lower() if isinstance(k, str) else k
        if not isinstance(k_lower, str) or k_lower not in allowed:
            continue
        bucket = out[k_lower]
        if isinstance(v, dict):
            t = v.get("text")
            if isinstance(t, str):
                s = t.strip()
                if s:
                    bucket.append(s)
        elif isinstance(v, list):
            for item in v:
                if isinstance(item, dict):
                    t = item.get("text")
                    if isinstance(t, str):
                        s = t.strip()
                        if s:
                            bucket.append(s)
        elif isinstance(v, str):
            s = v.strip()
            if s:
                bucket.append(s)
    return out



if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="YMAL suggestions test runner")
    parser.add_argument("--provider", choices=["vertex", "http"], default="http")
    parser.add_argument("--qwen_api_base", default="http://ds-qwen.devfdg.net")
    parser.add_argument("--qwen_api_key", default="empty")
    parser.add_argument("--qwen_model", default="Qwen3-30B-A3B-Instruct-2507")
    args = parser.parse_args()

    if args.provider:
        os.environ["YMAL_LLM_PROVIDER"] = args.provider
    if args.qwen_api_base:
        os.environ["QWEN_API_BASE"] = args.qwen_api_base
    if args.qwen_api_key:
        os.environ["QWEN_API_KEY"] = args.qwen_api_key
    if args.qwen_model:
        os.environ["QWEN_MODEL"] = args.qwen_model




    # python agent_YMAL/gen_query_service.py --provider vertex --symbol WOO --lang en
    # python agent_YMAL/gen_query_service.py --provider http --qwen_api_base http://ds-qwen.devfdg.net --qwen_model Qwen3-4B --qwen_api_key empty --symbol WOO --lang en
    # Tip: you can also set QWEN_API_URL to the full route, e.g. http://host/v1/chat/completions
    cnt = 0
    fw = open("/Users/<USER>/Downloads/test_genquery.jsonl", "w", encoding="utf-8")
    with open("/Users/<USER>/Downloads/1756804603565-lf-observations-export-cmbqu68j5000ezl07mnj40ufq.jsonl", "r", encoding="utf-8") as f:
        for line in f:
            data = json.loads(line)
            content=data['output']['before_report']
            token = data['metadata']['token']
            reply = format_reply(content)
            if reply['tldr']:
                cnt =+1
                out ='\n'.join([f"{k}:\n" + "\n".join(v) + "\n" for k, v in reply.items()])
                suggestions_1 = generate_suggestions(token, out, 'en', 'Qwen3-30B-A3B-Instruct-2507')
                print('-1-', suggestions_1)
                suggestions_2 = generate_suggestions(token, out, 'en', 'Qwen3-4B')
                print('-2-', suggestions_2)
                suggestions_3 = generate_suggestions(token, out, 'en', 'Qwen3-8B')
                print('-3-', suggestions_3)
                fw.write(json.dumps({"token": token, "Qwen3-30B-A3B-Instruct-2507": suggestions_1, "Qwen3-4B": suggestions_2, "Qwen3-8B": suggestions_3, 'reply': out}) + "\n")
                if cnt >30:
                    break
