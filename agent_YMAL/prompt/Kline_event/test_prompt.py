import requests
import json

resp = requests.post(
    "https://your-llm-endpoint",
    headers={"Content-Type": "application/json"},
    json={
        "model": "your-model",
        "messages": [
            {"role": "system", "content": "<put the prompt here>"},
            {"role": "user", "content": "{\"charts\": {\"header\": {}, \"data\": [...]}, \"events\": []}"}
        ],
        "stream": True
    },
    stream=True
)

for line in resp.iter_lines():
    if line:
        try:
            frame = json.loads(line.decode("utf-8"))
            print("Frame:", frame)
        except Exception:
            print("Raw:", line)
