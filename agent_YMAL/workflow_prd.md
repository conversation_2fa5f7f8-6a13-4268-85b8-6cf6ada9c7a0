我先快速浏览你指定的现有文件和目录结构，以便在 PRD 里给出精确的落地方案和调用关系。随后我会直接给出自包含的 PRD。

[5 tools called]

### PRD｜YMAL 消息面与基本面工作流新增（s_yaml_dev）

#### 一、业务背景与目标
- **背景**: 现有 `agent_YMAL` 仅提供通用问答流（`reply_service.py -> answer_stream`），未对“基本面解读”和“社区观点（消息面）”做专门结构化输出。
- **目标**: 在不改动现有框架与其他模块的前提下，仅在你当前分支 `s_yaml_dev` 的 `agent_YMAL` 目录内，新增两个可独立运行的工作流文件，提供高质量、结构化的输出：
  - 基本面：What is {token}?（factsheet）
  - 消息面：What are people saying about {token}?（sentiment）

#### 二、范围与约束
- **只允许新增**：仅在当前分支 `s_yaml_dev` 的 `agent_YMAL` 目录新增文件；不修改其他目录/文件。
- **模型与配置**：使用 Vertex AI（Gemini 2.5 Pro）；你已完成环境配置。
- **Prompt 管理**：新增的两个工作流对应的 prompt 文件需存放在 `agent_YMAL/prompt` 下（对齐 `prompt/Kline_event` 的组织方式）。
- **调用关系**：不接入当前路由（`route_question`），这两个工作流先以可独立运行/测试的模块交付，后续由路由层统一接入。

#### 三、总体架构与调用关系（当前形态）
- 现状（不变）：`ttft_endpoint.py -> reply_service.route_question -> answer_stream -> Vertex`
- 本次新增：两个独立工作流模块，均提供 `run(token: str, target_lang: str="en", enable_search: bool=False) -> str`，便于未来路由集成。
- 提示：参考阅读的现有文件（不改动）
  - `agent_YMAL/workflow_A.py`：事实描述模板
  - `agent_YMAL/workflow_B.py`：社区舆情模板
  - `agent_YMAL/kline_reply.py`：K 线事件摘要
  - `agent_YMAL/prompt/Kline_event/*`：Prompt 目录组织参考

#### 四、目录与文件清单（新增）
- `agent_YMAL/fundamental_what_is.py`（基本面工作流）
- `agent_YMAL/sentiment_what_saying.py`（消息面用户意见工作流）
- `agent_YMAL/prompt/fundamental_what_is/user_prompt.jinja`（基本面 prompt）
- `agent_YMAL/prompt/sentiment_what_saying/user_prompt.jinja`（消息面用户意见 prompt）

说明：
- `*.jinja` 文件仅作为模板文本存储；代码用简单占位符替换（`{{token}}`, `{{target_lang}}`），无需引入 Jinja2 依赖。
- 保持与 `Kline_event` 一致的 prompt 目录风格。

#### 五、详细设计

##### 1) 基本面工作流：`fundamental_what_is.py`
- **业务问题**: “What is {token}?” P0
- **参考来源**:
  - 你的 hexa 目录里已写好 prompt（可直接复用文案）
  - 可显式加入 `Binance Research` 项目页作为提示源，例如 `https://www.binance.com/en/research/projects/newton_protocol`
- **输出结构（Markdown）**:
  - TLDR 代币总览（2-5 句）
  - Key Metrics 核心指标（供应量、流通量、分配、财报等）
  - Utility & Governance（用途、治理与经济模型）
  - Project Updates（近一年时间线；官方 X、官网、指定网址优先）
- **实现要点**:
  - 提供 `run(token, target_lang="en", enable_search=False)`：
    - 读取 `prompt/fundamental_what_is/user_prompt.jinja`，用 `{{token}}`, `{{target_lang}}` 替换。
    - 调用 `google.genai`（Gemini 2.5 Pro）。
    - 可选 `enable_search=True` 时传入 `types.Tool(google_search=...)`，提升外部信息召回（与 `reply_service` 一致）。
  - 语言：统一通过 `target_lang` 控制（默认英文，可传 `"zh"` 输出中文）。
  - 避免做价格预测；严格事实口吻；若信息不足需显式声明。
  - 来源标注：鼓励模型在末尾列出参考来源（若 prompt 里包含该要求）。

- **Prompt 内容来源**:
  - 复用你在 `grok-utool-test/hexa/fundamentals_analyzer.py` 里 `build_fundamentals_prompt` 的结构设计与约束（TLDR/Key metrics/Utility&Governance/Project Updates），将文案落到 `user_prompt.jinja`，占位符改为 `{{token}}`/`{{target_lang}}`。
  - 可在 prompt 中加入“优先官方来源（官网、官方 X、Binance Research）”等检索偏好与合规约束。

##### 2) 消息面工作流：`sentiment_what_saying.py`
- **业务问题**: “What are people saying about {token}?” P0（强化用户观点召回）
- **输出结构（Markdown）**:
  - TLDR：总结用户观点与重要论据
  - Bullish（显示比例，如 48%）：看涨观点与论据；突出价格点位/涨幅空间
  - Bearish：看跌观点与论据；突出价格点位/跌幅空间
  - Neutral：中立观点与论据；突出区间/触发条件
  - 说明：比例为估算（让模型给出近似百分比，总和约等于 100%）
- **实现要点**:
  - `run(token, target_lang="en", enable_search=False)` 同上。
  - Prompt 强调“提炼用户观点与论据（引用原话/简述来源）”，“抽取价格点位与区间”，“输出比例估算（Bullish/Bearish/Neutral）”。
  - 可要求模型在末尾附上来源列表（社媒帖子、新闻链接等）。
- **Prompt 内容来源**:
  - 你在 `grok-utool-test/hexa` 中的消息面 prompt（请落到 `prompt/sentiment_what_saying/user_prompt.jinja`）。如需，我可协助把你现有文案转换为模板版本。

##### 3) 统一 LLM 调用方式
- 参考 `workflow_A/B.py`、`kline_reply.py` 的模式：
  - 内置 `_call_llm_vertex(prompt: str, enable_search: bool)` 小函数
  - `genai.Client(vertexai=True, project, location)`；`GenerateContentConfig` 里可设置 `thinking_config`（预算例如 256）与 `tools`（按 `enable_search` 控制）
- 错误处理：失败返回形如 `[Error] ...` 的字符串（与现有模块一致）
- 日志：可选 `logger`；若无需则从简

##### 4) Prompt 文件读取
- 简单文件读 + 替换：
  - 读取 `user_prompt.jinja`（UTF-8）
  - 将 `{{token}}`、`{{target_lang}}`、（如需）`{{binance_research_url}}` 替换为实参
- 无需引入 Jinja2 依赖，避免修改依赖清单

#### 六、接口定义（对外约定）
- `agent_YMAL/fundamental_what_is.py`
  - `def run(token: str, target_lang: str = "en", enable_search: bool = False) -> str`
- `agent_YMAL/sentiment_what_saying.py`
  - `def run(token: str, target_lang: str = "en", enable_search: bool = False) -> str`
- 返回值：Markdown 文本；失败返回以 `[Error]` 开头的错误信息
- 行为：纯计算型函数，不产生副作用（不改动外部文件/状态）

#### 七、验收标准（Definition of Done）
- 新增文件严格位于 `agent_YMAL` 目录内；不修改任何其他文件/目录。
- 在本地可直接调用（示例）：
  - 基本面：
    ```bash
    python -c "from agent_YMAL.fundamental_what_is import run; print(run('NEWTON', 'en'))"
    ```
  - 消息面：
    ```bash
    python -c "from agent_YMAL.sentiment_what_saying import run; print(run('NEWTON', 'en'))"
    ```
- 输出满足既定结构，段落与要点完整；英文/中文可切换；错误返回清晰。
- Prompt 文件确实落在 `agent_YMAL/prompt/.../user_prompt.jinja` 中，代码通过文件读取。

#### 八、开发任务清单与里程碑
- M1：基础搭建（0.5 天）
  - 新增目录与空文件；实现通用 `_call_llm_vertex`；打通 Vertex 调用
- M2：基本面工作流（1 天）
  - 将 hexa 的基本面 prompt 文案迁移到 `prompt/fundamental_what_is/user_prompt.jinja`
  - 实现 `fundamental_what_is.run`；本地用 `NEWTON` 测试，开启/关闭 `enable_search`
- M3：消息面工作流（1 天）
  - 将你的消息面 prompt 文案迁移到 `prompt/sentiment_what_saying/user_prompt.jinja`
  - 实现 `sentiment_what_saying.run`；本地测试比例估算与价格点位抽取
- M4：稳固与交付（0.5 天）
  - 边界测试、错误处理、输出一致性检查；交付说明文档（README 片段）

#### 九、测试要点
- 功能：
  - 不同 `token` 输入（常见币种/小众币种/拼写边界）；`target_lang` 切换中英
  - `enable_search=True/False` 对输出的“参考来源/更新时效性”的影响
- 质量：
  - 基本面结构是否齐全，指标是否客观、无预测
  - 消息面是否能形成 Bullish/Bearish/Neutral 的比例估算，是否包含关键价格点位/区间
- 稳定性：
  - LLM 错误/超时返回是否规范；未提供 `token` 是否返回空字符串

#### 十、风险与缓解
- 风险：消息面比例“凭空估算”偏差较大
  - 缓解：Prompt 中明确“估算、接近 100%，并列出主要论据与来源”
- 风险：外部站点可访问性/检索不稳定
  - 缓解：默认 `enable_search=False`；必要时再开启
- 风险：符号歧义（同名/相近名 token）
  - 缓解：Prompt 中加入“避免混淆”与识别提示（参考 hexa 的做法）

#### 十一、示例代码骨架（供实现时参考）

```python
# agent_YMAL/fundamental_what_is.py
from pathlib import Path
import os
from google import genai
from google.genai import types

def _read_prompt() -> str:
    p = Path(__file__).resolve().parent / "prompt" / "fundamental_what_is" / "user_prompt.jinja"
    return p.read_text(encoding="utf-8")

def _call_llm_vertex(prompt: str, enable_search: bool = False) -> tuple[bool, str]:
    project = os.getenv("GOOGLE_CLOUD_PROJECT") or os.getenv("GCLOUD_PROJECT") or os.getenv("GCP_PROJECT")
    location = os.getenv("GOOGLE_CLOUD_LOCATION") or os.getenv("VERTEX_LOCATION") or "us-central1"
    client = genai.Client(vertexai=True, project=project, location=location) if project else genai.Client(vertexai=True)
    try:
        resp = client.models.generate_content(
            model="gemini-2.5-pro",
            contents=prompt,
            config=types.GenerateContentConfig(
                thinking_config=types.ThinkingConfig(thinking_budget=256),
                tools=[types.Tool(google_search=types.GoogleSearch())] if enable_search else None,
            ),
        )
        return True, (resp.text or "").strip()
    except Exception as e:
        return False, str(e)

def run(token: str, target_lang: str = "en", enable_search: bool = False) -> str:
    token = (token or "").strip()
    target_lang = (target_lang or "en").strip()
    if not token:
        return ""
    tmpl = _read_prompt()
    # 简单占位符替换
    prompt = tmpl.replace("{{token}}", token).replace("{{target_lang}}", target_lang)
    ok, text = _call_llm_vertex(prompt, enable_search=enable_search)
    return text if ok else f"[Error] {text}"
```

```python
# agent_YMAL/sentiment_what_saying.py
from pathlib import Path
import os
from google import genai
from google.genai import types

def _read_prompt() -> str:
    p = Path(__file__).resolve().parent / "prompt" / "sentiment_what_saying" / "user_prompt.jinja"
    return p.read_text(encoding="utf-8")

def _call_llm_vertex(prompt: str, enable_search: bool = False) -> tuple[bool, str]:
    project = os.getenv("GOOGLE_CLOUD_PROJECT") or os.getenv("GCLOUD_PROJECT") or os.getenv("GCP_PROJECT")
    location = os.getenv("GOOGLE_CLOUD_LOCATION") or os.getenv("VERTEX_LOCATION") or "us-central1"
    client = genai.Client(vertexai=True, project=project, location=location) if project else genai.Client(vertexai=True)
    try:
        resp = client.models.generate_content(
            model="gemini-2.5-pro",
            contents=prompt,
            config=types.GenerateContentConfig(
                thinking_config=types.ThinkingConfig(thinking_budget=256),
                tools=[types.Tool(google_search=types.GoogleSearch())] if enable_search else None,
            ),
        )
        return True, (resp.text or "").strip()
    except Exception as e:
        return False, str(e)

def run(token: str, target_lang: str = "en", enable_search: bool = False) -> str:
    token = (token or "").strip()
    target_lang = (target_lang or "en").strip()
    if not token:
        return ""
    tmpl = _read_prompt()
    prompt = tmpl.replace("{{token}}", token).replace("{{target_lang}}", target_lang)
    ok, text = _call_llm_vertex(prompt, enable_search=enable_search)
    return text if ok else f"[Error] {text}"
```

#### 十二、需要你补充/确认的信息
- **消息面 prompt**：读取 `@/Users/<USER>/Documents/grok-utool-test/hexa` 中的消息面 prompt 文案（落到 `agent_YMAL/prompt/sentiment_what_saying/user_prompt.jinja`）。
- **`enable_search=True`**：两类工作流默认检索开启
- **输出语言默认**：输出语言应该是上层控制，传入 language 参数的，参考现有实现，比如 replay_service。所以 prompt 需要多支持语言指定占位符和相关指令。

如果以上信息无异议，我就按该 PRD 开始在 `s_yaml_dev` 的 `agent_YMAL` 目录内落地两个工作流与对应 prompt 文件。 
- 基本面先行；完成后再照此模式开发消息面。

- 新增文件：`agent_YMAL/fundamental_what_is.py`, `agent_YMAL/sentiment_what_saying.py`；`agent_YMAL/prompt/fundamental_what_is/user_prompt.jinja`, `agent_YMAL/prompt/sentiment_what_saying/user_prompt.jinja`
- 现有框架与路由不改动；输出为 Markdown 文本，便于前端直接渲染。