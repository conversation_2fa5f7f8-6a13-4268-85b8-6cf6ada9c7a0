import re
import json
import time
from typing import AsyncGenerator, Optional, Dict, List

from pathlib import Path
import sys
import os
# Make project root importable
sys.path.append(str(Path(__file__).resolve().parent.parent))

from dotenv import load_dotenv
from util.logger.logger_utils import setup_logger

# Load env for GOOGLE_CLOUD_*
load_dotenv()

from google import genai
from google.genai import types

# Initialize Vertex if envs exist
try:
    import vertexai
    if os.getenv("GOOGLE_CLOUD_PROJECT") and os.getenv("GOOGLE_CLOUD_LOCATION"):
        vertexai.init(project=os.environ["GOOGLE_CLOUD_PROJECT"], location=os.environ["GOOGLE_CLOUD_LOCATION"])
except Exception:
    pass

logger = setup_logger(__name__)


def _call_llm_vertex(system_instruction: str, contents: str, enable_search: bool = False):
    project = (
        os.environ.get("GOOGLE_CLOUD_PROJECT")
        or os.environ.get("GCLOUD_PROJECT")
        or os.environ.get("GCP_PROJECT")
    )
    location = os.environ.get("GOOGLE_CLOUD_LOCATION") or os.environ.get("VERTEX_LOCATION") or "us-central1"
    try:
        client = genai.Client(vertexai=True, project=project, location=location) if project else genai.Client(vertexai=True)
        response = client.models.generate_content(
            model="gemini-2.5-pro",
            contents=contents,
            config=types.GenerateContentConfig(
                system_instruction=system_instruction,
                thinking_config=types.ThinkingConfig(thinking_budget=-1),
                tools=[types.Tool(google_search=types.GoogleSearch())] if enable_search else None,
            ),
        )
        return True, response.text
    except Exception as exc:
        return False, str(exc)


def _call_llm_qwen_http(system_instruction: str, user_prompt: str, model: str = "Qwen3-4B"):
    """
    Call a Qwen-compatible HTTP endpoint (OpenAI style /v1/chat/completions).
    Controlled by envs:
      - `QWEN_API_BASE` (e.g. http://host:port)
      - `QWEN_API_KEY`
    """
    import requests  # local import to avoid hard dep when unused

    api_key = os.getenv("QWEN_API_KEY", "empty")
    api_base = (os.getenv("QWEN_API_BASE") or "http://ds-qwen.devfdg.net").strip().rstrip("/")
    url = f"{api_base}/v1/chat/completions"

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}",
    }
    payload = {
        "model": model,
        "messages": [
            {"role": "system", "content": system_instruction},
            {"role": "user", "content": user_prompt},
        ],
        "temperature": 0.3,
        "max_tokens": 512,
        "enable_thinking": False,
        "thinking": {"type": "disabled"},
    }
    try:
        http_response = requests.post(url, headers=headers, json=payload, timeout=300)
        response_json = http_response.json()
        # Try common fields
        if isinstance(response_json, dict) and "choices" in response_json:
            choice = response_json["choices"][0] if response_json.get("choices") else None
            if isinstance(choice, dict):
                message_obj = choice.get("message") or {}
                if isinstance(message_obj, dict) and isinstance(message_obj.get("content"), str):
                    return True, message_obj["content"]
                if isinstance(choice.get("text"), str):
                    return True, choice["text"]
        # Some servers return plain {text|content|output}
        for key in ("output", "content", "text"):
            value = response_json.get(key)
            if isinstance(value, str):
                return True, value
        return False, f"Unexpected response: {response_json}"
    except Exception as exc:
        return False, str(exc)


def answer_once(query: str, target_lang: str = "en", enable_search: bool = False) -> str:
    """
    Produce a single-turn answer for the given query in target_lang.
    Returns the text (error message prefixed with [Error] on failure).
    """
    query = (query or "").strip()
    target_lang = (target_lang or "en").strip()
    if not query:
        return ""

    system_instruction = (
        "You are a market-focused crypto assistant. "
        "Answer the user's single question concisely and objectively. "
        "Avoid financial advice, prediction, and unnecessary boilerplate. "
        "Do not discuss politics, public policy, or geopolitical topics. "
        "Do not reference, compare, or comment on Binance's competitors; avoid naming other exchanges or platforms. "
        "If the user asks about these restricted topics, briefly decline and redirect to neutral, platform-agnostic market information. "
        "Always respond fully in the target language."
    )

    # Simple flat content format; no JSON needed
    user_prompt = f"Inputs:\n- Query: {query}\n- TargetLang: {target_lang}"
    success, result_text = _call_llm_vertex(system_instruction, user_prompt, enable_search=enable_search)
    if not success:
        logger.error(f"YMAL reply LLM error: {result_text}")
        return f"[Error] {result_text}"
    return result_text


def answer_once_with_model(query: str, target_lang: str, model_name: Optional[str] = None, provider: Optional[str] = None) -> str:
    """Answer a single query with a specific model provider.

    - If provider is 'http' or 'qwen', calls a Qwen-compatible endpoint using `model_name`.
    - Otherwise falls back to Vertex (Gemini) using the local prompt style.
    """
    query = (query or "").strip()
    target_lang = (target_lang or "en").strip()
    if not query:
        return ""

    system_instruction = (
        "You are a market-focused crypto assistant. "
        "Answer the user's single question concisely and objectively. "
        "Avoid financial advice, prediction, and unnecessary boilerplate. "
        "Do not discuss politics, public policy, or geopolitical topics. "
        "Do not reference, compare, or comment on Binance's competitors; avoid naming other exchanges or platforms. "
        "If the user asks about these restricted topics, briefly decline and redirect to neutral, platform-agnostic market information. "
        "Always respond fully in the target language."
    )

    user_prompt = f"Inputs:\n- Query: {query}\n- TargetLang: {target_lang}"

    provider_name = (provider or os.getenv("YMAL_LLM_PROVIDER") or "").lower()
    if provider_name in ("http", "qwen"):
        chosen_model = model_name or os.getenv("QWEN_MODEL") or "Qwen3-4B"
        success, result_text = _call_llm_qwen_http(system_instruction, user_prompt, chosen_model)
        if not success:
            logger.error(f"Qwen HTTP error: {result_text}")
            return f"[Error] {result_text}"
        return result_text

    success, result_text = _call_llm_vertex(system_instruction, user_prompt, enable_search=False)
    if not success:
        logger.error(f"Vertex LLM error: {result_text}")
        return f"[Error] {result_text}"
    return result_text

async def _stream_llm_vertex(
    contents: str,
    system_instruction: Optional[str] = None,
    enable_search: bool = False,
    response_id: Optional[str] = None,
) -> AsyncGenerator[Dict[str, str], None]:
    """Stream text, thoughts, and optional search info from Vertex Gemini.

    Yields formatted chunks:
    - "[TEXT] ..." for normal output tokens
    - "[THOUGHT] ..." for model thoughts (if enabled)
    - trailing sections with [SEARCH QUERIES] and [SOURCES] when available
    """

    # Build client from env (supports default project discovery)
    project = (
        os.environ.get("GOOGLE_CLOUD_PROJECT")
        or os.environ.get("GCLOUD_PROJECT")
        or os.environ.get("GCP_PROJECT")
    )
    location = os.environ.get("GOOGLE_CLOUD_LOCATION") or os.environ.get("VERTEX_LOCATION") or "us-central1"
    client = genai.Client(vertexai=True, project=project, location=location) if project else genai.Client(vertexai=True)

    # Request config: enable thinking stream and optional Google Search tool
    generation_config = types.GenerateContentConfig(
        system_instruction=system_instruction,
        thinking_config=types.ThinkingConfig(thinking_budget=-1, include_thoughts=True),
        tools=[types.Tool(google_search=types.GoogleSearch())] if enable_search else None,
    )

    def extract_text(event) -> Optional[str]:
        # Prefer top-level text if present
        top_level_text = getattr(event, "text", None)
        if top_level_text:
            return top_level_text
        # Otherwise join non-thought parts' text
        content = getattr(event, "content", None)
        parts = getattr(content, "parts", None) or []
        segments = [getattr(part, "text", None) for part in parts if not getattr(part, "thought", False)]
        segments = [segment for segment in segments if segment]
        return "".join(segments) if segments else None

    def iter_thoughts(event):
        candidate = (getattr(event, "candidates", None) or [None])[0]
        content = getattr(candidate, "content", None)
        parts = getattr(content, "parts", None) or []
        for part in parts:
            thought_text = getattr(part, "text", None)
            if getattr(part, "thought", False) and thought_text:
                yield thought_text

    last_grounding_metadata = None

    # Stream tokens and capture grounding metadata for sources/search queries
    async for event in await client.aio.models.generate_content_stream(
        model="gemini-2.5-pro",
        contents=contents,
        config=generation_config,
    ):
        text = extract_text(event)
        if text:
            yield {"type": "Content", "message": text, "id": response_id or ""}

        for thought_text in iter_thoughts(event):
            yield {"type": "Thought", "message": thought_text, "id": response_id or ""}

        candidate = (getattr(event, "candidates", None) or [None])[0]
        grounding_metadata = getattr(candidate, "grounding_metadata", None) if candidate else None
        if grounding_metadata:
            last_grounding_metadata = grounding_metadata

    # Emit search info and sources after stream ends (if available)
    if enable_search and last_grounding_metadata:
        # queries = getattr(last_grounding_metadata, "web_search_queries", None) or []
        chunks = getattr(last_grounding_metadata, "grounding_chunks", None)
        if chunks:
            for index, chunk in enumerate(chunks):
                web = getattr(chunk, "web", None)
                uri = getattr(web, "uri", None) if web else None
                if uri:
                    title = getattr(web, "title", None) or uri
                    yield {"type": "Refereces", "message": f"[{index+1}] {title} - {uri}", "id": response_id or ""}



async def answer_stream(
    query: str,
    target_lang: str = "en",
    enable_search: bool = False,
    response_id: Optional[str] = None,
    last_reply: Optional[str] = None,
) -> AsyncGenerator[Dict[str, str], None]:
    """Stream the answer as structured chunks with type/message/id."""
    query = (query or "").strip()
    target_lang = (target_lang or "en").strip()
    if not query:
        return

    system_instruction = (
        "You are a market-focused crypto assistant. "
        "Answer the user's single question concisely and objectively. "
        "Avoid financial advice, prediction, and unnecessary boilerplate. "
        "Do not discuss politics, public policy, or geopolitical topics. "
        "Do not reference, compare, or comment on Binance's competitors; avoid naming other exchanges or platforms. "
        "If the user asks about these restricted topics, briefly decline and redirect to neutral, platform-agnostic market information. "
        "Always respond fully in the target language."
    )

    prompt_contents = f"Inputs:\n- Query: {query}\n- TargetLang: {target_lang}"

    try:
        async for chunk_text in _stream_llm_vertex(
            contents=prompt_contents,
            system_instruction=system_instruction,
            enable_search=enable_search,
            response_id=response_id,
        ):
            if chunk_text:
                yield chunk_text
    except Exception as exc:
        yield {"type": "Content", "message": f"[Error] {exc}", "id": response_id or ""}


def _classify_question(question: str, token: str) -> Optional[str]:
    """
    Classify question type for workflow routing.

    Args:
        question: User question string
        token: Token parameter from the request

    Returns:
        Workflow type string or None if no match:
        - "fundamental": For "What is {token}?" questions
        - "sentiment": For "What are people saying about {token}?" questions
        - None: Route to general LLM
    """
    if not token or token.upper() == "NONE":
        return None

    question_lower = question.lower().strip()

    # Fundamental analysis: "What is ${token}?"
    if re.match(r'^what\s+is\s+\$[a-zA-Z0-9]+\?$', question_lower):
        return "fundamental"

    # Sentiment analysis: "What are people saying about ${token}?"
    if re.match(r'^what\s+are\s+people\s+saying\s+about\s+\$[a-zA-Z0-9]+\?$', question_lower):
        return "sentiment"

    # Add more question types here as needed
    # Example: Price analysis, technical analysis, etc.

    return None


async def _run_workflow(workflow_type: str, token: str, target_lang: str, response_id: Optional[str]) -> AsyncGenerator[Dict[str, str], None]:
    """
    Run the specified workflow with given parameters.

    Args:
        workflow_type: Type of workflow ("fundamental", "sentiment", etc.)
        token: Token parameter
        target_lang: Target language
        response_id: Optional response ID

    Yields:
        Workflow output chunks
    """
    try:
        if workflow_type == "fundamental":
            from .fundamental_what_is import run_stream
        elif workflow_type == "sentiment":
            from .sentiment_what_saying import run_stream
        else:
            yield {"type": "Content", "message": f"[Error] Unknown workflow type: {workflow_type}", "id": response_id or ""}
            return

        async for chunk in run_stream(
            token=token,
            target_lang=target_lang,
            enable_search=True,
            response_id=response_id
        ):
            if chunk:
                yield chunk

    except Exception as e:
        logger.error(f"{workflow_type.title()} workflow failed: {e}")
        yield {"type": "Content", "message": f"[Error] {workflow_type.title()} workflow failed: {str(e)}", "id": response_id or ""}


async def route_question(question: str, token: str = "None", target_lang: str = "en", en_query=None, response_id=None, last_reply=None) -> AsyncGenerator[Dict[str, str], None]:
    """
    Route questions to appropriate workflows or fallback to general LLM.

    Args:
        question: User question
        token: Token symbol (optional)
        target_lang: Target language for response
        en_query: English query template (optional)
        response_id: Response ID for tracking
        last_reply: Previous reply context (optional)

    Yields:
        Streaming response chunks
    """
    normalized_question = re.sub(r"\s+", " ", question).strip()

    # Classify question type and route to appropriate workflow
    workflow_type = _classify_question(normalized_question, token)

    if workflow_type:
        async for chunk in _run_workflow(workflow_type, token, target_lang, response_id):
            if chunk:
                yield chunk
        return

    # Fallback to general LLM streaming
    async for chunk in answer_stream(normalized_question, target_lang, enable_search=False, response_id=response_id, last_reply=last_reply):
        if chunk:
            yield chunk



if __name__ == "__main__":
    import asyncio

    async def _demo():
        demo_question = "What is the difference between FDV and circulating market cap?"
        print("[streaming]", flush=True)
        async for chunk in answer_stream(demo_question, 'en', enable_search=True, response_id="demo-1"):
            print(json.dumps(chunk, ensure_ascii=False), flush=True)
        print("\n[done]", flush=True)

    asyncio.run(_demo())
