#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTFT benchmark for an OpenAI-compatible endpoint.
- Uses only /chat/completions (per project setup).
- Supports SSE (text/event-stream) and non-SSE (TTFB≈TTFT fallback).
- Reports Avg / P50 / P90 / Min / Max.
- Supports warmup, runs, simple concurrency.
"""

import argparse, asyncio, json, math, statistics, time, random
from typing import Optional
import aiohttp
from transformers import AutoTokenizer

DEFAULT_BASE = "http://ds-qwen.devfdg.net/v1"
DEFAULT_MODEL = "Qwen3-30B-A3B-Instruct-2507"

 

async def try_once(session, url, body, tok=None, input_tokens_cli=None):
    # 新实现：包含 input/output 长度统计（移除无用兼容逻辑）

    t0 = time.perf_counter()
    async with session.post(url, json=body) as resp:
        is_sse = (resp.headers.get("Content-Type", "").lower()
                  .startswith("text/event-stream"))

        ttft_ms = None
        t_end = None
        token_ts = []
        input_tokens = None
        output_tokens = None
        collected = []
        out_pieces = []
        out_full_text = ""

        async for chunk, _ in resp.content.iter_chunks():
            if not chunk:
                continue
            now = time.perf_counter()
            if ttft_ms is None:
                ttft_ms = (now - t0) * 1000.0

            if is_sse:
                text = chunk.decode("utf-8", "ignore")
                for event in text.split("\n\n"):
                    if not event.strip():
                        continue
                    line = event.strip()
                    if not line.startswith("data:"):
                        continue
                    data = line[5:].strip()
                    if data == "[DONE]":
                        t_end = now
                        break
                    try:
                        ev = json.loads(data)
                    except Exception:
                        ev = None
                    if isinstance(ev, dict):
                        u = ev.get("usage") or ev.get("x_usage")
                        if isinstance(u, dict):
                            input_tokens = input_tokens or u.get("prompt_tokens") or u.get("input_tokens")
                            output_tokens = output_tokens or u.get("completion_tokens") or u.get("output_tokens")
                        ch = ev.get("choices")
                        if isinstance(ch, list) and ch:
                            delta = (ch[0] or {}).get("delta", {})
                            seg = delta.get("content") or delta.get("text")
                            if isinstance(seg, str):
                                out_pieces.append(seg)
                                token_ts.append(now)
                        # 专注 chat.completions 风格，不再兼容其他事件类型
                if t_end is not None:
                    break
            else:
                collected.append(chunk)

        if t_end is None:
            t_end = time.perf_counter()

        if not is_sse and collected:
            try:
                body_bytes = b"".join(collected)
                obj = json.loads(body_bytes.decode("utf-8", "ignore"))
                if isinstance(obj, dict):
                    u = obj.get("usage") or obj.get("x_usage")
                    if isinstance(u, dict):
                        input_tokens = input_tokens or u.get("prompt_tokens") or u.get("input_tokens")
                        output_tokens = output_tokens or u.get("completion_tokens") or u.get("output_tokens")
                    out_text = None
                    ch = obj.get("choices")
                    if isinstance(ch, list) and ch:
                        msg = (ch[0] or {}).get("message", {})
                        if isinstance(msg, dict) and isinstance(msg.get("content"), str):
                            out_text = msg.get("content")
                        if out_text is None:
                            t = (ch[0] or {}).get("text")
                            if isinstance(t, str):
                                out_text = t
                    if out_text is None:
                        t = obj.get("output_text")
                        if isinstance(t, str):
                            out_text = t
                    if out_text is None and isinstance(obj.get("content"), str):
                        out_text = obj.get("content")
                    if isinstance(out_text, str):
                        out_full_text = out_text
            except Exception:
                pass

        # 拼接增量文本，供可选的输出 token 统计使用
        if out_pieces:
            out_full_text = "".join(out_pieces)

        e2e_ms = (t_end - t0) * 1000.0
        itls_ms = [(token_ts[i] - token_ts[i - 1]) * 1000.0 for i in range(1, len(token_ts))]
        tpot_ms = (sum(itls_ms) / len(itls_ms)) if itls_ms else 0.0

        # Fallback: if server didn't return usage tokens
        if input_tokens is None and input_tokens_cli is not None:
            input_tokens = input_tokens_cli
        if output_tokens is None and tok and isinstance(out_full_text, str) and out_full_text:
            try:
                output_tokens = len(tok(out_full_text, add_special_tokens=False).input_ids)
            except Exception:
                pass

        return {
            "ttft_ms": ttft_ms or 0.0,
            "e2e_ms": e2e_ms,
            "itls_ms": itls_ms,
            "tpot_ms": tpot_ms,
            "is_sse": is_sse,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
        }


def _with_output_hint(prompt: str, lo: int, hi: int) -> str:
    mid = max(lo, min(hi, (lo + hi) // 2))
    return (
        f"{prompt}\n\n"
        f"请将你的回答长度控制在{lo}到{hi}个token左右，尽量接近{mid}个token。Do not include <think> or reasoning traces in your reply. Only output the final answer."
    )


async def detect_route(base: str, model: str, prompt: str, auth: Optional[str], timeout: int, try_no_auth: bool,
                      output_tokens_min: int, output_tokens_max: int, output_hint: bool):
    # 精简：仅使用 /chat/completions 一条路由，OpenAI 兼容最常见
    headers_common = {"Content-Type": "application/json", "Accept": "text/event-stream"}
    path = "/chat/completions"
    # 探测阶段使用上界，确保足够长
    if output_hint:
        prompt = _with_output_hint(prompt, output_tokens_min, output_tokens_max)
    body = {"model": model, "stream": True, "temperature": 0,
            "max_tokens": max(output_tokens_min, output_tokens_max),
            "stream_options": {"include_usage": True},
            "messages": [{"role": "user", "content": prompt}]}
    for with_auth in ([True, False] if try_no_auth else [True]):
        headers = dict(headers_common)
        if with_auth and auth:
            headers["Authorization"] = f"Bearer {auth}"
        url = base.rstrip("/") + path
        try:
            async with aiohttp.ClientSession(headers=headers, timeout=aiohttp.ClientTimeout(total=timeout)) as sess:
                result = await try_once(sess, url, body)
                return result, url, body, headers
        except Exception:
            continue
    raise RuntimeError("/chat/completions route didn't work. Check base or auth.")

async def run_bench(base: str, model: str, auth: str, runs: int, warmup: int, timeout: int,
                    concurrency: int, prompt: str, try_no_auth: bool, metric: str, tok=None, input_tokens_cli=None,
                    output_tokens_min: int = 40, output_tokens_max: int = 60, output_hint: bool = False):
    # 先探测可用路由
    result0, url, body_proto, headers = await detect_route(
        base=base, model=model, prompt=prompt, auth=auth, timeout=timeout, try_no_auth=try_no_auth,
        output_tokens_min=output_tokens_min, output_tokens_max=output_tokens_max, output_hint=output_hint
    )
    print(f"[Detect] url={url}")
    print(f"[Detect] SSE={result0['is_sse']}  TTFT~={result0['ttft_ms']:.1f} ms  E2E~={result0['e2e_ms']:.1f} ms")

    # 预热
    async with aiohttp.ClientSession(headers=headers, timeout=aiohttp.ClientTimeout(total=timeout)) as sess:
        for _ in range(max(0, warmup)):
            try:
                await try_once(sess, url, body_proto, tok, input_tokens_cli)
            except Exception:
                pass

        # 并发执行 runs 次
        sem = asyncio.Semaphore(concurrency) if (concurrency and concurrency > 1) else None
        ttft_samples = []
        e2e_samples = []
        input_tokens_samples = []
        output_tokens_samples = []


        async def do_one(i: int):
            async with (sem or _null_async_context()):
                # 每次运行选择一个 max_tokens 范围内的值
                lo, hi = sorted((output_tokens_min, output_tokens_max))
                max_toks = random.randint(lo, hi)
                body = dict(body_proto)
                body["max_tokens"] = max_toks
                if output_hint:
                    # 确保每次请求带有长度提示（detect 时已带，但这里稳妥重置）
                    body["messages"] = [{"role": "user", "content": _with_output_hint(prompt, lo, hi)}]
                return await try_once(sess, url, body, tok, input_tokens_cli)

        tasks = [asyncio.create_task(do_one(i)) for i in range(runs)]
        for coro in asyncio.as_completed(tasks):
            try:
                r = await coro
                if r.get("ttft_ms") is not None:
                    ttft_samples.append(r["ttft_ms"])
                if r.get("e2e_ms") is not None:
                    e2e_samples.append(r["e2e_ms"])
                # 收集长度统计
                if r.get("input_tokens") is not None:
                    input_tokens_samples.append(r["input_tokens"])
                if r.get("output_tokens") is not None:
                    output_tokens_samples.append(r["output_tokens"])
                # 单次输出
                parts = []
                if metric in ("ttft", "both"):
                    parts.append(f"TTFT={r['ttft_ms']:.1f} ms")
                if metric in ("e2e", "both"):
                    parts.append(f"E2E={r['e2e_ms']:.1f} ms")
                idx = len(ttft_samples) if metric != "e2e" else len(e2e_samples)
                print(f"Run {idx}/{runs}: " + "  ".join(parts))
            except Exception as e:
                print(f"Run ERROR: {e}")

    if not (ttft_samples or e2e_samples):
        raise RuntimeError("No successful samples. Check gateway prefix or auth.")


    def summarize(label: str, xs):
        xs = sorted(xs)
        p50 = statistics.median(xs)
        p90 = xs[max(0, math.floor(0.9 * len(xs)) - 1)]
        avg = statistics.mean(xs)
        # 仅按 tokens 计算平均长度
        def avg_len(ts):
            if ts:
                return f"Avg Input Length: {sum(ts)/len(ts):.1f} tokens"
            return "Avg Input Length: N/A"
        def avg_out_len(ts):
            if ts:
                return f"Avg Output Length: {sum(ts)/len(ts):.1f} tokens"
            return "Avg Output Length: N/A"
        print(f"\n===== {label} Summary =====")
        print(f"Endpoint : {url}")
        print(f"Model    : {model}")
        print(f"Samples  : {len(xs)}")
        print(f"Avg      : {avg:.1f} ms")
        print(f"P50      : {p50:.1f} ms")
        print(f"P90      : {p90:.1f} ms")
        print(f"Min/Max  : {xs[0]:.1f} / {xs[-1]:.1f} ms")
        if label == "E2E":
            print(avg_len(input_tokens_samples))
            print(avg_out_len(output_tokens_samples))

    if metric in ("ttft", "both") and ttft_samples:
        summarize("TTFT", ttft_samples)
    if metric in ("e2e", "both") and e2e_samples:
        summarize("E2E", e2e_samples)

class _null_async_context:
    async def __aenter__(self): return None
    async def __aexit__(self, exc_type, exc, tb): return False

def main():
    ap = argparse.ArgumentParser(description="TTFT/E2E benchmark for OpenAI-compatible endpoint")
    ap.add_argument("--base", default=DEFAULT_BASE, help="Base URL, e.g., http://host/v1")
    ap.add_argument("--model", default=DEFAULT_MODEL, help="Model ID shown by your server")
    ap.add_argument("--auth", default="empty", help='Bearer token (use "" to omit header)')
    ap.add_argument("--runs", type=int, default=20, help="Number of measured runs")
    ap.add_argument("--warmup", type=int, default=3, help="Warmup requests")
    ap.add_argument("--timeout", type=int, default=60, help="Per-request timeout (s)")
    ap.add_argument("--concurrency", type=int, default=5, help="Concurrent requests during measurement")
    ap.add_argument("--try-no-auth", action="store_true", help="Also probe without Authorization header")
    ap.add_argument("--prompt", default="Say one short sentence.", help="Short prompt to minimize precompute")
    ap.add_argument("--input-tokens", type=int, default=None,
                    help="Target input length by tokens (client-side tokenization).")
    ap.add_argument("--tokenizer", type=str, default="Qwen/Qwen2.5-7B-Instruct",
                    help="HF tokenizer id; use one close to your served model.")
    ap.add_argument("--metric", choices=["ttft", "e2e", "both"], default="both",
                    help="Which metric to summarize: TTFT, end-to-end, or both.")
    ap.add_argument("--output-hint", action="store_true",
                    help="Append instruction to target output length in [min,max] tokens.")
    ap.add_argument("--output-tokens-min", type=int, default=40,
                    help="Min output tokens per request (inclusive).")
    ap.add_argument("--output-tokens-max", type=int, default=60,
                    help="Max output tokens per request (inclusive).")
    args = ap.parse_args()

    def build_prompt_by_tokens(base_text: str, n_tokens: int, tok_name: str) -> str:
        filler = "In this task, consider the context and think carefully. "
        try:
            tok = AutoTokenizer.from_pretrained(tok_name, trust_remote_code=True)
            buf = base_text + " "
            while len(tok(buf).input_ids) < n_tokens + 4:  # +4 for specials headroom
                buf += filler
            ids = tok(buf, add_special_tokens=False).input_ids[:n_tokens]
            return tok.decode(ids, skip_special_tokens=True)
        except Exception:
            # Fallback: approximate tokens by characters (≈4 chars/token)
            approx_chars = max(0, n_tokens * 4)
            buf = base_text + " "
            while len(buf) < approx_chars:
                buf += filler
            return buf[:approx_chars]

    gen_prompt = args.prompt
    if args.input_tokens:
        gen_prompt = build_prompt_by_tokens(args.prompt, args.input_tokens, args.tokenizer)

    # Initialize tokenizer once for fallback token counting
    try:
        tok = AutoTokenizer.from_pretrained(args.tokenizer, trust_remote_code=True)
    except Exception:
        tok = None

    asyncio.run(run_bench(args.base, args.model, args.auth, args.runs, args.warmup,
                      args.timeout, args.concurrency, gen_prompt, args.try_no_auth, args.metric,
                      tok, args.input_tokens, args.output_tokens_min, args.output_tokens_max, args.output_hint))
if __name__ == "__main__":
    main()
