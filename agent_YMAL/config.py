from dataclasses import dataclass
import os

@dataclass
class YmalConfig:
    # <PERSON> proxy (local dev)
    conan_api: str = os.getenv("CONAN_API", "https://conan.toolsfdg.net/private/eureka/api/request")
    default_test_key: str = os.getenv("CONAN_TEST_KEY", "")
    # Direct kb-mgmt (online/prod)
    kb_mgmt_api: str = os.getenv("YMAL_KB_MGMT_API", "http://kb-mgmt.eureka.dev.local:10999/api/vector/search")
    # Common
    events_collection: str = os.getenv("YMAL_EVENTS_COLLECTION", "bigdata-token-report-token-events")
    embedding_model: str = os.getenv("YMAL_EMBED_MODEL", "qwen3_embedding_8b")

