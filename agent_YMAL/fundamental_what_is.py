"""
Fundamental analysis workflow for crypto tokens.
Provides structured analysis of "What is {token}?" with factual, mobile-optimized output.
"""
from pathlib import Path
import sys
import os
from typing import <PERSON>ple
from dotenv import load_dotenv
from google import genai
from google.genai import types

# Ensure project root importable and env loaded
sys.path.append(str(Path(__file__).resolve().parent.parent))
load_dotenv()

try:
    import vertexai
    if os.getenv("GOOGLE_CLOUD_PROJECT") and os.getenv("GOOGLE_CLOUD_LOCATION"):
        vertexai.init(project=os.environ["GOOGLE_CLOUD_PROJECT"], location=os.environ["GOOGLE_CLOUD_LOCATION"])
except Exception:
    pass


def _read_prompt() -> str:
    """Read the fundamental analysis prompt template."""
    prompt_path = Path(__file__).resolve().parent / "prompt" / "fundamental_what_is" / "user_prompt.jinja"
    return prompt_path.read_text(encoding="utf-8")


def _call_llm_vertex(prompt: str, enable_search: bool = False) -> <PERSON><PERSON>[bool, str]:
    """
    Call Vertex AI Gemini 2.5 Pro with the given prompt.
    
    Args:
        prompt: The formatted prompt string
        enable_search: Whether to enable Google Search tool
        
    Returns:
        Tuple of (success: bool, result: str)
    """
    # Get project and location from environment variables
    project = (
        os.getenv("GOOGLE_CLOUD_PROJECT") 
        or os.getenv("GCLOUD_PROJECT") 
        or os.getenv("GCP_PROJECT")
    )
    location = (
        os.getenv("GOOGLE_CLOUD_LOCATION") 
        or os.getenv("VERTEX_LOCATION") 
        or "us-central1"
    )
    
    try:
        # Initialize Vertex AI client
        client = genai.Client(vertexai=True, project=project, location=location) if project else genai.Client(vertexai=True)
        
        # Configure generation parameters
        config = types.GenerateContentConfig(
            thinking_config=types.ThinkingConfig(thinking_budget=256),
            tools=[types.Tool(google_search=types.GoogleSearch())] if enable_search else None,
        )
        
        # Generate content
        response = client.models.generate_content(
            model="gemini-2.5-pro",
            contents=prompt,
            config=config,
        )
        
        return True, (response.text or "").strip()
        
    except Exception as e:
        return False, str(e)


def run(token: str, target_lang: str = "en", enable_search: bool = False) -> str:
    """
    Generate fundamental analysis for a given token.
    
    Args:
        token: Token symbol (e.g., "BTC", "NEWTON")
        target_lang: Target language for output ("en" for English, "zh" for Chinese)
        enable_search: Whether to enable Google Search for enhanced information retrieval
        
    Returns:
        Markdown-formatted fundamental analysis or error message starting with "[Error]"
    """
    # Input validation and normalization
    token = (token or "").strip()
    target_lang = (target_lang or "en").strip()
    
    if not token:
        return ""
    
    try:
        # Read and format the prompt template
        template = _read_prompt()
        
        # Simple placeholder replacement (no Jinja2 dependency)
        prompt = template.replace("{{token}}", token).replace("{{target_lang}}", target_lang)
        
        # Call LLM
        success, result = _call_llm_vertex(prompt, enable_search=enable_search)
        
        if success:
            return result
        else:
            return f"[Error] {result}"
            
    except Exception as e:
        return f"[Error] Failed to process fundamental analysis: {str(e)}"


if __name__ == "__main__":
    # Simple test when run directly
    import sys
    
    if len(sys.argv) > 1:
        test_token = sys.argv[1]
        test_lang = sys.argv[2] if len(sys.argv) > 2 else "en"
        test_search = sys.argv[3].lower() == "true" if len(sys.argv) > 3 else False
        
        print(f"Testing fundamental analysis for {test_token} (lang: {test_lang}, search: {test_search})")
        print("=" * 80)
        result = run(test_token, test_lang, test_search)
        print(result)
    else:
        print("Usage: python fundamental_what_is.py <token> [lang] [enable_search]")
        print("Example: python fundamental_what_is.py NEWTON en true")
