"""
Fundamental analysis workflow for crypto tokens.
Provides structured analysis of "What is {token}?" with factual, mobile-optimized output.
"""
from pathlib import Path
import sys
import os
from typing import <PERSON><PERSON>, AsyncGenerator, Optional, Dict
from dotenv import load_dotenv
from google import genai
from google.genai import types

# Ensure project root importable and env loaded
sys.path.append(str(Path(__file__).resolve().parent.parent))
load_dotenv()

try:
    import vertexai
    if os.getenv("GOOGLE_CLOUD_PROJECT") and os.getenv("GOOGLE_CLOUD_LOCATION"):
        vertexai.init(project=os.environ["GOOGLE_CLOUD_PROJECT"], location=os.environ["GOOGLE_CLOUD_LOCATION"])
except Exception:
    pass


def _read_prompt() -> str:
    """Read the fundamental analysis prompt template."""
    prompt_path = Path(__file__).resolve().parent / "prompt" / "fundamental_what_is" / "user_prompt.jinja"
    return prompt_path.read_text(encoding="utf-8")


def _call_llm_vertex(prompt: str, enable_search: bool = False) -> Tuple[bool, str]:
    """
    Call Vertex AI Gemini 2.5 Pro with the given prompt.
    
    Args:
        prompt: The formatted prompt string
        enable_search: Whether to enable Google Search tool
        
    Returns:
        Tuple of (success: bool, result: str)
    """
    # Get project and location from environment variables
    project = (
        os.getenv("GOOGLE_CLOUD_PROJECT") 
        or os.getenv("GCLOUD_PROJECT") 
        or os.getenv("GCP_PROJECT")
    )
    location = (
        os.getenv("GOOGLE_CLOUD_LOCATION") 
        or os.getenv("VERTEX_LOCATION") 
        or "us-central1"
    )
    
    try:
        # Initialize Vertex AI client
        client = genai.Client(vertexai=True, project=project, location=location) if project else genai.Client(vertexai=True)
        
        # Configure generation parameters
        config = types.GenerateContentConfig(
            thinking_config=types.ThinkingConfig(thinking_budget=256),
            tools=[types.Tool(google_search=types.GoogleSearch())] if enable_search else None,
        )
        
        # Generate content
        response = client.models.generate_content(
            model="gemini-2.5-pro",
            contents=prompt,
            config=config,
        )
        
        return True, (response.text or "").strip()
        
    except Exception as e:
        return False, str(e)


def run(token: str, target_lang: str = "en", enable_search: bool = False) -> str:
    """
    Generate fundamental analysis for a given token.
    
    Args:
        token: Token symbol (e.g., "BTC", "NEWTON")
        target_lang: Target language for output ("en" for English, "zh" for Chinese)
        enable_search: Whether to enable Google Search for enhanced information retrieval
        
    Returns:
        Markdown-formatted fundamental analysis or error message starting with "[Error]"
    """
    # Input validation and normalization
    token = (token or "").strip()
    target_lang = (target_lang or "en").strip()
    
    if not token:
        return ""
    
    try:
        # Read and format the prompt template
        template = _read_prompt()
        
        # Simple placeholder replacement (no Jinja2 dependency)
        prompt = template.replace("{{token}}", token).replace("{{target_lang}}", target_lang)
        
        # Call LLM
        success, result = _call_llm_vertex(prompt, enable_search=enable_search)
        
        if success:
            return result
        else:
            return f"[Error] {result}"
            
    except Exception as e:
        return f"[Error] Failed to process fundamental analysis: {str(e)}"


async def _stream_llm_vertex_fundamentals(
    prompt: str,
    enable_search: bool = True,
    response_id: Optional[str] = None
) -> AsyncGenerator[Dict[str, str], None]:
    """
    Stream fundamental analysis from Vertex AI Gemini 2.5 Pro.

    Args:
        prompt: The formatted prompt string
        enable_search: Whether to enable Google Search tool
        response_id: Optional response ID for tracking

    Yields:
        Dict with type/message/id structure matching reply_service format
    """
    # Get project and location from environment variables
    project = (
        os.getenv("GOOGLE_CLOUD_PROJECT")
        or os.getenv("GCLOUD_PROJECT")
        or os.getenv("GCP_PROJECT")
    )
    location = (
        os.getenv("GOOGLE_CLOUD_LOCATION")
        or os.getenv("VERTEX_LOCATION")
        or "us-central1"
    )

    try:
        # Initialize Vertex AI client
        client = genai.Client(vertexai=True, project=project, location=location) if project else genai.Client(vertexai=True)

        # Configure generation parameters
        generation_config = types.GenerateContentConfig(
            thinking_config=types.ThinkingConfig(thinking_budget=-1, include_thoughts=True),
            tools=[types.Tool(google_search=types.GoogleSearch())] if enable_search else None,
        )

        def extract_text(event) -> Optional[str]:
            # Prefer top-level text if present
            top_level_text = getattr(event, "text", None)
            if top_level_text:
                return top_level_text
            # Otherwise join non-thought parts' text
            content = getattr(event, "content", None)
            parts = getattr(content, "parts", None) or []
            segments = [getattr(part, "text", None) for part in parts if not getattr(part, "thought", False)]
            segments = [segment for segment in segments if segment]
            return "".join(segments) if segments else None

        def iter_thoughts(event):
            candidate = (getattr(event, "candidates", None) or [None])[0]
            content = getattr(candidate, "content", None)
            parts = getattr(content, "parts", None) or []
            for part in parts:
                thought_text = getattr(part, "text", None)
                if getattr(part, "thought", False) and thought_text:
                    yield thought_text

        last_grounding_metadata = None

        # Stream tokens and capture grounding metadata for sources/search queries
        async for event in await client.aio.models.generate_content_stream(
            model="gemini-2.5-pro",
            contents=prompt,
            config=generation_config,
        ):
            text = extract_text(event)
            if text:
                yield {"type": "Content", "message": text, "id": response_id or ""}

            for thought_text in iter_thoughts(event):
                yield {"type": "Thought", "message": thought_text, "id": response_id or ""}

            candidate = (getattr(event, "candidates", None) or [None])[0]
            grounding_metadata = getattr(candidate, "grounding_metadata", None) if candidate else None
            if grounding_metadata:
                last_grounding_metadata = grounding_metadata

        # Emit search info and sources after stream ends (if available)
        if enable_search and last_grounding_metadata:
            chunks = getattr(last_grounding_metadata, "grounding_chunks", None)
            if chunks:
                for index, chunk in enumerate(chunks):
                    web = getattr(chunk, "web", None)
                    uri = getattr(web, "uri", None) if web else None
                    if uri:
                        title = getattr(web, "title", None) or uri
                        yield {"type": "References", "message": f"[{index+1}] {title} - {uri}", "id": response_id or ""}

    except Exception as e:
        yield {"type": "Content", "message": f"[Error] {str(e)}", "id": response_id or ""}


async def run_stream(
    token: str,
    target_lang: str = "en",
    enable_search: bool = True,
    response_id: Optional[str] = None
) -> AsyncGenerator[Dict[str, str], None]:
    """
    Generate streaming fundamental analysis for a given token.

    Args:
        token: Token symbol (e.g., "BTC", "NEWTON")
        target_lang: Target language for output ("en" for English, "zh" for Chinese)
        enable_search: Whether to enable Google Search for enhanced information retrieval
        response_id: Optional response ID for tracking

    Yields:
        Dict with type/message/id structure for streaming output
    """
    # Input validation and normalization
    token = (token or "").strip()
    target_lang = (target_lang or "en").strip()

    if not token:
        return

    try:
        # Read and format the prompt template
        template = _read_prompt()

        # Simple placeholder replacement (no Jinja2 dependency)
        prompt = template.replace("{{token}}", token).replace("{{target_lang}}", target_lang)

        # Stream LLM response
        async for chunk in _stream_llm_vertex_fundamentals(prompt, enable_search=enable_search, response_id=response_id):
            if chunk:
                yield chunk

    except Exception as e:
        yield {"type": "Content", "message": f"[Error] Failed to process fundamental analysis: {str(e)}", "id": response_id or ""}


if __name__ == "__main__":
    # Simple test when run directly
    import sys
    import asyncio
    import json

    async def test_stream():
        if len(sys.argv) > 1:
            test_token = sys.argv[1]
            test_lang = sys.argv[2] if len(sys.argv) > 2 else "en"
            test_search = sys.argv[3].lower() == "true" if len(sys.argv) > 3 else True

            print(f"Testing streaming fundamental analysis for {test_token} (lang: {test_lang}, search: {test_search})")
            print("=" * 80)

            async for chunk in run_stream(test_token, test_lang, test_search, "test-1"):
                print(json.dumps(chunk, ensure_ascii=False), flush=True)
            print("\n[done]", flush=True)
        else:
            print("Usage: python fundamental_what_is.py <token> [lang] [enable_search]")
            print("Example: python fundamental_what_is.py NEWTON en true")

    asyncio.run(test_stream())
