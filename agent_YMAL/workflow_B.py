from pathlib import Path
import sys, os, json
from dotenv import load_dotenv
from google import genai
from google.genai import types

# Ensure project root importable and env loaded
sys.path.append(str(Path(__file__).resolve().parent.parent))
load_dotenv()

try:
    import vertexai
    if os.getenv("GOOGLE_CLOUD_PROJECT") and os.getenv("GOOGLE_CLOUD_LOCATION"):
        vertexai.init(project=os.environ["GOOGLE_CLOUD_PROJECT"], location=os.environ["GOOGLE_CLOUD_LOCATION"])
except Exception:
    pass


def _call_llm_vertex(prompt: str):
    project = (
        os.environ.get("GOOGLE_CLOUD_PROJECT")
        or os.environ.get("GCLOUD_PROJECT")
        or os.environ.get("GCP_PROJECT")
    )
    location = os.environ.get("GOOGLE_CLOUD_LOCATION") or os.environ.get("VERTEX_LOCATION") or "us-central1"
    try:
        client = genai.Client(vertexai=True, project=project, location=location) if project else genai.Client(vertexai=True)
        resp = client.models.generate_content(
            model="gemini-2.5-pro",
            contents=prompt,
            config=types.GenerateContentConfig(
                thinking_config=types.ThinkingConfig(thinking_budget=256)
            ),
        )
        return True, resp.text
    except Exception as e:
        return False, str(e)


def run(token: str, target_lang: str = "en") -> str:
    """Workflow B: "What are people saying about {token}?" Summarize recent sentiment/topics."""
    token = (token or "").strip()
    target_lang = (target_lang or "en").strip()
    if not token:
        return ""

    system_instr = (
        "You are a crypto community analyst. Summarize what people are discussing about the token across posts and tweets. "
        "Focus on key themes, narratives, and sentiment cues. No price prediction. "
        "Always answer fully in the target language."
    )
    inputs_json = json.dumps({"token": token, "target_lang": target_lang}, ensure_ascii=False)
    prompt = f"{system_instr}\n\nInputs:\n{inputs_json}"

    ok, text = _call_llm_vertex(prompt)
    return (text or "").strip() if ok else f"[Error] {text}"

