"""
Simple K-line mock provider for quick UI overlay. Use real charts later.
"""
from typing import Dict, Any


def get_hourly_mock() -> Dict[str, Any]:
    # Minimal mock: list of [timestamp, open, high, low, close]
    base = 1754269200000
    delta = 3600_000
    rows = []
    for i in range(24):
        t = base + i * delta
        o = 114500 + i * 10
        h = o + 300
        l = o - 300
        c = o + (i % 3 - 1) * 120
        rows.append([t, o, h, l, c])
    return {
        "header": ["timestamp", "open_price", "high_price", "low_price", "close_price"],
        "data": rows,
    }

