import asyncio
import datetime
from typing import Dict, Set
import logging
import util.tools.memory as memory
from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, WebSocket
from google.adk.events import Event
import json

from common.enum.business_enum import WebSocketBusinessType
from util.logger.logger_utils import setup_logger, log_exception_as_single_line
from util.shared_config import MAX_CONCURRENT_SESSIONS, APP_NAME
from util.tools.markdown_post_process import run_markdown_post_process
from endpoints.ai_report import session as ai_report_session

import logging

logger = setup_logger("agent_tracer")

class SessionManager:
    """Manages Google Gemini Live API sessions with proper limits and cleanup"""

    def __init__(self, max_sessions: int = MAX_CONCURRENT_SESSIONS):
        self.max_sessions = max_sessions
        self.active_sessions: Dict[str, dict] = {}
        self.semaphore = asyncio.Semaphore(max_sessions)
        self.cleanup_tasks: Set[asyncio.Task] = set()

    async def acquire_session(self, session_id: str) -> bool:
        """Acquire a session slot, returns True if successful"""
        # Check if session already exists
        if session_id in self.active_sessions:
            logger.info(f"Session {session_id} already exists. Active sessions: {len(self.active_sessions)}/{self.max_sessions}")
            return True
            
        try:
            # Try to acquire with a timeout to avoid hanging
            await asyncio.wait_for(self.semaphore.acquire(), timeout=10.0)
            self.active_sessions[session_id] = {
                'created_at': datetime.datetime.now(),
                'status': 'active'
            }
            logger.info(
                f"Session {session_id} acquired. Active sessions: {len(self.active_sessions)}/{self.max_sessions}")
            return True
        except asyncio.TimeoutError:
            logger.warning(f"Session {session_id} acquisition timed out. Too many concurrent sessions.")
            return False

    async def release_session(self, session_id: str):
        """Release a session slot"""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
            self.semaphore.release()
            logger.info(
                f"Session {session_id} released. Active sessions: {len(self.active_sessions)}/{self.max_sessions}")
        else:
            logger.warning(f"Attempted to release non-existent session: {session_id}")

    def get_active_session_count(self) -> int:
        """Get the number of active sessions"""
        return len(self.active_sessions)

    def get_semaphore_state(self) -> dict:
        """Get debug information about semaphore state"""
        return {
            'active_sessions_count': len(self.active_sessions),
            'max_sessions': self.max_sessions,
            'semaphore_value': self.semaphore._value,
            'active_session_ids': list(self.active_sessions.keys())
        }

    async def emergency_reset(self):
        """Emergency reset of session manager state - use only if session state is corrupted"""
        logger.warning("Performing emergency reset of session manager state")
        
        # Clear all active sessions
        old_sessions = list(self.active_sessions.keys())
        self.active_sessions.clear()
        
        # Reset semaphore to initial state
        # We need to release enough permits to get back to max_sessions
        current_value = self.semaphore._value
        releases_needed = self.max_sessions - current_value
        for _ in range(releases_needed):
            self.semaphore.release()
            
        logger.warning(f"Emergency reset completed. Cleared {len(old_sessions)} sessions: {old_sessions}")
        logger.info(f"New session state: {self.get_semaphore_state()}")

    async def cleanup_old_sessions(self, max_age_minutes: int = 30):
        """Clean up sessions older than max_age_minutes"""
        now = datetime.datetime.now()
        to_remove = []

        for session_id, session_info in self.active_sessions.items():
            age = now - session_info['created_at']
            if age.total_seconds() > max_age_minutes * 60:
                to_remove.append(session_id)

        for session_id in to_remove:
            logger.info(f"Cleaning up old session: {session_id}")
            await self.release_session(session_id)
            
        # Also clean up old user contexts
        memory.cleanup_old_user_contexts(max_age_minutes)

# Global session manager
session_manager = SessionManager()

# Periodic cleanup task
async def periodic_session_cleanup():
    """Background task to clean up old sessions"""
    while True:
        try:
            await asyncio.sleep(300)  # Run every 5 minutes
            await session_manager.cleanup_old_sessions(max_age_minutes=30)
            
            # Log detailed session state for debugging
            semaphore_state = session_manager.get_semaphore_state()
            logger.info(f"Periodic cleanup completed. Session state: {semaphore_state}")
            
            # Log warning if there's a mismatch between semaphore and active sessions
            if semaphore_state['active_sessions_count'] != (session_manager.max_sessions - semaphore_state['semaphore_value']):
                logger.warning(f"Session count mismatch detected! Active sessions: {semaphore_state['active_sessions_count']}, "
                             f"Semaphore value: {semaphore_state['semaphore_value']}, "
                             f"Expected semaphore value: {session_manager.max_sessions - semaphore_state['active_sessions_count']}")
        except Exception as e:
            logger.error(f"Error in periodic session cleanup: {e}")

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting BDP Search Agent application")
    logger.info(f"Session limit set to: {session_manager.max_sessions}")
    
    # Start background cleanup task
    cleanup_task = asyncio.create_task(periodic_session_cleanup())
    
    # Start system instructions cleanup as a background task to avoid blocking startup
    async def background_cleanup():
        try:
            # Clean up any system instructions that might be in conversation histories
            from util.tools.memory import cleanup_all_sessions_system_instructions
            cleanup_all_sessions_system_instructions()
        except Exception as e:
            logger.error(f"Error in background system instructions cleanup: {e}")
    
    cleanup_system_task = asyncio.create_task(background_cleanup())

    yield

    # Shutdown
    logger.info("Shutting down BDP Search Agent application")

    # Cancel cleanup tasks
    cleanup_task.cancel()
    cleanup_system_task.cancel()
    try:
        await cleanup_task
    except asyncio.CancelledError:
        pass
    try:
        await cleanup_system_task
    except asyncio.CancelledError:
        pass

    # Clean up all active sessions
    for session_id in list(session_manager.active_sessions.keys()):
        try:
            await session_manager.release_session(session_id)
        except Exception as e:
            logger.error(f"Error cleaning up session {session_id}: {e}")

from google.genai.types import (
    Part,
    Content,
)
import re
import util.tools.memory as memory
import util.tools.root as root
from starlette.websockets import WebSocketDisconnect, WebSocketState
from util.tools.language_check import check_language_text_list, check_language

def filter_thinking_message(text: str) -> str:
    """
    Filter out ALL <think> </think> tags and their content from the text
    """
    if not text or not isinstance(text, str):
        return text
    # part.text = "<think>\n\n</think>\n\n" or "<think>actual thinking content</think>"
    # Remove ALL thinking tags and their content (empty or with content)
    text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL | re.IGNORECASE)
    # Clean up extra whitespace but preserve meaningful formatting
    text = re.sub(r'\n\s*\n', '\n', text)  # Remove multiple empty lines
    text = text.strip()

    return text

def filter_thinking_message(text: str) -> str:
    """
    Filter out ALL <think> </think> tags and their content from the text
    """
    if not text or not isinstance(text, str):
        return text
    # part.text = "<think>\n\n</think>\n\n" or "<think>actual thinking content</think>"
    # Remove ALL thinking tags and their content (empty or with content)
    text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL | re.IGNORECASE)
    # Clean up extra whitespace but preserve meaningful formatting
    text = re.sub(r'\n\s*\n', '\n', text)  # Remove multiple empty lines
    text = text.strip()

    return text

async def client_to_agent_messaging_for_ws_biz(websocket, sequential_runner, session_id, session_service, ws_biz_type):
    """Dispatch to the proper session starter based on business type, and return (runner, run_config)."""
    logger.info(f"[DEBUG] client_to_agent_messaging called: session={session_id}, biz={ws_biz_type}")
    if ws_biz_type is WebSocketBusinessType.AI_REPORT:
        return await ai_report_session.client_to_agent_messaging(websocket, sequential_runner, session_id, session_service)
    return await client_to_agent_messaging(websocket, sequential_runner, session_id, session_service)

async def client_to_agent_messaging(websocket, sequential_runner, session_id, session_service=None):
    """Client to agent communication using run_async approach"""
    logger.info(f"[DEBUG] client_to_agent_messaging started for session: {session_id}")
    logger.info(f"[DEBUG] WebSocket state: {websocket.client_state}")
    
    # Session context is now initialized in main.py before tasks are created
    logger.info(f"[DEBUG] Session context should already be initialized for session: {session_id}")
    
    try:
        while True:
            # Check WebSocket state before attempting to receive
            if websocket.client_state in [WebSocketState.DISCONNECTED, WebSocketState.CONNECTING]:
                logger.info(f"[DEBUG] WebSocket in invalid state ({websocket.client_state}), stopping client_to_agent_messaging for session: {session_id}")
                break
                
            logger.info(f"[DEBUG] Waiting for message from client for session: {session_id}")
            try:
                text = await websocket.receive_text()
                logger.info(f"[DEBUG] Received raw message for session {session_id}: {text[:100]}...")
            except WebSocketDisconnect:
                logger.info(f"[DEBUG] WebSocket disconnected while receiving message for session: {session_id}")
                break
            except RuntimeError as e:
                if "WebSocket is not connected" in str(e):
                    logger.info(f"[DEBUG] WebSocket not connected, stopping client_to_agent_messaging for session: {session_id}")
                    break
                else:
                    raise
            
            data = json.loads(text)
            message_type = data.get("type")
            message_text = data.get("message", "")
            
            logger.info(f"[DEBUG] Parsed message - Type: {message_type}, Text: {message_text[:50]}...")
            logger.info(f"[DEBUG] Message data structure: {json.dumps(data, indent=2)}")
            
            if message_type == 1 and session_service:
                logger.info(f"[DEBUG] Processing legacy message type 1 for session: {session_id}")
                
                # Validate that we're not processing system instructions as user messages
                if message_text and ("CRITICAL RESPONSE HANDLING" in message_text or "绝对禁止的行为" in message_text or "RESPONSE PASSTHROUGH PROTOCOL" in message_text):
                    logger.warning(f"[DEBUG] Detected system instruction in legacy user message for session {session_id}, skipping: {message_text[:100]}...")
                    continue
                
                # Legacy message format - store in session history
                event = Event(
                    author="user",
                    content=Content(role="user", parts=[Part.from_text(text=message_text)])
                )
                session = await session_service.get_session(
                    app_name=APP_NAME,
                    user_id=session_id,
                    session_id=session_id
                )
                
                # Check if session exists before appending event
                if session is None:
                    logger.error(f"[DEBUG] error: Session not found for session_id: {session_id}, cannot append event")
                    # Just Log, Do not send "Session not found. Please try reconnecting." to client
                    # error_response = {
                    #     "type": 2,
                    #     "message": {
                    #         "sub_type": 200,
                    #         "msg": "Session not found. Please try reconnecting."
                    #     }
                    # }
                    # await memory.queue_websocket_message(session_id, error_response)
                    continue
                
                await session_service.append_event(session=session, event=event)
                logger.info(f"[DEBUG] Stored legacy message in session history for session: {session_id}")
                
                # Also add to our custom conversation history tracking
                memory.add_query_to_session_history(session_id, message_text)
                
            elif message_type == 20:
                logger.info(f"[DEBUG] Processing live message type 20 for session: {session_id}")
                
                # Validate that we're not processing system instructions as user messages
                if message_text and ("CRITICAL RESPONSE HANDLING" in message_text or "绝对禁止的行为" in message_text or "RESPONSE PASSTHROUGH PROTOCOL" in message_text):
                    logger.warning(f"[DEBUG] Detected system instruction in user message for session {session_id}, skipping: {message_text[:100]}...")
                    continue
                
                # Create content for the message
                content = Content(role="user", parts=[Part.from_text(text=message_text)])
                logger.info(f"[CLIENT TO AGENT]: Processing message with run_async: {message_text}")
                
                # Add to our custom conversation history tracking
                memory.add_query_to_session_history(session_id, message_text)
                
                # Process the message using sequential runner
                try:
                    from google.adk.agents.run_config import RunConfig
                    run_config = RunConfig(response_modalities=["TEXT"])
                    
                    # Call run_sequential with the new message
                    live_events = sequential_runner.run_sequential(
                        new_message=content,
                        run_config=run_config
                    )
                    
                    # Process the events and queue responses
                    async for event in live_events:
                        logger.info(f"[DEBUG] Processing event from sequential runner for session: {session_id}")
                        # Handle FollowUpEvent
                        if hasattr(event, 'follow_up_questions'):
                            follow_up_data = {
                                "type": 2,
                                "message": {
                                    "sub_type": 301,
                                    "msg": {
                                        "title": "you may also ask",
                                        "list": event.follow_up_questions
                                    }
                                }
                            }
                            await memory.queue_websocket_message(session_id, follow_up_data)
                            logger.info(f"[AGENT TO CLIENT]type301: Follow-up questions queued: {event.follow_up_questions}")
                            continue
                        
                        # Handle regular content events (from rootAgent or other agents)
                        if event.content and event.content.parts:
                            # Check if this event has function calls (indicating intermediate thinking)
                            has_function_call = False
                            for part in event.content.parts:
                                # function_call=None function_response=None for text parts
                                if part.function_call or part.function_response:
                                    logger.info(f"[DEBUG] Found function call/response in part: {part}")
                                    has_function_call = True
                                    break
                            logger.info(f"[DEBUG] has_function_call: {has_function_call}")
                            text_parts = [part for part in event.content.parts if part.text is not None and part.text.strip()]
                            logger.info(f"[DEBUG] text_parts: {text_parts}")
                            logger.info(f"[DEBUG] Event has {len(event.content.parts)} parts, has_function_call: {has_function_call}, text_parts: {len(text_parts)}")
                            
                            # Only process text if there's no function call (meaning this is the final response)
                            if not has_function_call and text_parts:
                                for part in text_parts:
                                    # Skip parts that are marked as thinking content
                                    if hasattr(part, 'thought') and part.thought:
                                        logger.info(f"[DEBUG] Skipping thinking content for session {session_id}: {part.text if part.text else 'No text'}...")
                                        continue
                                    
                                    # Queue the response
                                    # if <think> </think> is in the text, remove content between <think> and </think>
                                    filtered_text = filter_thinking_message(part.text)
                                    logger.info(f"[DEBUG] filtered_text: {filtered_text}")
                                    response_data = {
                                        "type": 2,
                                        "message": {
                                            "sub_type": 200,
                                            "msg": check_language(session_id, part.text)
                                        }
                                    }
                                    await memory.queue_websocket_message(session_id, response_data)
                                    logger.info(f"[AGENT TO CLIENT]: Queued response chunk: {part.text[:100]}...")

                                    # Check for pending convert card and launch it after text response
                                    try:
                                        from agent_convert.chat_convert_tool import get_pending_convert_card, queue_convert_card
                                        pending_convert_data = get_pending_convert_card(session_id)
                                        if pending_convert_data:
                                            logger.info(f"[DEBUG] Found pending convert card data, launching card for session {session_id}")
                                            # Launch the convert card after text response is queued
                                            await queue_convert_card(pending_convert_data)
                                            logger.info(f"[DEBUG] Convert card launched successfully for session {session_id}")
                                    except Exception as e:
                                        logger.error(f"[DEBUG] Error launching convert card for session {session_id}: {e}")

                                    try:
                                        from agent_spot.chat_spot_tool import get_pending_spot_card, queue_spot_card
                                        pending_spot_data = get_pending_spot_card(session_id)
                                        if pending_spot_data:
                                            logger.info(f"[DEBUG] Found pending spot card data, launching card for session {session_id}")
                                            # Launch the convert card after text response is queued
                                            await queue_spot_card(pending_spot_data)
                                            logger.info(f"[DEBUG] Spot card launched successfully for session {session_id}")
                                    except Exception as e:
                                        logger.error(f"[DEBUG] Error launching spot card for session {session_id}: {e}")

                                    

                                    # Send type3 message after each subtype 200
                                    one_turn_complete_data = {
                                        "type": 3,
                                        "turn_complete": True
                                    }
                                    await memory.queue_websocket_message(session_id, one_turn_complete_data)
                                    logger.info(f"[AGENT TO CLIENT]type3: (after each subtype 200 message) one turn complete message sent into memory queue.")

                                    # For follow-up questions
                                    if hasattr(part, 'follow_up_questions') and part.follow_up_questions:
                                        follow_up_data = {
                                            "type": 2,
                                            "message": {
                                                "sub_type": 301,
                                                "msg": {
                                                    "title": "you may also ask",
                                                    "list": check_language_text_list(session_id, part.follow_up_questions)
                                                }
                                            }
                                        }
                                        await memory.queue_websocket_message(session_id, follow_up_data)
                                        logger.info(f"[AGENT TO CLIENT]type301: Follow-up questions queued: {part.follow_up_questions}")
                            else:
                                logger.info(f"[DEBUG] Skipping text processing because event has function_call or no valid text parts - this is intermediate thinking, not final response")
                        
                        # Handle turn complete
                        if event.turn_complete:
                            # Queue type 3 message only after all content has been queued
                            turn_complete_data = {
                                "type": 3,
                                "turn_complete": True
                            }
                            await memory.queue_websocket_message(session_id, turn_complete_data)
                            logger.info(f"[AGENT TO CLIENT]type3: Turn complete message queued")
                            break
                    
                    logger.info(f"[DEBUG] Finished processing message for session: {session_id}")
                    
                except Exception as e:
                    logger.error(f"[DEBUG] Error processing message with sequential runner for session {session_id}: {e}")
                    log_exception_as_single_line(logger, e, "[DEBUG] Full error exception details")
                    
                
            else:
                logger.warning(f"[DEBUG] Unknown message type {message_type} for session: {session_id}")
                
    except WebSocketDisconnect as e:
        logger.info(f"[DEBUG] WebSocket disconnected for session {session_id}: {e}")
    except Exception as e:
        logger.error(f"[DEBUG] Error in client_to_agent_messaging for session {session_id}: {e}")
        log_exception_as_single_line(logger, e, "[DEBUG] Full exception details for client_to_agent_messaging")

async def agent_to_client_messaging(websocket, sequential_runner, session_id, ws_biz_type):
    """Agent to client communication using message queue"""
    logger.info(f"[DEBUG] agent_to_client_messaging started for session: {session_id}")
    logger.info(f"[DEBUG] WebSocket state: {websocket.client_state}")
    logger.info(f"[DEBUG] Initial session contexts: {list(memory._session_user_contexts.keys())}")
    
    # Wait for session context to be initialized
    retry_count = 0
    max_retries = 10  # Increased from 3 to handle slower initialization
    while session_id not in memory._session_user_contexts and retry_count < max_retries:
        logger.info(f"[DEBUG] Waiting for session context to be initialized for session {session_id}, retry {retry_count + 1}/{max_retries}")
        logger.info(f"[DEBUG] Available session contexts: {list(memory._session_user_contexts.keys())}")
        await asyncio.sleep(0.1)  # Increased from 0.05 to 0.1 for more stable initialization
        retry_count += 1
    
    if session_id not in memory._session_user_contexts:
        logger.error(f"[DEBUG] Session context not initialized after {max_retries} retries for session {session_id}")
        logger.info(f"[DEBUG] Available session contexts: {list(memory._session_user_contexts.keys())}")
        return
    
    logger.info(f"[DEBUG] Session context found for session {session_id}")
    logger.info(f"[DEBUG] Session context keys: {list(memory._session_user_contexts[session_id].keys())}")
    
    # Helper function to safely get session context
    def get_session_context_safe():
        """Safely get session context with proper error handling"""
        try:
            if session_id not in memory._session_user_contexts:
                logger.warning(f"[DEBUG] Session context not found for session {session_id} during execution")
                logger.info(f"[DEBUG] Available session contexts: {list(memory._session_user_contexts.keys())}")
                return None
            # Update last accessed timestamp
            memory.update_session_last_accessed(session_id)
            return memory._session_user_contexts[session_id]
        except Exception as e:
            logger.error(f"[DEBUG] Error accessing session context for session {session_id}: {e}")
            return None
    
    try:
        while True:
            # Check WebSocket state before attempting to send
            if websocket.client_state in [WebSocketState.DISCONNECTED, WebSocketState.CONNECTING]:
                logger.info(f"[DEBUG] WebSocket in invalid state ({websocket.client_state}), stopping agent_to_client_messaging for session: {session_id}")
                break
            
            # Safely get session context
            session_context = get_session_context_safe()
            if not session_context:
                logger.warning(f"[DEBUG] Session context not available for session {session_id}, waiting before retry")
                await asyncio.sleep(0.5)  # Wait longer before retrying
                continue
                
            # Get message queue for this session
            message_queue = session_context.get("websocket_message_queue")
            if not message_queue:
                logger.warning(f"[DEBUG] No message queue found for session {session_id}")
                logger.info(f"[DEBUG] Available session contexts: {list(memory._session_user_contexts.keys())}")
                await asyncio.sleep(0.1)
                continue

            try:
                # Log queue state before attempting to get message
                # logger.info(f"[DEBUG] Queue state for session {session_id} - Size: {message_queue.qsize()}, Empty: {message_queue.empty()}")
                
                # Get message from queue with timeout
                #logger.info(f"[DEBUG] Attempting to get message from queue for session {session_id}")
                message = await asyncio.wait_for(message_queue.get(), timeout=1.0)
                
                logger.info(f"[DEBUG] Retrieved message from queue for session {session_id}: {message}")
                
                # If message is already a dict, use it directly
                if isinstance(message, dict):
                    message_data = message
                else:
                    # If message is a string, parse it
                    message_data = json.loads(message)

                json_dumps_ensure_ascii = (ws_biz_type != WebSocketBusinessType.AI_REPORT)
                logger.info(f"[DEBUG] Prepared message data for sending: {json.dumps(message_data, indent=2, ensure_ascii=json_dumps_ensure_ascii)}")
                
                
                
                # Clean up "---" marks in the message content
                if isinstance(message_data, dict) and "message" in message_data:
                    if isinstance(message_data["message"], dict) and "msg" in message_data["message"]:
                        # For type 2 messages with sub_type 200 (text messages)
                        if message_data["message"].get("sub_type") == 200:
                            original_text = message_data["message"]["msg"]
                            cleaned_text = run_markdown_post_process(original_text)
                            message_data["message"]["msg"] = cleaned_text
                            logger.info(f"[DEBUG] Cleaned '---' marks from message for session {session_id}")
                    elif isinstance(message_data["message"], str):
                        # For direct string messages
                        original_text = message_data["message"]
                        cleaned_text = run_markdown_post_process(original_text)
                        message_data["message"] = cleaned_text
                        logger.info(f"[DEBUG] Cleaned '---' marks from direct message for session {session_id}")
                
                # Send message through websocket with proper exception handling
                try:
                    # Double-check WebSocket state before sending
                    if websocket.client_state in [WebSocketState.DISCONNECTED, WebSocketState.CONNECTING]:
                        logger.info(f"[DEBUG] WebSocket in invalid state ({websocket.client_state}) before sending message for session: {session_id}")
                        break
                        
                    await websocket.send_text(json.dumps(message_data, ensure_ascii=json_dumps_ensure_ascii))
                    logger.info(f"[DEBUG] Successfully sent message to client for session {session_id}, message_data: {message_data}    ")
                except WebSocketDisconnect as ws_disconnect:
                    logger.info(f"[DEBUG] WebSocket disconnected while sending message for session {session_id}: {ws_disconnect}")
                    # Break out of the loop since the connection is closed
                    break
                except RuntimeError as e:
                    if "Cannot call \"send\" once a close message has been sent" in str(e):
                        logger.info(f"[DEBUG] WebSocket already closed, stopping agent_to_client_messaging for session: {session_id}")
                        break
                    else:
                        logger.error(f"[DEBUG] RuntimeError sending message through WebSocket for session {session_id}: {e}")
                        log_exception_as_single_line(logger, e, "[DEBUG] Full exception details for WebSocket send")
                        # Continue to next message instead of breaking, in case it's a temporary issue
                        continue
                except Exception as send_error:
                    logger.error(f"[DEBUG] Error sending message through WebSocket for session {session_id}: {send_error}")
                    log_exception_as_single_line(logger, send_error, "[DEBUG] Full exception details for WebSocket send")
                    # Continue to next message instead of breaking, in case it's a temporary issue
                    continue
                
                # Mark message as processed
                message_queue.task_done()
                logger.info(f"[DEBUG] Message marked as processed in queue for session {session_id}")
                logger.info(f"[DEBUG] Queue state after processing - Size: {message_queue.qsize()}, Empty: {message_queue.empty()}")
                
            except asyncio.TimeoutError:
                # No message in queue, continue polling
                # logger.debug(f"[DEBUG] No message in queue for session {session_id} after timeout")
                # logger.info(f"[DEBUG] Queue state at timeout - Size: {message_queue.qsize()}, Empty: {message_queue.empty()}")
                continue
            except asyncio.QueueEmpty:
                # Queue is empty, continue polling
                # logger.debug(f"[DEBUG] Queue is empty for session {session_id}")
                continue
            except Exception as e:
                logger.error(f"[DEBUG] Error processing message for session {session_id}: {e}")
                log_exception_as_single_line(logger, e, "[DEBUG] Full exception details")
                continue
                
    except WebSocketDisconnect as e:
        logger.error(f"[DEBUG] WebSocket disconnected for session {session_id}: {e}")
        logger.error(f"[DEBUG] WebSocket state at disconnect: {websocket.client_state}")
    except Exception as e:
        logger.error(f"[DEBUG] Error in agent_to_client_messaging for session {session_id}: {e}")
        log_exception_as_single_line(logger, e, "[DEBUG] Full exception details for agent_to_client_messaging")
