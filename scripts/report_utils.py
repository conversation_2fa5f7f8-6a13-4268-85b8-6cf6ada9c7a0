"""
Shared utilities for report generation and testing scripts.
Contains common functions for file operations, message extraction, status checking, and retry logic.
"""
from __future__ import annotations

import os
import re
import time
from typing import Any, Optional, Callable, Dict

# Default output directory for all reports
OUTPUT_DIR = "basic_reports"


def ensure_dir(path: str) -> None:
    """Create directory if it doesn't exist."""
    if not os.path.isdir(path):
        os.makedirs(path, exist_ok=True)


def save_report(token: str, content: str, output_dir: str = OUTPUT_DIR) -> str:
    """
    Save report content to a file.

    Args:
        token: Token symbol for filename
        content: Full content to write
        output_dir: Directory to save the file (defaults to OUTPUT_DIR)

    Returns:
        Full path to the saved file
    """
    ensure_dir(output_dir)
    filename = f"{token}_fundamental.txt"
    fpath = os.path.join(output_dir, filename)
    with open(fpath, "w", encoding="utf-8") as f:
        f.write(content)
    return fpath


def extract_message(data: Any) -> Optional[str]:
    """
    Enhanced extraction of the model's message from API JSON with citation processing.
    Preserves existing URLs and handles Grok citation tags appropriately.

    Args:
        data: API response data

    Returns:
        Extracted message string with processed citations or None if not found
    """
    try:
        # Common paths for message extraction
        msg = (
            data.get("data", {}).get("outputs", {}).get("message")
            or data.get("outputs", {}).get("message")
            or data.get("message")
        )

        if isinstance(msg, str):
            # First, preserve any existing URLs in the message
            existing_urls = _extract_existing_urls(msg)

            # Try to extract citation data from the API response
            citations = _extract_citations_from_response(data)

            # Process citation tags in the message
            if citations:
                # If we have citation data, try to resolve the tags
                msg = _process_citation_tags(msg, citations)
            else:
                # If no citation data found, clean up the tags but preserve existing URLs
                msg = _clean_citation_tags_preserve_urls(msg)

            return msg
    except Exception:
        pass
    return None


def _extract_existing_urls(message: str) -> List[str]:
    """
    Extract existing URLs from the message to preserve them.

    Args:
        message: Message text

    Returns:
        List of URLs found in the message
    """
    import re
    # Find both markdown links and plain URLs
    markdown_urls = re.findall(r'\[([^\]]*)\]\(([^)]+)\)', message)
    plain_urls = re.findall(r'https?://[^\s\)]+', message)

    all_urls = []
    for text, url in markdown_urls:
        all_urls.append(url)
    all_urls.extend(plain_urls)

    return list(set(all_urls))  # Remove duplicates


def _clean_citation_tags_preserve_urls(message: str) -> str:
    """
    Clean up citation tags while preserving existing URLs.

    Args:
        message: Message with citation tags and possibly existing URLs

    Returns:
        Message with citation tags removed but URLs preserved
    """
    # First extract existing URLs to preserve them
    existing_urls = _extract_existing_urls(message)

    # Clean citation tags
    cleaned = _clean_citation_tags(message)

    # Verify that existing URLs are still present
    remaining_urls = _extract_existing_urls(cleaned)

    # If we lost URLs during cleaning, this indicates the URLs were properly formatted
    # and should have been preserved by the cleaning process
    if len(existing_urls) > len(remaining_urls):
        # This shouldn't happen with proper cleaning, but log it for debugging
        print(f"Warning: Lost {len(existing_urls) - len(remaining_urls)} URLs during citation cleaning")

    return cleaned


def _extract_citations_from_response(data: Any) -> Dict[str, Dict[str, Any]]:
    """
    Extract citation/reference data from API response.

    Args:
        data: API response data

    Returns:
        Dictionary mapping citation IDs to citation data
    """
    citations = {}

    def search_for_citations(obj: Any, path: str = "") -> None:
        """Recursively search for citation data in the response."""
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key

                # Look for citation-related fields
                if any(keyword in key.lower() for keyword in ['citation', 'reference', 'source', 'link', 'url']):
                    if isinstance(value, list):
                        # Handle list of citations
                        for i, item in enumerate(value):
                            if isinstance(item, dict):
                                citation_id = item.get('id') or item.get('citation_id') or str(i)
                                citations[str(citation_id)] = item
                    elif isinstance(value, dict):
                        # Handle dictionary of citations
                        for cite_id, cite_data in value.items():
                            if isinstance(cite_data, dict):
                                citations[str(cite_id)] = cite_data

                search_for_citations(value, current_path)
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                search_for_citations(item, f"{path}[{i}]")

    search_for_citations(data)
    return citations


def _process_citation_tags(message: str, citations: Dict[str, Dict[str, Any]]) -> str:
    """
    Process Grok citation tags in the message and replace with actual URLs.

    Args:
        message: Original message with citation tags
        citations: Dictionary of citation data

    Returns:
        Message with processed citations
    """
    # Pattern to match Grok citation tags
    citation_pattern = r'<grok:render[^>]*card_type="citation_card"[^>]*>.*?<argument name="citation_id">(\d+)</argument>.*?</grok:render>'

    def replace_citation(match):
        citation_id = match.group(1)

        if citation_id in citations:
            citation_data = citations[citation_id]

            # Try to extract URL and title from citation data
            url = (citation_data.get('url') or
                   citation_data.get('link') or
                   citation_data.get('href') or
                   citation_data.get('source_url'))

            title = (citation_data.get('title') or
                    citation_data.get('name') or
                    citation_data.get('source_title') or
                    f"Reference {citation_id}")

            if url:
                return f"[{title}]({url})"
            else:
                return f"[Reference {citation_id}]"
        else:
            return f"[Reference {citation_id}]"

    # Replace citation tags with markdown links
    processed_message = re.sub(citation_pattern, replace_citation, message, flags=re.DOTALL)
    return processed_message


def _clean_citation_tags(message: str) -> str:
    """
    Clean up citation tags when no citation data is available.
    Preserves existing markdown links and URLs.

    Args:
        message: Message with citation tags

    Returns:
        Message with citation tags removed but existing URLs preserved
    """
    # Pattern to match any Grok render tags
    render_pattern = r'<grok:render[^>]*>.*?</grok:render>'

    # Remove the tags entirely since we don't have the reference data
    cleaned_message = re.sub(render_pattern, '', message, flags=re.DOTALL)

    # Clean up any extra whitespace left behind, but preserve line structure
    cleaned_message = re.sub(r' +', ' ', cleaned_message)  # Multiple spaces to single space
    cleaned_message = re.sub(r'\n\s*\n\s*\n+', '\n\n', cleaned_message)  # Multiple newlines to double newline

    return cleaned_message.strip()


def is_partial_success(resp: Any) -> bool:
    """
    Check if API response indicates partial success or empty message.

    Args:
        resp: API response data

    Returns:
        True if response is partial success or has empty message
    """
    try:
        status = resp.get("data", {}).get("status")
        message = resp.get("data", {}).get("outputs", {}).get("message", "")
        return status == "partial-succeeded" or not message or message.strip() in ["", "''", '""']
    except Exception:
        return False


def retry_on_failure(func: Callable, token: str, max_retries: int = 2, delay: float = 10.0) -> tuple[Any, bool]:
    """
    Retry function call on partial success or failure.

    Args:
        func: Function to call (should return API response)
        token: Token being processed (for logging)
        max_retries: Maximum number of retry attempts
        delay: Delay between retries in seconds

    Returns:
        Tuple of (result, is_successful)
    """
    for attempt in range(max_retries + 1):
        try:
            result = func(token)

            # Check if result is successful
            if not is_partial_success(result):
                if attempt > 0:
                    print(f"  ✓ Retry {attempt} succeeded for {token}")
                return result, True

            # If partial success and we have retries left
            if attempt < max_retries:
                print(f"  ⚠ Attempt {attempt + 1} partial success for {token}, retrying in {delay}s...")
                time.sleep(delay)
            else:
                print(f"  ⚠ All {max_retries + 1} attempts resulted in partial success for {token}")
                return result, False

        except Exception as e:
            if attempt < max_retries:
                print(f"  ✗ Attempt {attempt + 1} failed for {token}: {e}, retrying in {delay}s...")
                time.sleep(delay)
            else:
                print(f"  ✗ All {max_retries + 1} attempts failed for {token}")
                raise e

    return None, False


def print_statistics(total: int, success: int, partial_tokens: list[str], failed_tokens: list[str],
                    retried_tokens: Optional[list[str]] = None) -> None:
    """
    Print detailed statistics about processing results.

    Args:
        total: Total number of tokens processed
        success: Number of successful tokens
        partial_tokens: List of tokens with partial success
        failed_tokens: List of tokens that failed
        retried_tokens: List of tokens that required retries
    """
    print(f"\n{'='*50}")
    print(f"FINAL STATISTICS")
    print(f"{'='*50}")
    print(f"Total tokens processed: {total}")
    print(f"Successful: {success}")
    print(f"Partial success (empty/partial response): {len(partial_tokens)}")
    print(f"Failed (API errors): {len(failed_tokens)}")

    if retried_tokens:
        print(f"Required retries: {len(retried_tokens)}")

    if partial_tokens:
        print(f"\nPartial success tokens: {', '.join(partial_tokens)}")
    if failed_tokens:
        print(f"Failed tokens: {', '.join(failed_tokens)}")
    if retried_tokens:
        print(f"Retried tokens: {', '.join(retried_tokens)}")
