#!/usr/bin/env python3
"""
Process cryptocurrency token queries using predefined question templates.
Generates a CSV report with responses for each token and query combination.
"""
from __future__ import annotations

import csv
import json
import os
import sys
import time
from datetime import datetime
from typing import List, Dict, Any

# Ensure project root on sys.path
ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT not in sys.path:
    sys.path.insert(0, ROOT)

from hexa.hexa_api_client import call_hexa, HexaAPIError
from report_utils import extract_message, is_partial_success, print_statistics, retry_on_failure

# Token list as specified in requirements
TOKENS: List[str] = [
    "BTC", "ETH", "SOL", "NMR", "XRP", 
    "BNB", "PEPE", "DOGE", "BIO", "ADA"
]

# Query templates as specified in requirements
QUERY_TEMPLATES: List[str] = [
    "Why is ${token} price up?",
    "Why is ${token} price down?", 
    "What could affect ${token}'s future price?",
    "What are people saying about ${token}?",
    "What is the latest news on ${token}?",
    "What is ${token}?",
    "What is next on ${token}'s roadmap?",
    "What is the latest update in ${token}'s codebase?",
    "Why is ${token}'s price up today?",
    "Why is ${token}'s price down today?"
]

OUTPUT_CSV = "crypto_query_responses.csv"


def generate_query(template: str, token: str) -> str:
    """Generate a query by substituting token in template."""
    return template.replace("${token}", token)


def query_hexa_api(query: str) -> str:
    """
    Send query to Hexa API and extract readable response.
    
    Args:
        query: The query string to send to API
        
    Returns:
        Extracted markdown content or error message
    """
    try:
        response = call_hexa(query, return_citations=True, return_search_results=True)
        message = extract_message(response)
        return message if message else "No readable response found"
    except HexaAPIError as e:
        return f"API Error: {str(e)}"
    except Exception as e:
        return f"Unexpected Error: {str(e)}"


def query_hexa_api_with_retry(query: str, token: str, template_idx: int, max_retries: int = 2) -> tuple[str, bool]:
    """
    Query Hexa API with retry logic for failed or partial responses.
    
    Args:
        query: The query string to send
        token: Token being processed (for logging)
        template_idx: Template index (for logging)
        max_retries: Maximum retry attempts
        
    Returns:
        Tuple of (response_text, is_successful)
    """
    def api_call_wrapper(_unused_token: str) -> Dict[str, Any]:
        """Wrapper to make API call compatible with retry_on_failure function."""
        return call_hexa(query, return_citations=True, return_search_results=True)
    
    try:
        response, is_successful = retry_on_failure(
            api_call_wrapper, 
            f"{token}_Q{template_idx+1}", 
            max_retries=max_retries, 
            delay=10.0
        )
        
        if response:
            message = extract_message(response)
            return message if message else "No readable response found", is_successful
        else:
            return "Failed to get response after retries", False
            
    except Exception as e:
        return f"Error: {str(e)}", False


def main(argv: list[str]) -> int:
    """Main processing function."""
    print(f"Processing {len(TOKENS)} tokens with {len(QUERY_TEMPLATES)} queries each")
    print(f"Total queries to process: {len(TOKENS) * len(QUERY_TEMPLATES)}")
    print(f"Output file: {OUTPUT_CSV}")
    print(f"Using 5-second intervals between API requests...")

    estimated_time = (len(TOKENS) * len(QUERY_TEMPLATES) * 6) / 60  # 6 seconds per query (5s delay + 1s processing)
    print(f"Estimated completion time: {estimated_time:.1f} minutes")

    # Prepare CSV data structure
    csv_data = []

    # Statistics tracking
    total_queries = 0
    successful_queries = 0
    partial_success_queries = 0
    failed_queries = 0
    retried_queries = 0
    
    # Process each token
    for token_idx, token in enumerate(TOKENS):
        print(f"\n{'='*60}")
        print(f"Processing token {token} ({token_idx + 1}/{len(TOKENS)})")
        print(f"{'='*60}")
        
        # Prepare row data for this token
        row_data = {"Token": token}
        
        # Process each query template for this token
        for template_idx, template in enumerate(QUERY_TEMPLATES):
            # Add delay between requests (except for the very first request)
            if total_queries > 0:
                print(f"Waiting 5 seconds before next request...")
                time.sleep(5)

            query = generate_query(template, token)
            print(f"  Query {template_idx + 1}/10: {query}")

            # Record start time
            start_time = time.time()

            # Make API call with retry logic
            response_text, is_successful = query_hexa_api_with_retry(
                query, token, template_idx, max_retries=2
            )

            # Calculate and display elapsed time
            elapsed_time = time.time() - start_time
            
            # Store response in row data (use template as column name for consistency)
            column_name = f"Q{template_idx + 1}: {template}"
            row_data[column_name] = response_text
            
            # Update statistics
            total_queries += 1
            if is_successful:
                successful_queries += 1
                print(f"    ✓ Success (took {elapsed_time:.2f}s)")
            else:
                if "No readable response found" in response_text:
                    partial_success_queries += 1
                    print(f"    ⚠ Partial success (took {elapsed_time:.2f}s)")
                else:
                    failed_queries += 1
                    print(f"    ✗ Failed (took {elapsed_time:.2f}s)")

                retried_queries += 1
        
        # Add completed row to CSV data
        csv_data.append(row_data)
        
        print(f"Completed token {token}: {len(row_data)-1} queries processed")
    
    # Write CSV file
    print(f"\n{'='*60}")
    print(f"Writing results to {OUTPUT_CSV}...")
    
    if csv_data:
        # Get all column names (Token + all query columns with template variables)
        fieldnames = ["Token"] + [f"Q{i+1}: {template}" for i, template in enumerate(QUERY_TEMPLATES)]

        with open(OUTPUT_CSV, "w", newline="", encoding="utf-8") as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(csv_data)
        
        print(f"Successfully created {OUTPUT_CSV} with {len(csv_data)} rows")
    else:
        print("No data to write to CSV file")
        return 1
    
    # Print detailed statistics
    print(f"\n{'='*60}")
    print(f"FINAL STATISTICS")
    print(f"{'='*60}")
    print(f"Total tokens processed: {len(TOKENS)}")
    print(f"Total queries processed: {total_queries}")
    print(f"Successful queries: {successful_queries}")
    print(f"Partial success queries: {partial_success_queries}")
    print(f"Failed queries: {failed_queries}")
    print(f"Queries requiring retries: {retried_queries}")
    print(f"Success rate: {(successful_queries / total_queries * 100):.1f}%")
    
    # Return success if >80% of queries were successful
    success_rate = successful_queries / total_queries if total_queries > 0 else 0
    return 0 if success_rate > 0.8 else 1


if __name__ == "__main__":
    sys.exit(main(sys.argv[1:]))
