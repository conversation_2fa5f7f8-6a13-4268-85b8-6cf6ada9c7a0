import asyncio
import json
import os 
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, field_validator
from datetime import datetime, timezone
import sys
import os
from google.adk.tools import FunctionTool, ToolContext
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, SseConnectionParams

# Import campaign data structures from campaign_tool.py
from rag_agent.campaign_tool import (
    format_timestamp,
    format_timestamp_relative, 
    format_timestamp_with_relative,
)
from util.tools.apollo import apollo_helper
from rag_agent.rag_helper.helper import conan_api_request_async

"""
Campaign Query Logic Summary
The DSL query builder uses a hierarchical if-elif-else 
structure based on what parameters the agent provides:

1: Token + Campaign Type

Criteria: Both token AND campaign_type are provided
MUST have BOTH:
- Token match (baseCoinSymbols OR title contains token)
- Campaign type match (announcementType = campaign_type)
Result: Most specific search - campaigns of exact type for
exact token

2: Token Only
Criteria: Only token is provided, no campaign_type
MUST have ANY ONE OF:
- baseCoinSymbols contains token
- title contains token
- announcementSummary contains query phrase
Result: All campaigns related to the token, ranked by relevance

3: Campaign Type Only
Criteria: Only campaign_type is provided, no token
MUST have:
- announcementType = campaign_type
SHOULD have (optional boost):
- announcementSummary contains query phrase
Result: All campaigns of that type, with query matches ranked
higher

Scenario 4: General Search

Criteria: Neither token nor campaign_type provided
MUST have ANY ONE OF:
- title contains query
- baseCoinSymbols contains query
- announcementType contains query
- description contains query
Result: Broad search across all campaign fields
"""

env = os.getenv("ENV", "qa")
if env == "qa":
    MCP_SERVER_URL = "http://bdp-search-mcp.eureka.qa.local:13106/sse" # *************:13106  http://bdp-search-mcp.eureka.qa.local:13106/sse
elif env == "prod":
    MCP_SERVER_URL = "http://bdp-search-mcp.eureka.local:13106/sse"
else:
    raise ValueError(f"Invalid environment: {env}")

# Mock data configuration
USE_CONAN_API = apollo_helper.get_apollo_values("campaign_use_conan_api", "false") == "true"
def get_conan_config():
    return apollo_helper.get_apollo_values("campaign_use_conan_api", "false") == "true"

sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from util.logger.logger_utils import setup_logger
from util.tools.mcp_cleanup import safe_close_mcp_toolset, defensive_mcp_cleanup

logger = setup_logger(__name__)

# Campaign type constants and detection
CAMPAIGN_TYPE_CONFIG = {
    'newListingSpot': {
        'keywords': ['spot', 'listing', 'newlisting', 'new listing', 'list'],
        'required_fields': ['planToOpenMarketTime'],
        'optional_fields': ['tradingPair', 'depositeOpenTime', 'preTradingTime', ]
    },
    'newListingFutures': {
        'keywords': ['futures', 'future', 'derivative', 'contract'],
        'required_fields': ['planToOpenMarketTime'],
        'optional_fields': ['tradingPair', 'contractType']
    },
    'newListingAlpha': {
        'keywords': ['alpha', 'new listing alpha'],
        'required_fields': ['planToOpenMarketTime'],
        'optional_fields': []
    },
    'launchPool': {
        'keywords': ['launchpool', 'launch pool', 'launch', 'pool', 'mining', 'farm', 'farming'],
        'required_fields': ['investStartTime', 'mineEndTime'],
        'optional_fields': ['openTradingTime']
    },
    'tge': {
        'keywords': ['tge', 'token generation', 'generation event', 'event'],
        'required_fields': ['startTime', 'endTime', 'subType'],
        'optional_fields': ['title', 'body', 'totalSales', 'salesPrice']
    },
    'airdrop': {
        'keywords': ['airdrop', 'air drop', 'air', 'drop'],
        'required_fields': ['airdropType', 'airdropStartTime', 'airdropEndTime', 'rewardAmount', 'eligibility', 'claimPoints'],
    },
    'earn': {
        'keywords': ['earn', 'earning', 'earn', 'earning'],
        'required_fields': ['earnType', 'subscriptionStartTime', 'estimatedAPR', 'duration', 'eligibility', 'limitPerUser'],
    },
    'tradingCompetition': {
        'keywords': ['trading competition', 'trading', 'competition'],
        'required_fields': ['startTime', 'endTime', 'rewardPool', 'rewardType', 'rankingCriteria', 'firstPlaceReward'],
    },
    'mission': {
        'keywords': ['mission', 'mission activity'],
        'required_fields': ['startTime', 'endTime'],
        'optional_fields': []
    },
    'campaign': {
        'keywords': ['campaign', 'campaign activity'],
        'required_fields': ['startTime', 'endTime'],
        'optional_fields': []
    }
}

def detect_campaign_type(query: str) -> Optional[str]:
    """
    Detect campaign type from user query
    
    Args:
        query: User query string
        
    Returns:
        Campaign type string or None if no match found
    """
    query_lower = query.lower().strip()
    
    # Check each campaign type for keyword matches
    for campaign_type, config in CAMPAIGN_TYPE_CONFIG.items():
        for keyword in config['keywords']:
            if keyword.lower() in query_lower:
                logger.info(f"Detected campaign type '{campaign_type}' from keyword '{keyword}' in query: {query}")
                return campaign_type
    
    logger.info(f"No specific campaign type detected in query: {query}")
    return None

class CampaignItem(BaseModel):
    id: Optional[str] = None
    title: Optional[str] = None
    announcementType: Optional[str] = None
    announcementTime: Optional[str] = None
    projectIntroduction: Optional[str] = None
    announcementSummary: Optional[str] = None
    baseCoinSymbols: Optional[List[str]] = None
    extendInfo: Optional[Dict[str, Any]] = None
    
    # Additional fields from actual data
    announcementId: Optional[str] = None
    announcementUrl: Optional[str] = None
    subTitle: Optional[str] = None
    status: Optional[str] = None
    update_time: Optional[str] = None
    extendInfoMap: Optional[Dict[str, Any]] = None
    # Separate timeline field (populated by enrich function)
    timeline: Optional[Dict[str, str]] = None
    subType: Optional[str] = None

class AllCampaignExtendInfo(BaseModel):
    # for the optional fields, it's in the extendInfo field, which is a dict
    planToOpenMarketTime: Optional[str] = None
    tradingPair: Optional[str] = None
    depositeOpenTime: Optional[str] = None
    preTradingTime: Optional[str] = None
    contractType: Optional[str] = None
    investStartTime: Optional[str] = None
    mineEndTime: Optional[str] = None
    openTradingTime: Optional[str] = None
    startTime: Optional[str] = None
    endTime: Optional[str] = None
    subType: Optional[str] = None
    title: Optional[str] = None
    body: Optional[str] = None
    totalSales: Optional[str] = None
    salesPrice: Optional[str] = None
    airdropType: Optional[str] = None
    airdropStartTime: Optional[str] = None
    airdropEndTime: Optional[str] = None
    rewardAmount: Optional[str] = None
    eligibility: Optional[str] = None
    claimPoints: Optional[str] = None
    earnType: Optional[str] = None
    subscriptionStartTime: Optional[str] = None
    estimatedAPR: Optional[str] = None
    duration: Optional[str] = None
    limitPerUser: Optional[str] = None
    competitionStartTime: Optional[str] = None
    competitionEndTime: Optional[str] = None
    rewardPool: Optional[str] = None
    rewardType: Optional[str] = None
    rankingCriteria: Optional[str] = None
    firstPlaceReward: Optional[str] = None

def convert_unix_to_datetime_string(timestamp_value: Union[int, str, None]) -> str:
    """
    Convert Unix timestamp to datetime string format
    
    Handles multiple input formats and converts to "YYYY-MM-DD HH:MM:SS" format:
    - Unix milliseconds (e.g., 1754611140000) → "2025-08-15 12:34:00"
    - Unix seconds (e.g., 1754611140) → "2025-08-15 12:34:00" 
    - String Unix timestamps → converted same as above
    - Already formatted strings → returned as-is
    
    Args:
        timestamp_value: Int or string timestamp in various formats
        
    Returns:
        Datetime string in "YYYY-MM-DD HH:MM:SS" format, or original string if already formatted
    """
    if not timestamp_value or timestamp_value == "NA":
        return None
    
    # Handle integer input (Unix timestamp)
    if isinstance(timestamp_value, int):
        try:
            # Check if it's in milliseconds (13 digits) or seconds (10 digits)
            if timestamp_value > 10000000000:  # Milliseconds (13 digits)
                timestamp_seconds = timestamp_value / 1000
            else:  # Seconds (10 digits)
                timestamp_seconds = timestamp_value
            
            dt = datetime.fromtimestamp(timestamp_seconds, tz=timezone.utc)
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, OSError) as e:
            logger.debug(f"Failed to convert Unix timestamp {timestamp_value}: {e}")
            return str(timestamp_value)  # Return as string if conversion fails
    
    # Handle string input
    if isinstance(timestamp_value, str):
        # If it looks like a Unix timestamp (all digits), convert it
        if timestamp_value.isdigit():
            try:
                timestamp_int = int(timestamp_value)
                # Check if it's in milliseconds (13 digits) or seconds (10 digits)
                if timestamp_int > 10000000000:  # Milliseconds (13 digits)
                    timestamp_seconds = timestamp_int / 1000
                else:  # Seconds (10 digits)
                    timestamp_seconds = timestamp_int
                
                dt = datetime.fromtimestamp(timestamp_seconds, tz=timezone.utc)
                return dt.strftime("%Y-%m-%d %H:%M:%S")
            except (ValueError, OSError) as e:
                logger.debug(f"Failed to convert Unix timestamp string {timestamp_value}: {e}")
                return timestamp_value
        else:
            # Already a formatted string, return as-is
            return timestamp_value
    
    return str(timestamp_value) if timestamp_value else None

def parse_timestamp_for_sorting(timestamp_value: Union[int, str, None]) -> int:
    """
    Parse timestamp value to Unix timestamp (seconds) for sorting purposes
    
    Args:
        timestamp_value: Int or string timestamp in various formats
        
    Returns:
        Unix timestamp in seconds, or 0 if parsing fails
    """
    if not timestamp_value or timestamp_value == "NA":
        return 0
    
    # Handle integer input directly
    if isinstance(timestamp_value, int):
        # Check if it's in milliseconds (13 digits) or seconds (10 digits)
        if timestamp_value > 10000000000:  # Milliseconds (13 digits)
            return timestamp_value // 1000
        else:  # Seconds (10 digits)
            return timestamp_value
    
    # Handle string input
    if isinstance(timestamp_value, str):
        # If it looks like a Unix timestamp (all digits), convert it
        if timestamp_value.isdigit():
            timestamp_int = int(timestamp_value)
            # Check if it's in milliseconds (13 digits) or seconds (10 digits)
            if timestamp_int > 10000000000:  # Milliseconds (13 digits)
                return timestamp_int // 1000
            else:  # Seconds (10 digits)
                return timestamp_int
        else:
            # Try parsing as datetime string
            try:
                dt = datetime.strptime(timestamp_value, "%Y-%m-%d %H:%M:%S")
                return int(dt.replace(tzinfo=timezone.utc).timestamp())
            except (ValueError, TypeError) as e:
                logger.debug(f"Failed to parse timestamp for sorting {timestamp_value}: {e}")
                return 0
    
    return 0

def parse_extend_info(campaign: CampaignItem) -> AllCampaignExtendInfo:
    """
    Parse extendInfo dict from CampaignItem into AllCampaignExtendInfo object
    
    Args:
        campaign: CampaignItem with extendInfo dict or extendInfoMap
        
    Returns:
        AllCampaignExtendInfo object with parsed fields
    """
    extend_data = {}
    
    # Try extendInfo first (parsed JSON)
    if campaign.extendInfo and isinstance(campaign.extendInfo, dict):
        extend_data = campaign.extendInfo
    # Fallback to extendInfoMap if available
    elif campaign.extendInfoMap and isinstance(campaign.extendInfoMap, dict):
        extend_data = campaign.extendInfoMap
    
    if not extend_data:
        return AllCampaignExtendInfo()
    
    try:
        # Filter only known fields to avoid validation errors
        known_fields = set(AllCampaignExtendInfo.model_fields.keys())
        filtered_data = {k: v for k, v in extend_data.items() if k in known_fields}
        
        # Convert timestamp fields from int/unix to datetime string format
        timestamp_fields = ['startTime', 'endTime', 'investStartTime', 'mineEndTime', 'planToOpenMarketTime', 
                           'depositeOpenTime', 'preTradingTime', 'openTradingTime', 'airdropStartTime', 
                           'airdropEndTime', 'subscriptionStartTime', 'competitionStartTime', 'competitionEndTime']
        
        for time_field in timestamp_fields:
            if time_field in filtered_data:
                converted_time = convert_unix_to_datetime_string(filtered_data[time_field])
                filtered_data[time_field] = converted_time
        
        return AllCampaignExtendInfo(**filtered_data)
    except Exception as e:
        logger.warning(f"Failed to parse extendInfo: {e}")
        return AllCampaignExtendInfo()

def get_mcp_campaign_sort_key(campaign: CampaignItem) -> tuple:
    """
    Get sorting key for MCP campaign data structure
    
    Sorting logic based on campaign type:
    1. Use campaign-specific key time field from CAMPAIGN_TYPE_CONFIG
    2. Fallback to announcementTime
    3. Final fallback to update_time
    4. Title for final sorting
    """
    time_stamp = 0
    time_field_used = "none"
    
    # Get extended info for campaign-specific timing
    extend_info = parse_extend_info(campaign)
    
    # Define campaign type to key time field mapping based on CAMPAIGN_TYPE_CONFIG
    campaign_time_fields = {
        'newListingSpot': 'planToOpenMarketTime',
        'newListingFutures': 'planToOpenMarketTime', 
        'newListingAlpha': 'planToOpenMarketTime',
        'launchPool': 'investStartTime',
        'tge': 'startTime',
        'airdrop': 'airdropStartTime',
        'earn': 'subscriptionStartTime',
        'tradingCompetition': 'startTime',
        'mission': 'startTime',
        'campaign': 'startTime'
    }
    
    # Try campaign-specific time field first
    campaign_type = campaign.announcementType
    if campaign_type and campaign_type in campaign_time_fields:
        key_field = campaign_time_fields[campaign_type]
        key_time_value = getattr(extend_info, key_field, None)
        
        if key_time_value and key_time_value != "NA":
            time_stamp = parse_timestamp_for_sorting(key_time_value)
            if time_stamp > 0:
                time_field_used = key_field
    
    # Fallback to announcement time
    if time_stamp == 0 and campaign.announcementTime and campaign.announcementTime != "NA":
        time_stamp = parse_timestamp_for_sorting(campaign.announcementTime)
        if time_stamp > 0:
            time_field_used = "announcementTime"
    
    # Final fallback to update_time
    if time_stamp == 0 and campaign.update_time and campaign.update_time != "NA":
        time_stamp = parse_timestamp_for_sorting(campaign.update_time)
        if time_stamp > 0:
            time_field_used = "update_time"
    
    # Title for final sorting
    title = campaign.title or ""
    
    logger.debug(f"Campaign '{title}' ({campaign_type}) - using {time_field_used} for sorting")
    
    return (-time_stamp, title.lower())  # Negative timestamp for newest first

def sort_mcp_campaigns(campaigns: List[CampaignItem]) -> List[CampaignItem]:
    """
    Sort MCP campaign data by campaign-specific key time fields (newest first)
    
    Args:
        campaigns: List of CampaignItem objects from MCP
        
    Returns:
        Sorted list of CampaignItem objects (newest first by relevant time field)
    """
    if not campaigns:
        return campaigns
    
    sorted_campaigns = sorted(campaigns, key=get_mcp_campaign_sort_key)
    
    logger.info(f"MCP campaign sorting completed: {len(campaigns)} campaigns (sorted by campaign-specific time fields - newest first)")
    
    # Show which time field was used for each campaign
    for i, campaign in enumerate(sorted_campaigns):
        extend_info = parse_extend_info(campaign)
        campaign_time_fields = {
            'newListingSpot': 'planToOpenMarketTime',
            'newListingFutures': 'planToOpenMarketTime', 
            'newListingAlpha': 'planToOpenMarketTime',
            'launchPool': 'investStartTime',
            'tge': 'startTime',
            'airdrop': 'airdropStartTime',
            'earn': 'subscriptionStartTime',
            'tradingCompetition': 'competitionStartTime',
            'mission': 'missionStartTime',
            'campaign': 'campaignStartTime'
        }
        
        # Determine which time field was actually used
        time_used = "No time"
        time_source = "none"
        
        if campaign.announcementType and campaign.announcementType in campaign_time_fields:
            key_field = campaign_time_fields[campaign.announcementType]
            key_time_value = getattr(extend_info, key_field, None)
            if key_time_value and key_time_value != "NA":
                time_used = key_time_value
                time_source = key_field
            elif campaign.announcementTime and campaign.announcementTime != "NA":
                time_used = campaign.announcementTime
                time_source = "announcementTime"
            elif campaign.update_time and campaign.update_time != "NA":
                time_used = campaign.update_time
                time_source = "update_time"
        else:
            # For unknown campaign types, use fallback logic
            if campaign.announcementTime and campaign.announcementTime != "NA":
                time_used = campaign.announcementTime
                time_source = "announcementTime"
            elif campaign.update_time and campaign.update_time != "NA":
                time_used = campaign.update_time
                time_source = "update_time"
        
        logger.info(f"  {i+1}. {campaign.title or 'No title'} - Type: {campaign.announcementType} - Time: {time_used} (from {time_source})")
    
    return sorted_campaigns

def format_mcp_campaign_timeline(campaign: CampaignItem) -> Dict[str, str]:
    """
    Format timeline information for MCP campaign data
    
    Args:
        campaign: CampaignItem object
        
    Returns:
        Dictionary with business-relevant timeline fields from extendInfo only
        (excludes administrative times like announcementTime and update_time)
    """
    timeline = {}
    
    # Parse extended info for business-relevant dates only
    extend_info = parse_extend_info(campaign)
    
    # Get campaign type for context-aware field mapping
    campaign_type = campaign.announcementType
    
    # Define campaign-type-specific field mappings
    campaign_type_mappings = {
        'newListingSpot': {
            'planToOpenMarketTime': 'planToOpenMarketTime',
            'depositeOpenTime': 'depositeOpenTime', 
            'preTradingTime': 'preTradingTime',
        },
        'newListingFutures': {
            'planToOpenMarketTime': 'planToOpenMarketTime',
            'depositeOpenTime': 'depositeOpenTime', 
            'preTradingTime': 'preTradingTime',
        },
        'newListingAlpha': {
            'planToOpenMarketTime': 'planToOpenMarketTime',
            'depositeOpenTime': 'depositeOpenTime', 
            'preTradingTime': 'preTradingTime',
        },
        'launchPool': {
            'investStartTime': 'investStartTime',
            'mineEndTime': 'mineEndTime',
            'openTradingTime': 'openTradingTime',
        },
        'tge': {
            'startTime': 'tgeStartTime',
            'endTime': 'tgeEndTime',
        },
        'airdrop': {
            'airdropStartTime': 'airdropStartTime',
            'airdropEndTime': 'airdropEndTime',
        },
        'earn': {
            'subscriptionStartTime': 'subscriptionStartTime',
        },
        'tradingCompetition': {
            'startTime': 'competitionStartTime',
            'endTime': 'competitionEndTime',
        },
        'mission': {
            'startTime': 'missionStartTime',
            'endTime': 'missionEndTime',
        },
        'campaign': {
            # For campaign type, determine subtype-specific mappings
            'startTime': 'campaignStartTime',
            'endTime': 'campaignEndTime',
        }
    }
    
    # Get the appropriate mapping for this campaign type
    if campaign_type in campaign_type_mappings:
        timeline_fields = campaign_type_mappings[campaign_type]
    else:
        # Fallback: use generic campaign mapping
        timeline_fields = campaign_type_mappings['campaign']
    
    # Only add fields that have valid business-relevant values
    for field, display_name in timeline_fields.items():
        value = getattr(extend_info, field, None)
        if value and value != "NA":
            timeline[display_name] = value
    
    return timeline

def enrich_mcp_campaign_with_timeline(campaign: CampaignItem) -> CampaignItem:
    """
    Enrich MCP campaign with formatted timeline information in a separate field
    
    Args:
        campaign: CampaignItem object
        
    Returns:
        CampaignItem with timeline populated in separate field (no text modification)
    """
    # Get formatted timeline and store in separate field
    timeline = format_mcp_campaign_timeline(campaign)
    
    # Always set the timeline field (empty dict if no timeline data)
    campaign.timeline = timeline if timeline else {}
    
    return campaign

def build_mission_campaign_query() -> Dict[str, Any]:
    """
    Build the query structure for mission campaigns
    
    Mission campaigns have:
    - announcementType: "campaign"
    - subType: "MISSION_ACTIVITY" 
    - status: "PUBLISHED" or "PRE_PUBLISHED"
    
    Returns:
        Dictionary representing the mission campaign query structure
    """
    return {
        'bool': {
            'must': [
                {
                    'match': {
                        'announcementType': 'campaign'
                    }
                },
                {
                    'match': {
                        'subType': 'MISSION_ACTIVITY'
                    }
                },
                {
                    'terms': {
                        'status': ['PUBLISHED', 'PRE_PUBLISHED']
                    }
                }
            ]
        }
    }

def enrich_mcp_campaign_with_timeline_text(campaign: CampaignItem, append_to_text: bool = False) -> CampaignItem:
    """
    Enrich MCP campaign with timeline information, optionally appending to text fields
    
    Args:
        campaign: CampaignItem object
        append_to_text: Whether to append timeline text to projectIntroduction/announcementSummary
        
    Returns:
        CampaignItem with timeline in separate field and optionally in text
    """
    # Always populate the separate timeline field
    campaign = enrich_mcp_campaign_with_timeline(campaign)
    
    # Optionally append to text fields if requested
    if append_to_text and campaign.timeline:
        timeline_text = "Timeline:\n"
        for field, value in campaign.timeline.items():
            timeline_text += f"- {field}: {value}\n"
        
        # Add to project introduction or announcement summary
        if campaign.projectIntroduction:
            campaign.projectIntroduction = f"{campaign.projectIntroduction}\n\n{timeline_text}"
        elif campaign.announcementSummary:
            campaign.announcementSummary = f"{campaign.announcementSummary}\n\n{timeline_text}"
        else:
            campaign.projectIntroduction = timeline_text
    
    return campaign

class CampaignDataResponse(BaseModel):
    """Response model for campaign data tool"""
    tool_status: str = Field(description="Status of the tool execution: SUCCESS, FAILED, or PARTIAL")
    error_message: Optional[str] = Field(None, description="Error message if tool execution failed")
    campaign_data: List[CampaignItem] = Field(default_factory=list, description="Campaign data retrieved from MCP tool")

async def fetch_campaign_data(query: str = "", campaign_type: str = "", token: str = "") -> List[CampaignItem]:
    """
    Fetch campaign data using MCP tool 'query campaign by dsl'.
    
    The agent LLM should intelligently analyze the user query and determine appropriate parameters:
    
    Args:
        query: Original user query string (always provided)
        campaign_type: Agent-determined campaign type (newListingSpot, newListingFutures, newListingAlpha, launchPool, tge, airdrop, earn, tradingCompetition, mission, campaign) or empty string
        token: Agent-extracted token symbol (e.g., 'HOME', 'ETH', 'BTC') or empty string  
        language: User's preferred language
        
    Query Size Logic:
        - Campaign-only queries (no token): Returns top 3 results
        - Token + Campaign queries: Returns up to 10 results for better relevance
        
    Returns:
        List[CampaignItem] containing campaign data sorted by update_time desc
    """
    mcp_toolset = None
    try:
        if not USE_CONAN_API:
            logger.info(f"Creating MCP toolset for URL: {MCP_SERVER_URL}")
            mcp_toolset = MCPToolset(connection_params=SseConnectionParams(url=MCP_SERVER_URL, timeout=5, sse_read_timeout=5))
            
            logger.info("Getting available MCP tools...")
            available_tools = await mcp_toolset.get_tools()
            target_tool = next((t for t in available_tools if t.name == "query campaign by dsl"), None)

            if not target_tool:
                logger.error("Tool 'query campaign by dsl' not found")
                return []

        # Use parameters provided by the agent LLM 
        # The agent will determine campaign_type and token based on user query analysis
        logger.info(f"Campaign search - Query: '{query}', Campaign type: '{campaign_type}', Token: '{token}'")
        
        # Determine query size based on scenario
        # Scenario 1: Campaign-only query (no token) -> limit to top 3
        # Scenario 2: Token + Campaign query -> get more results for relevance
        query_size = 5
        
        # Build DSL query following the example structure
        dsl_query = {
            'size': query_size,
            'query': {
                'bool': {
                    'must': []
                }
            }
        }
        # for all queries , need to sort by the update_time desc
        # then after the es sort, need to sort by the anncoumentTime desc.
        dsl_query['sort'] = {
            'update_time': {
                'order': 'desc'
            }
        }
        # Build query based on agent-provided parameters following the example DSL structure
        
        if token and campaign_type:
            # Scenario 2: Token + Campaign query (like your example)
            # Example: agent calls with token="HOME", campaign_type="newListingSpot"
            
            # Build token query - different fields for different campaign types
            if campaign_type == "mission":
                # Mission campaigns don't have baseCoinSymbols, check title and projectIntroduction
                token_query = {
                    'bool': {
                        'should': [
                            {
                                'match_phrase': {
                                    'title': token.upper()
                                }
                            },
                            {
                                'match_phrase': {
                                    'projectIntroduction': token.upper()
                                }
                            }
                        ]
                    }
                }
                campaign_type_query = build_mission_campaign_query()
            else:
                # Other campaign types have baseCoinSymbols
                token_query = {
                    'bool': {
                        'should': [
                            {
                                'match': {
                                    'baseCoinSymbols': token.upper()
                                }
                            },
                            {
                                'match_phrase': {
                                    'title': token.upper()
                                }
                            }
                        ]
                    }
                }
                campaign_type_query = {
                    'match': {
                        'announcementType': campaign_type
                    }
                }
            
            dsl_query['query']['bool']['must'] = [token_query, campaign_type_query]
            
        elif token:
            # Token-only query - agent specifies only token
            dsl_query['query']['bool']['must'] = [
                {
                    'bool': {
                        'should': [
                            {
                                'match': {
                                    'baseCoinSymbols': token.upper()
                                }
                            },
                            {
                                'match_phrase': {
                                    'title': token.upper()
                                }
                            },
                            {
                                'match_phrase': {
                                    'announcementSummary': query
                                }
                            }
                        ]
                    }
                }
            ]
            
        elif campaign_type:
            # Campaign-only query - agent specifies only campaign type (returns top 3)
            # Handle special cases that use subType matching
            if campaign_type in ["mission"]:
                dsl_query['query']['bool']['must'] = [build_mission_campaign_query()]
            elif campaign_type == "campaign":
                dsl_query['query']['bool']['must'] = [
                    {
                        'bool': {
                            'must': [
                                {
                                    'match': {
                                        'announcementType': 'campaign'
                                    }
                                },
                                {
                                    'terms': {
                                        'status': ['PUBLISHED', 'PRE_PUBLISHED']
                                    }
                                }
                            ]
                        }
                    }
                ]
            else:
                # Other campaign types - use announcementType directly
                dsl_query['query']['bool']['must'] = [
                    {
                        'match': {
                            'announcementType': campaign_type
                        }
                    }
                ]
            dsl_query['query']['bool']['should'] = [
                {
                    'match_phrase': {
                        'announcementSummary': query
                    }
                },
                {
                    'match_phrase': {
                        'projectIntroduction': query
                    }
                }
            ]
        else:
            # Build DSL query following the example structure
            dsl_query = {
                'size': 10,
                'query': {
                    'bool': {
                        'must': []
                    }
                }
            }
            # for all queries , need to sort by the update_time desc
            # then after the es sort, need to sort by the anncoumentTime desc.
            dsl_query['sort'] = {
                'update_time': {
                    'order': 'desc'
                }
            }
            # General query - agent doesn't specify specific parameters
            dsl_query['query']['bool']['must'] = [
                {
                    'bool': {
                        'should': [
                            {'match_phrase': {'title': query}},
                            {'match': {'baseCoinSymbols': query}},
                            {'match': {'announcementType': query}},
                            {'match_phrase': {'description': query}},
                            {'match_phrase': {'announcementSummary': query}},
                            {'match_phrase': {'projectIntroduction': query}}
                        ]
                    }
                }
            ]
            # Exclude announcements with type 'Others' when no specific token or campaign type is specified
            dsl_query['query']['bool']['must_not'] = [
                {
                    'match': {
                        'announcementType': 'Others'
                    }
                }
            ]
        
        logger.info(f"DSL Query for campaign search: {json.dumps(dsl_query, indent=2)}")
        mcp_args = {"dsl": json.dumps(dsl_query), 'scene': 'campaignAgent'}
        # Fetch campaign data using MCP tool with DSL query
        if not USE_CONAN_API:
            result = await target_tool.run_async(args=mcp_args, tool_context=None)
            # Validate result content
            if not result or not hasattr(result, 'content') or not result.content:
                logger.error(f"No content returned from MCP tool for campaign query: {query}")
                return []
                
            if not result.content[0] or not hasattr(result.content[0], 'text') or not result.content[0].text:
                logger.error(f"Empty or invalid text content for campaign query: {query}")
                return []
            
            raw_text = result.content[0].text.strip()
            if not raw_text:
                logger.error(f"Empty text content after stripping for campaign query: {query}")
                return []
        else:
            result = await conan_api_request_async(endpoint="/campaign/queryByDSL", method="post", params=mcp_args)
            if not result or not result.get('data'):
                logger.error(f"No data returned from Conan API for campaign query: {query}")
                return []
            
            # For Conan API, the data is already parsed
            campaign_data = result.get('data', {}).get('llmCampaignList', [])
            if not campaign_data:
                logger.error(f"No llmCampaignList returned from Conan API for campaign query: {query}")
                return []
        
        # Handle MCP tool response (JSON string)
        if USE_CONAN_API:
            # Data is already parsed from Conan API
            pass
        else:
            # Parse JSON string from MCP tool
            try:
                parsed_data = json.loads(raw_text)
                campaign_data = parsed_data.get('llmCampaignList', [])
            except json.JSONDecodeError as json_error:
                logger.error(f"Failed to parse JSON for campaign query {query}. Raw content: '{raw_text[:200]}...'. Error: {json_error}")
                return []

        logger.info(f"Fetched campaign data for query '{query}': {len(campaign_data) if isinstance(campaign_data, list) else 'N/A'} items")
        logger.info(f"Campaign data: {campaign_data}")
        # Convert raw data to CampaignItem objects
        campaign_items = []
        if isinstance(campaign_data, list):
            for item_data in campaign_data:
                try:
                    if isinstance(item_data, dict):
                        # Handle extendInfo JSON string parsing
                        processed_item = item_data.copy()
                        if 'extendInfo' in processed_item and isinstance(processed_item['extendInfo'], str):
                            try:
                                processed_item['extendInfo'] = json.loads(processed_item['extendInfo'])
                            except (json.JSONDecodeError, TypeError):
                                logger.warning(f"Failed to parse extendInfo JSON: {processed_item['extendInfo']}")
                                processed_item['extendInfo'] = {}
                        print(f"processed_item: {processed_item}" )
                        campaign_item = CampaignItem(**processed_item)
                        logger.info(f"campaign_item after processing: {campaign_item}")
                        campaign_items.append(campaign_item)
                except Exception as e:
                    logger.warning(f"Failed to parse campaign item: {e}, item: {item_data}")
        elif isinstance(campaign_data, dict):
            # Single campaign item
            try:
                processed_item = campaign_data.copy()
                if 'extendInfo' in processed_item and isinstance(processed_item['extendInfo'], str):
                    try:
                        processed_item['extendInfo'] = json.loads(processed_item['extendInfo'])
                    except (json.JSONDecodeError, TypeError):
                        logger.warning(f"Failed to parse extendInfo JSON: {processed_item['extendInfo']}")
                        processed_item['extendInfo'] = {}
                
                campaign_item = CampaignItem(**processed_item)
                campaign_items.append(campaign_item)
            except Exception as e:
                logger.warning(f"Failed to parse single campaign item: {e}")

        # filter out the campaigns from Others type
        campaign_items = [campaign for campaign in campaign_items if campaign.announcementType != "Others"]
        # Sort campaigns using new MCP helper function
        sorted_campaigns = sort_mcp_campaigns(campaign_items)
        
        # Enrich campaigns with timeline information using new MCP helper function
        enriched_campaigns = []
        for i, campaign in enumerate(sorted_campaigns):
            # Log timeline for debugging
            timeline = format_mcp_campaign_timeline(campaign)
            if timeline:
                logger.info(f"Campaign {i+1} ({campaign.title or 'Unknown'}) timeline:")
                for field, formatted in timeline.items():
                    logger.info(f"  {field}: {formatted}")
            
            # Enrich campaign with timeline information
            enriched_campaign = enrich_mcp_campaign_with_timeline(campaign)
            enriched_campaigns.append(enriched_campaign)
        
        logger.info(f"Processed {len(enriched_campaigns)} campaign items with formatted dates")
        return enriched_campaigns

    except Exception as e:
        logger.error(f"Error fetching campaign data: {e}")
        return []
    finally:
        if mcp_toolset:
            try:
                await safe_close_mcp_toolset(mcp_toolset)
            except Exception as cleanup_e:
                logger.warning(f"Error during MCP toolset cleanup: {cleanup_e}")

fetch_campaign_data_tool  = FunctionTool(func=fetch_campaign_data)