import asyncio
import json
import re
import time
import hashlib
from typing import Dict, Any, Optional
from datetime import datetime
from google.adk.agents import Agent, SequentialAgent
from google.adk.tools import FunctionTool, ToolContext
from google.adk.agents.llm_agent import LlmAgent
from google.adk.agents.callback_context import CallbackContext
from google.adk.models import LlmResponse, LlmRequest
from google.adk.tools.base_tool import BaseTool
from google.genai.types import Content, Part
import sys
import os
import aiohttp
from agent_translate import create_translate_agent 

from util.opik_util import opik_tracer

# Setup GoogleADK instrumentation for Langfuse tracing
from openinference.instrumentation.google_adk import GoogleADKInstrumentor
GoogleADKInstrumentor().instrument()

sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from conf import model_config
from util.logger.logger_utils import setup_logger
from rag_agent.campaign.campaign_mcp_tool import fetch_campaign_data, CampaignDataResponse, fetch_campaign_data_tool
from util.tools.memory import _session_user_contexts
from util.tools.memory import get_current_session_id, set_current_session_id, get_user_context, get_session_context
from util.tools.compliance import is_business_compliance
from rag_agent.campaign.campaign_prompt import CAMPAIGN_ANALYSIS_PROMPT, CAMPAIGN_ANALYSIS_PROMPT_WITHOUT_USER_CONTEXT
from rag_agent.google.google_search_agent import create_campaign_fallback_search_agent
from google.adk.tools import agent_tool
from rag_agent.rag_helper.helper import write_summary_to_storage
from rag_agent.rag_helper.helper import (
    str2number,
    generate_summary_id,
    get_session_id_from_context,
    ensure_session_context,
    store_session_metadata,
    get_all_session_metadata,
    reset_session_metadata,
    get_token_tagging,
    cleanup_session_metadata,
    get_session_metadata,
    clean_google_search_response
)
from rag_agent.rag_helper.helper import prepare_cardsJson
from util.tools.language_check import translate_text, translate_text_with_agent, translate_texts_with_agent_parallel

logger = setup_logger(__name__)

# Get current date for agent instructions
current_date = datetime.now().strftime("%Y-%m-%d")

# Import campaign card configuration class
from rag_agent.campaign.campaign_card_config import CampaignCardConfig


async def create_campaign_cards(campaign_type: str, campaign_metadata_list: list, token: str = "", domain: str = "binance.com", user_language: str = "en", translate_agent=None, session_id: str = "") -> list:
    """
    Create campaign cards data from campaign metadata list using configuration system
    
    Args:
        campaign_type: Type of campaign (e.g., 'newListingSpot', 'launchPool')
        campaign_metadata_list: List of campaign metadata dictionaries (can be None)
        token: Optional token symbol for title
        domain: Domain for deeplink generation
        user_language: User's preferred language for translation
        translate_agent: Pre-created translate agent for efficient translation
        session_id: Session ID for translation context
        
    Returns:
        List of campaign card data dictionaries
    """
    # Handle None or empty campaign metadata list
    if not campaign_metadata_list:
        logger.info(f"No campaign metadata available for campaign type: {campaign_type}")
        return []
    
    campaign_card_config = CampaignCardConfig()
    logger.info(f"Creating campaign cards for campaign type: {campaign_type}")
    campaign_cards_data = []
    
    # Phase 1: Process all campaigns and collect translation tasks
    campaign_data_items = []
    translation_tasks = []
    
    for campaign_dict in campaign_metadata_list[:3]:  # Top 3 campaigns
        # Get the campaign type for this specific campaign
        current_campaign_type = campaign_dict.get('announcementType', '') or campaign_type
        logger.info(f"Campaign type: {current_campaign_type}")
        start_time = campaign_dict.get('announcementTime', '')
        end_time = campaign_dict.get('end_time', '')
        timeline = campaign_dict.get('timeline', {})
        extend_info = campaign_dict.get('extendInfo', {})
        
        # Get configuration for this campaign type
        config = campaign_card_config.get_config(current_campaign_type)
        if not config:
            logger.warning(f"No configuration found for campaign type: {current_campaign_type}")
            continue
        
        # Use configuration to resolve all fields
        title = campaign_card_config.get_field_value(
            config.get('title', {}).get('priority', []),
            campaign_dict, timeline, extend_info, token,
            current_campaign_type, "title"
        )
        
        subtitle = campaign_card_config.get_field_value(
            config.get('subtitle', {}).get('priority', []),
            campaign_dict, timeline, extend_info, token,
            current_campaign_type, "subtitle"
        )
        
        # Store campaign data and collect translation tasks
        campaign_data = {
            'title': title,
            'subtitle': subtitle,
            'campaign_dict': campaign_dict,
            'timeline': timeline,
            'extend_info': extend_info,
            'start_time': start_time,
            'end_time': end_time,
            'config': config,
            'token': token,
            'campaign_type': current_campaign_type
        }
        campaign_data_items.append(campaign_data)
        
        # Collect translation tasks for non-English languages
        if user_language and user_language.lower() != "en" and user_language.lower() != "english":
            try:
                # Get the potential baseCoinSymbol value
                base_coin_symbols = campaign_dict.get('baseCoinSymbols', [])
                base_coin_symbol = base_coin_symbols[0] if base_coin_symbols else ""
                
                # Check if title is using baseCoinSymbol or token - if so, don't translate
                if title and title != base_coin_symbol and title != token:
                    if translate_agent and session_id:
                        translation_tasks.append({
                            'text': title,
                            'field': 'title',
                            'campaign_index': len(campaign_data_items) - 1
                        })
                        logger.info(f"Collected title for translation: '{title}'")
                
                if subtitle:
                    if translate_agent and session_id:
                        translation_tasks.append({
                            'text': subtitle,
                            'field': 'subtitle',
                            'campaign_index': len(campaign_data_items) - 1
                        })
                        logger.info(f"Collected subtitle for translation: '{subtitle}'")
                        
            except Exception as e:
                logger.error(f"Error collecting translation tasks for language {user_language}: {e}")
                # Continue with original text if translation fails
    
    # Phase 2: Perform parallel translation of all collected texts
    if translation_tasks and translate_agent and session_id:
        logger.info(f"Starting parallel translation of {len(translation_tasks)} texts")
        try:
            texts_to_translate = [task['text'] for task in translation_tasks]
            
            # Use the existing parallel translation function
            from util.tools.language_check import translate_texts_with_agent_parallel
            translated_results = await translate_texts_with_agent_parallel(
                texts_to_translate, 
                user_language, 
                translate_agent, 
                session_id
            )
            
            # Apply translated results back to campaign data
            for i, task in enumerate(translation_tasks):
                if i < len(translated_results) and translated_results[i] != task['text']:
                    # Translation was successful and different from original
                    campaign_index = task['campaign_index']
                    field = task['field']
                    campaign_data_items[campaign_index][field] = translated_results[i]
                    logger.info(f"Applied translation for {field}: '{task['text'][:50]}...' -> '{translated_results[i][:50]}...'")
                else:
                    logger.warning(f"Translation failed or unchanged for {task['field']}: {task['text'][:50]}...")
            
        except Exception as e:
            logger.error(f"Error during parallel translation: {e}")
            # Continue with original texts if parallel translation fails
    
    # Phase 3: Create campaign cards using processed data (original or translated)
    for campaign_data in campaign_data_items:
        title = campaign_data['title']
        subtitle = campaign_data['subtitle']
        campaign_dict = campaign_data['campaign_dict']
        timeline = campaign_data['timeline']
        extend_info = campaign_data['extend_info']
        start_time = campaign_data['start_time']
        end_time = campaign_data['end_time']
        config = campaign_data['config']
        token = campaign_data['token']
        campaign_type = campaign_data['campaign_type']
        
        # Get display name and icon type from configuration
        display_card_type = config.get('display_name', campaign_type or "Campaign")
        icon_type = campaign_card_config.get_icon_type(campaign_type, config, campaign_dict, token)
        
        # Get timing values from configuration
        timing_config = config.get('timing', {})
        actual_start_time = campaign_card_config.get_timing_value(
            timing_config.get('start_time', {}),
            timeline, extend_info, start_time, campaign_dict
        )
        actual_end_time = campaign_card_config.get_end_time_value(
            timing_config.get('end_time', {}),
            timeline, extend_info, end_time, campaign_dict
        )
        
        # Get deeplink from configuration
        deeplink_url = campaign_card_config.get_deeplink(config, campaign_dict, token, domain)
        
        # Get listingType for new listing campaigns (static value from config)
        listing_type = config.get('listingType', '')
        
        # Use fixed card name for all campaigns
        card_name = "search_campaign"
        
        # Build card data
        card_props = {
            "CardType": display_card_type,
            "IconType": icon_type,
            "title": title,
            "subTitle": subtitle,
            "startTime": actual_start_time,
            "endTime": actual_end_time,
            "deeplink": deeplink_url
        }
        
        # Add listingType for new listings only
        if listing_type:
            card_props["listingType"] = listing_type
        
        card_data = {
            "data": {
                "name": card_name,
                "props": card_props
            },
            "type": "jarvis.comp"
        }
        campaign_cards_data.append(card_data)
    
    return campaign_cards_data


# Callback to extract campaign args before tool execution (similar to market agent)
def before_tool_callback_extract_campaign_args(tool: BaseTool, args: Dict[str, Any], tool_context: ToolContext) -> None:
    """Extract campaign args from tool context before tool execution"""
    opik_tracer.before_tool_callback(tool, args, tool_context)
    logger.info(f"Campaign Before Tool Callback: Extracted args: {args}")
    # args: {'query': 'bitcoin campaign', 'campaign_type': 'newListingSpot', 'token': 'BTC'}
    session_id = get_session_id_from_context(tool_context)
    if tool.name == 'CampaignSearchFallback':
        logger.info(f"Campaign Before Tool Callback: Skipping args storage for fallback tool: {tool.name}")
        return
    if session_id:
        store_session_metadata(session_id, 'campaign_args', args, "campaign_metadata_")
        store_session_metadata(session_id, 'campaign_token', args.get('token', ''), "campaign_metadata_")
        store_session_metadata(session_id, 'campaign_type', args.get('campaign_type', ''), "campaign_metadata_")

# Callback to extract campaign metadata after tool execution
def extract_campaign_metadata_after_tool_callback(tool: BaseTool, args: Dict[str, Any], tool_context: ToolContext, tool_response: Dict) -> None:
    """Extract campaign data metadata directly from tool response after tool execution"""
    try:
        opik_tracer.after_tool_callback(tool, args, tool_context, tool_response)
        # First, try to ensure session_id is available in context
        session_id = get_session_id_from_context(tool_context)
        
        # If no session_id found, try to get it from thread-local and set it in context
        if not session_id:
            session_id = get_current_session_id()
            if session_id:
                logger.info(f"Setting session_id in tool_context.state from thread-local: {session_id}")
                tool_context.state["session_id"] = session_id
            else:
                logger.warning("No session_id found in tool_context, cannot extract campaign metadata.")
                return
        
        # Ensure the session_id is set in context.state for future use
        if session_id and "session_id" not in tool_context.state:
            tool_context.state["session_id"] = session_id
            logger.info(f"Set session_id in tool_context.state: {session_id}")
        
        # Ensure session context exists
        ensure_session_context(session_id)

        logger.info(f"Campaign After Tool Callback: Extracting metadata for session: {session_id}")
        
        # Check if this is a fallback tool response (CampaignSearchFallback)
        if tool.name == 'CampaignSearchFallback':
            # Google search agent tool is used as fallback, response is in string format
            store_session_metadata(session_id, 'fallback_used', 'google_search', "campaign_metadata_")
            store_session_metadata(session_id, 'has_content', True, "campaign_metadata_")
            store_session_metadata(session_id, 'fallback_body', tool_response, "campaign_metadata_")
            logger.info(f"Campaign After Tool callback for Google search completed - response cleaned")
            return None
        
        # Extract campaign data from tool response and store in session metadata
        # Handle both List[CampaignItem] (direct response) and CampaignDataResponse format
        campaign_data = None
        if isinstance(tool_response, list):
            # Direct response from fetch_campaign_data (List[CampaignItem])
            campaign_data = tool_response
        elif hasattr(tool_response, 'campaign_data') and tool_response.campaign_data:
            # CampaignDataResponse format
            campaign_data = tool_response.campaign_data
        
        if campaign_data:
            campaign_count = len(campaign_data) if isinstance(campaign_data, list) else 0
            
            # Build comprehensive campaign metadata list
            campaign_metadata_list = []
            
            for campaign in (campaign_data if isinstance(campaign_data, list) else []):
                campaign_dict = campaign.model_dump()
                campaign_metadata_list.append(campaign_dict)
            
            # Store single campaign metadata list
            store_session_metadata(session_id, 'card_metadata', campaign_metadata_list, "campaign_metadata_")
            
            logger.debug(f"Campaign After Tool Callback: Stored {campaign_count} campaigns in session metadata")
            if campaign_metadata_list:
                campaign_titles = [c.get('title', 'No title') for c in campaign_metadata_list[:3]]
                campaign_types = list(set([c.get('announcementType', '') for c in campaign_metadata_list if c.get('announcementType')]))
                logger.debug(f"  - Campaign Titles: {campaign_titles}")
                logger.debug(f"  - Campaign Types: {campaign_types}")
            
    except Exception as e:
        logger.error(f"Error extracting campaign metadata: {e}")
        logger.error(f"Campaign Callback context type: {type(tool_response)}")
        logger.error(f"Campaign Callback context attributes: {dir(tool_response)}")

def before_model_callback_campaign_analysis(callback_context: CallbackContext, llm_request: LlmRequest) -> None:
    """Before model callback to cleanup session metadata"""
    opik_tracer.before_model_callback(callback_context, llm_request)
    try:
        # First, try to ensure session_id is available in context
        session_id = get_session_id_from_context(callback_context)
        
        # If no session_id found, try to get it from thread-local and set it in context
        if not session_id:
            
            session_id = get_current_session_id()
            if session_id:
                callback_context.state["session_id"] = session_id
            else:
                logger.warning("No session_id found in any source, cannot check content availability.")
                return None 
        
        # Ensure the session_id is set in context.state for future use
        if session_id and "session_id" not in callback_context.state:
            callback_context.state["session_id"] = session_id
        
        # Ensure session context exists
        ensure_session_context(session_id)
        logger.info(f"Before Model Callback: Checking if content is available for session: {session_id}")
        # Check if any tools have been executed in this session
        user_language = callback_context.state["user_language"]
    except Exception as e:
        logger.error(f"Error getting user language for session {session_id}: {e}, using default language")
        user_language = "en"
    
    if get_session_metadata(session_id, 'fallback_used', "campaign_metadata_") == 'google_search':
        logger.info("Google search fallback detected - returning early response to skip model processing")
        # Get the stored fallback content
        # fallback_title = get_session_metadata(session_id, 'fallback_title', 'Search Result')
        fallback_body = get_session_metadata(session_id, 'fallback_body', "campaign_metadata_", 'No content available')
        user_query = callback_context.state['user_query']
        cleaned_response = clean_google_search_response(fallback_body, user_query, user_language)
        # 返回原始tool response作为news agent的Model response, 
        # 提前结束agent runtime, 这样可以避免重复调用news agent来再处理google search tool返回的内容, 减少一次调用, 优化runtime
        llm_response = LlmResponse(
                    content=Content(
                        role="model",parts=[Part(text=cleaned_response)],
                    )
                )
        opik_tracer.after_model_callback(callback_context, llm_response)
        opik_tracer.after_agent_callback(callback_context)
        return llm_response
    return None

# Before agent callback to cleanup session metadata
def before_agent_callback_campaign_analysis(callback_context: CallbackContext) -> None:
    """Cleanup session metadata before agent starts"""
    opik_tracer.before_agent_callback(callback_context)
    session_id = get_session_id_from_context(callback_context)
    
    # Add session recovery logic like news agent
    if not session_id:
        session_id = get_current_session_id()
        set_current_session_id(session_id=session_id)
    
    # Set basic context in callback state with error handling
    try:
        user_context = get_session_context(session_id)
        user_id = user_context.get("user_id", None)
        user_language = user_context.get("language", None)
        user_query = user_context.get("query", None)
    except (KeyError, TypeError) as e:
        logger.warning(f"Error getting user context for session {session_id}: {e}, using default language")
        user_language = "en"  # Default fallback
    
    callback_context.state["session_id"] = session_id
    callback_context.state["user_language"] = user_language  # Default
    callback_context.state["user_id"] = user_id
    callback_context.state["user_query"] = user_query

    # if already set, skip
    if user_id and user_language:
        cleanup_session_metadata(session_id, meta_prefix="campaign_metadata_")
        return None
    else:
        # check the user query from the callback context event user message
        if hasattr(callback_context, '_invocation_context') and callback_context._invocation_context:
            inv_context = callback_context._invocation_context
            if hasattr(inv_context, 'session') and inv_context.session and hasattr(inv_context.session, 'events'):
                events = inv_context.session.events
                for event in events:
                    if event.content.role == 'user':
                        concat_user_query = event.content.parts[0].text
                        user_query = concat_user_query.split('|')[0].split(':')[1].strip()
                        callback_context.state["user_query"] = user_query
                        user_language = concat_user_query.split('|')[1].split(':')[1].strip()
                        callback_context.state["user_language"] = user_language
                        break
        cleanup_session_metadata(session_id, meta_prefix="campaign_metadata_")
        return None

def create_campaign_analysis_agent(user_query: str, user_language: str, use_user_context: bool = True) -> Agent:
    """Agent responsible for generating campaign analysis in plain text markdown format with tool access for data fetching
    
    Args:
        user_query: The user's query string
        user_language: The user's preferred language
        use_user_context: If True, uses CAMPAIGN_ANALYSIS_PROMPT; if False, uses CAMPAIGN_ANALYSIS_PROMPT_WITHOUT_USER_CONTEXT
    """
    
    # Import the tool function
    from rag_agent.campaign.campaign_mcp_tool import fetch_campaign_data

    # Create Google search agent tool for fallback with simple prompt
    try:
        simple_search_agent = create_campaign_fallback_search_agent()
        google_search_agent_tool = agent_tool.AgentTool(agent=simple_search_agent)
        logger.info("Campaign fallback search agent tool created")
        tools = [fetch_campaign_data_tool, google_search_agent_tool]
    except Exception as e:
        logger.error(f"Error creating Google search agent tool for campaign agent: {e}")
        tools = [fetch_campaign_data_tool]

    # Select prompt based on parameter
    if use_user_context:
        selected_prompt = CAMPAIGN_ANALYSIS_PROMPT
        prompt_format_params = {
            "current_date": current_date,
            "campaign_data": "Campaign data will be retrieved via tools based on user query scenarios",
            "original_query_in_user_language": user_query,
            "user_language": user_language,
        }
    else:
        selected_prompt = CAMPAIGN_ANALYSIS_PROMPT_WITHOUT_USER_CONTEXT
        prompt_format_params = {
            "current_date": current_date,
            "campaign_data": "Campaign data will be retrieved via tools based on user query scenarios",
            "original_query_in_user_language": user_query,
        }

    # Create the agent with tool access and stream-compatible callbacks
    agent = Agent(
        name="CampaignAnalysisAgent",
        model=model_config.MODEL_FLASH,
        generate_content_config=model_config.create_generate_content_config(model_config.CAMPAIGN_AGENT_CONFIG),
        description="Generates comprehensive campaign analysis from Binance campaigns data in plain text markdown format using MCP DSL tool with Google search fallback",
        instruction=selected_prompt.format(**prompt_format_params),
        tools=tools,
        before_agent_callback=before_agent_callback_campaign_analysis,
        after_agent_callback=opik_tracer.after_agent_callback,
        before_model_callback=before_model_callback_campaign_analysis,
        after_model_callback=opik_tracer.after_model_callback,
        before_tool_callback=before_tool_callback_extract_campaign_args,
        after_tool_callback=extract_campaign_metadata_after_tool_callback,  
    )
    
    return agent


async def process_campaign_analysis_with_prefetch(
    analysis_id: str,
    trace_id: str,
    user_query: str,
    user_id: Optional[str] = None,
    user_language: str = "en",
    session_id: Optional[str] = None,
    domain: str = "binance.com",
    agent_runner = None,
    run_config = None,
) -> Dict[str, Any]:
    """
    Process campaign analysis with prefetch approach - fetch campaign data first,
    then let the agent analyze and provide recommendations.
    
    Workflow:
    1. Prefetch campaign data based on query and token
    2. Create agent with prefetched data
    3. Run agent to generate analysis and recommendations
    4. Parse and return structured response
    
    Args:
        analysis_id: Unique identifier for this analysis
        trace_id: Trace ID for logging
        user_query: The user's query string
        user_id: User ID for personalized analysis
        user_language: User's preferred language
        session_id: Session ID for context management
        agent_runner: The agent runner instance
        run_config: Run configuration for the agent
    
    Returns:
        Dict containing status, analysis content, metadata, and storage results
    """
    logger.info(f"Processing campaign analysis with prefetch: '{user_query[:50]}...' for user: {user_id}")
    
    # Ensure session context exists and is clean
    if session_id:
        reset_session_metadata(session_id, meta_prefix="campaign_metadata_")
    
    try:
        # ============================================================================
        # STEP 1: Validate inputs and setup
        # ============================================================================
        if not agent_runner:
            logger.error("No agent runner provided")
            return {
                "status": "error",
                "status_code": 400,
                "error": "No agent runner provided",
                "user_query": user_query,
                "timestamp": datetime.now().isoformat()
            }
        
        # ============================================================================
        # STEP 2: Prefetch campaign data
        # ============================================================================
        logger.info("Prefetching campaign data...")
        token = None 
        try:
            token_tagging = get_token_tagging(user_query, content_id=analysis_id, gptSuccess=False)
            if token_tagging:
                token = token_tagging
        except Exception as e:
            logger.error(f"Error in token tagging: {e}")
            # Keep the original token value
        # ============================================================================
        # STEP 3: Create agent with prefetched data and execute analysis
        # ============================================================================
        logger.info("Creating campaign analysis agent")
        
        # Create agent for streaming markdown output
        agent = create_campaign_analysis_agent(user_query, user_language)
        
        # Create translate agent for this session (to avoid creating new agents per translation)
        translate_agent = None
        if user_language and user_language.lower() != "en" and user_language.lower() != "english":
            try:
                translate_agent = create_translate_agent()
                logger.info("Translate agent created for session")
            except Exception as e:
                logger.warning(f"Failed to create translate agent: {e}")
        
        # Set the agent in the runner
        agent_runner.agent = agent
        
        # Create the query content for the agent
        query_content = Content(
            parts=[Part.from_text(text=f"User Query: {user_query} | User Langauge: {user_language} | Crypto Symbol: {token} | Type: campaign_analysis")],
            role="user"
        )
        
        # Run the agent and collect response
        agent_response = ""
        agent_session_id = session_id or f"temp_campaign_{trace_id}"
        
        logger.info(f"Running campaign agent with prefetched data for session: {agent_session_id}")
        
        # Execute the agent
        response_stream = agent_runner.run_async(
            session_id=agent_session_id,
            user_id=user_id,
            new_message=query_content,
            run_config=run_config
        )
        
        # Collect response from stream
        async for event in response_stream:
            if hasattr(event, 'content') and event.content and event.content.parts:
                for part in event.content.parts:
                    if hasattr(part, 'thought') and part.thought:
                        # remove the thinking content
                        continue
                    if hasattr(part, 'text') and part.text:
                        agent_response += part.text
            
            # Break when turn is complete
            if hasattr(event, 'turn_complete') and event.turn_complete:
                break
        
        if not agent_response:
            logger.error("No response received from campaign analysis agent")
            return {
                "status": "error",
                "status_code": 500,
                "error": "No response received from campaign analysis agent",
                "user_query": user_query,
                "timestamp": datetime.now().isoformat()
            }
        
        logger.info(f"Campaign agent response received: {len(agent_response)} characters")
        
        # ============================================================================
        # STEP 4: Parse and validate response + Extract metadata from session
        # ============================================================================
        logger.info("Processing campaign analysis response and extracting metadata...")
        
        # The response is now plain markdown text
        campaign_analysis_content = agent_response.strip()
        
        # Extract title from markdown (look for first # heading)
        title_match = re.search(r'^##\s*(.+)', campaign_analysis_content, re.MULTILINE)
        title = title_match.group(1).strip() if title_match else "Campaign Analysis"
        logger.info(f"Extracted title: {title}")
        
        # Get campaign metadata from session (stored by after_tool_callback)
        campaign_metadata_list = get_session_metadata(session_id, "card_metadata", "campaign_metadata_")
        # Get token from args metadata if available
        campaign_args = get_session_metadata(session_id, 'campaign_args', "campaign_metadata_")
        campaign_token = campaign_args.get('token', '') if isinstance(campaign_args, dict) else ''
        campaign_type = campaign_args.get("campaign_type", "") if isinstance(campaign_args, dict) else ""
        # Create campaign cards using the new function
        # Store campaign cards in original language (no translation during analysis phase)
        campaign_cards_data = await create_campaign_cards(campaign_type, campaign_metadata_list, campaign_token or token, domain, user_language, None, session_id)
        # Use markdown content for storage
        cardsJson = prepare_cardsJson(campaign_analysis_content, campaign_cards_data)
        
        # ============================================================================
        # STEP 5: Write structured response
        # ============================================================================
        has_response_content = len(agent_response) > 0

        # Write the campaign analysis to the pushAISearchMessage endpoint
        storage_result = await write_summary_to_storage(
            summary_id=analysis_id,
            trace_id=trace_id,
            user_query=user_query,
            summary_content=campaign_analysis_content,
            title=title,
            language=user_language,
            user_id=user_id,
            scene="SRPSearch",
            type="campaign",
            sub_type="online",
            session_id=session_id,
            news_ids=[],
            source_name_list=["Binance Campaigns"],
            cardsJson=cardsJson
        )
        
        logger.info(f"Storage result: {storage_result}")
        
        # Check if we have campaign data or if fallback was used
        has_campaign_data = len(campaign_metadata_list) > 0 if isinstance(campaign_metadata_list, list) else False
        logger.info(f"Campaign data available: {has_campaign_data}, Response content length: {len(agent_response)}")
        
        if not has_response_content:
            # No response content at all
            return {
                "status": "error",
                "status_code": 500,
                "error": "No response generated for the analysis",
                "user_query": user_query,
                "analysis_id": analysis_id,
            }
        
        # Determine source type for response metadata
        source_type = "campaign_data" if has_campaign_data else "google_search_fallback"
        
        # Success case
        return {
            "status": "success",
            "status_code": 200,
            "source": source_type,
            "body": campaign_analysis_content,
            "user_query": user_query,
            "token": campaign_token or token,
            "symbol": campaign_token or token,
            "summaryId": analysis_id,
            "structured_output": False,  # Now streaming markdown
        }
        
    except Exception as e:
        logger.error(f"Error in process_campaign_analysis_with_prefetch: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        return {
            "status": "error",
            "status_code": 500,
            "error": str(e),
            "user_query": user_query,
            "analysis_id": analysis_id,
            "timestamp": datetime.now().isoformat()
        }
    finally:
        # Clean up session metadata
        if session_id:
            cleanup_session_metadata(session_id, "campaign")