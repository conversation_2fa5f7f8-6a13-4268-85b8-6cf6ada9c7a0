{"newListingSpot": {"display_name": "New Listing", "icon_type": "token", "card_name_prefix": "search_campaign", "title": {"priority": ["baseCoinSymbol", "token", "campaign_name"]}, "subtitle": {"priority": ["title", "projectIntroduction", "announcementSummary"]}, "listingType": "spots", "timing": {"start_time": {"timeline": "planToOpenMarketTime", "extendInfo": "planToOpenMarketTime", "fallback": "announcementTime"}, "end_time": {"timeline": "", "extendInfo": "", "fallback": ""}}, "deeplink": {"type": "template", "url": "bnc://app.binance.com/markets/marketsDetail?at=spot&symbol={token}USDT"}}, "newListingFutures": {"display_name": "New Listing", "icon_type": "token", "card_name_prefix": "search_campaign", "title": {"priority": ["baseCoinSymbol", "token", "campaign_name"]}, "subtitle": {"priority": ["title", "projectIntroduction", "announcementSummary"]}, "listingType": "futures", "timing": {"start_time": {"timeline": "planToOpenMarketTime", "extendInfo": "planToOpenMarketTime", "fallback": "announcementTime"}, "end_time": {"timeline": "", "extendInfo": "", "fallback": ""}}, "deeplink": {"type": "template", "url": "bnc://app.binance.com/markets/marketsDetail?at=futures&symbol={token}USDT"}}, "newListingAlpha": {"display_name": "New Listing", "icon_type": "token", "card_name_prefix": "search_campaign", "title": {"priority": ["baseCoinSymbol", "token", "campaign_name"]}, "subtitle": {"priority": ["title", "projectIntroduction", "announcementSummary"]}, "listingType": "alpha", "timing": {"start_time": {"timeline": "planToOpenMarketTime", "extendInfo": "planToOpenMarketTime", "fallback": "announcementTime"}, "end_time": {"timeline": "", "extendInfo": "", "fallback": ""}}, "deeplink": {"type": "template", "url": "bnc://app.binance.com/markets/alpha?at=chain_new"}}, "launchPool": {"display_name": "Launchpool", "icon_type": "token", "card_name_prefix": "search_campaign", "title": {"priority": ["baseCoinSymbol", "token", "campaign_name"]}, "subtitle": {"priority": ["title", "projectIntroduction", "announcementSummary"]}, "timing": {"start_time": {"timeline": "investStartTime", "extendInfo": "investStartTime", "fallback": "announcementTime", "secondary_fallback": "update_time"}, "end_time": {"timeline": "mineEndTime", "extendInfo": "mineEndTime", "fallback": "end_time"}}, "deeplink": {"type": "static", "url": "bnc://app.binance.com/mp/web?appId=pFnC4qaUdJuq4DDvnZJFyo&startPagePath=cGFnZXMvbXAvaG9tZS9pbmRleA"}}, "tge": {"display_name": "TGE", "icon_type": "TGE", "card_name_prefix": "search_campaign", "title": {"priority": ["title", "projectIntroduction", "campaign_name"]}, "subtitle": {"priority": ["projectIntroduction", "announcementSummary"]}, "timing": {"start_time": {"timeline": "tgeStartTime", "extendInfo": "startTime", "fallback": "announcementTime", "secondary_fallback": "update_time"}, "end_time": {"timeline": "tgeEndTime", "extendInfo": "endTime", "fallback": "end_time"}}, "deeplink": {"type": "static", "url": "bnc://app.binance.com/mp/app?appId=xoqXxUSMRccLCrZNRebmzj&startPagePath=cGFnZXMvZGlzY292ZXIvaW5kZXg=&startPageQuery=ZGlzY292ZXItdGFiPVRHRQ=="}}, "airdrop": {"display_name": "Megadrop", "icon_type": "token", "card_name_prefix": "search_campaign", "title": {"priority": ["baseCoinSymbol", "token"]}, "subtitle": {"priority": ["title", "projectIntroduction", "announcementSummary"]}, "timing": {"start_time": {"timeline": "airdropStartTime", "extendInfo": "airdropStartTime", "fallback": "announcementTime", "secondary_fallback": "update_time"}, "end_time": {"timeline": "airdropEndTime", "extendInfo": "airdropEndTime", "fallback": "end_time"}}, "deeplink": {"type": "static", "url": "bnc://app.binance.com/mp/web?appId=pFnC4qaUdJuq4DDvnZJFyo&startPagePath=cGFnZXMvbXAvaG9tZS9pbmRleA"}}, "earn": {"display_name": "Campaign", "icon_type": "CAMPAIGN", "card_name_prefix": "search_campaign", "title": {"priority": ["title", "projectIntroduction", "campaign_name"]}, "subtitle": {"priority": ["projectIntroduction", "announcementSummary"]}, "timing": {"start_time": {"timeline": "subscriptionStartTime", "extendInfo": "subscriptionStartTime", "fallback": "announcementTime", "secondary_fallback": "update_time"}, "end_time": {"timeline": "duration", "extendInfo": "duration", "fallback": "end_time", "special": "duration_text"}}, "deeplink": {"type": "earn_url"}}, "tradingCompetition": {"display_name": "Campaign", "icon_type": "CAMPAIGN", "card_name_prefix": "search_campaign", "title": {"priority": ["title", "projectIntroduction", "campaign_name"]}, "subtitle": {"priority": ["projectIntroduction", "announcementSummary"]}, "timing": {"start_time": {"timeline": "competitionStartTime", "extendInfo": "startTime", "fallback": "announcementTime", "secondary_fallback": "update_time"}, "end_time": {"timeline": "competitionEndTime", "extendInfo": "endTime", "fallback": "end_time"}}, "deeplink": {"type": "announcement_url"}}, "mission": {"display_name": "Campaign", "icon_type": "CAMPAIGN", "card_name_prefix": "search_campaign", "title": {"priority": ["title", "projectIntroduction", "campaign_name"]}, "subtitle": {"priority": ["projectIntroduction", "announcementSummary"]}, "timing": {"start_time": {"timeline": "missionStartTime", "extendInfo": "startTime", "fallback": "announcementTime", "secondary_fallback": "update_time"}, "end_time": {"timeline": "endTime", "extendInfo": "endTime", "fallback": "end_time"}}, "deeplink": {"type": "announcement_url"}}, "campaign": {"display_name": "Campaign", "icon_type": "CAMPAIGN", "card_name_prefix": "search_campaign", "title": {"priority": ["title", "projectIntroduction", "campaign_name"]}, "subtitle": {"priority": ["projectIntroduction", "announcementSummary"]}, "timing": {"start_time": {"timeline": "campaignStartTime", "extendInfo": "startTime", "fallback": "announcementTime", "secondary_fallback": "update_time"}, "end_time": {"timeline": "endTime", "extendInfo": "endTime", "fallback": "end_time"}}, "deeplink": {"type": "announcement_url"}}}