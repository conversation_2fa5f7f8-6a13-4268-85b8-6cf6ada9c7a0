import json
import os
from typing import Dict, Any
import base64
from util.logger.logger_utils import setup_logger

logger = setup_logger(__name__)


class CampaignCardConfig:
    """Configuration class for campaign card generation"""
    
    def __init__(self):
        self._config = None
        self.load_config()
    
    def load_config(self):
        """Load configuration from JSON file"""
        config_path = os.path.join(os.path.dirname(__file__), 'campaign_card_config.json')
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                self._config = json.load(f)
                logger.info(f"Loaded campaign card configuration with {len(self._config)} campaign types")
        except Exception as e:
            logger.error(f"Failed to load campaign card configuration: {e}")
            self._config = {}
    
    def get_config(self, campaign_type: str) -> Dict[str, Any]:
        """Get configuration for a specific campaign type"""
        return self._config.get(campaign_type, {})
    
    def get_field_value(self, config_priority: list, campaign_dict: dict, timeline: dict, extend_info: dict, token: str, campaign_type: str = None, field_type: str = None) -> str:
        """Get field value based on priority configuration"""
        # Special handling for campaign subtitle - return empty if title exists (no space for display)
        if campaign_type in ["tge", "earn", "tradingCompetition", "mission", "campaign"] and field_type == "subtitle":
            title_value = self._get_single_field_value("title", campaign_dict, timeline, extend_info, token)
            if title_value:
                return ""
        
        for source in config_priority:
            # Get field value
            value = self._get_single_field_value(source, campaign_dict, timeline, extend_info, token)
            if value:
                return value
        
        return ""
    
    def _get_single_field_value(self, field_name: str, campaign_dict: dict, timeline: dict, extend_info: dict, token: str) -> str:
        """Get value from a single field source"""
        if field_name == "baseCoinSymbol":
            base_coin_symbols = campaign_dict.get('baseCoinSymbols', [])
            return base_coin_symbols[0] if base_coin_symbols else ""
        elif field_name == "token":
            return token or ""
        elif field_name == "campaign_name":
            value = campaign_dict.get('title', '')
            return value if value and value != "NA" else ""
        elif field_name == "projectIntroduction":
            value = campaign_dict.get('projectIntroduction', '')
            return value if value and value != "NA" else ""
        elif field_name == "announcementSummary":
            value = campaign_dict.get('announcementSummary', '')
            return value if value and value != "NA" else ""
        else:
            value = campaign_dict.get(field_name, '')
            return value if value and value != "NA" else ""
    
    def get_timing_value(self, timing_config: dict, timeline: dict, extend_info: dict, fallback_value: str, campaign_dict: dict = None) -> str:
        """Get timing value based on timing configuration"""
        # logger.debug(f"Timing resolution - config: {timing_config}, fallback: {fallback_value}, has_campaign_dict: {campaign_dict is not None}")
        
        # Try timeline first
        if timeline and timing_config.get("timeline"):
            timeline_value = timeline.get(timing_config["timeline"])
            if timeline_value:
                # logger.debug(f"Using timeline value: {timeline_value}")
                # Convert Unix timestamp if needed
                converted_timeline = self._convert_timestamp(timeline_value)
                if converted_timeline:
                    return converted_timeline
        
        # Try extendInfo
        if extend_info and timing_config.get("extendInfo"):
            extend_value = extend_info.get(timing_config["extendInfo"])
            if extend_value:
                # logger.debug(f"Using extendInfo value: {extend_value}")
                # Convert Unix timestamp if needed
                converted_extend = self._convert_timestamp(extend_value)
                if converted_extend:
                    return converted_extend
        
        # Primary fallback
        if fallback_value:
            converted_fallback = self._convert_timestamp(fallback_value)
            if converted_fallback:
                # logger.debug(f"Using primary fallback: {converted_fallback}")
                return converted_fallback
        
        # Secondary fallback (use update_time if available)
        if campaign_dict and timing_config.get("secondary_fallback"):
            secondary_value = campaign_dict.get(timing_config["secondary_fallback"])
            if secondary_value:
                # logger.debug(f"Using secondary fallback ({timing_config['secondary_fallback']}): {secondary_value}")
                return self._convert_timestamp(secondary_value)
        
        return ""
    
    def get_end_time_value(self, timing_config: dict, timeline: dict, extend_info: dict, fallback_value: str, campaign_dict: dict = None) -> str:
        """Get end time value with special handling for duration_text (Earn campaigns)"""
        
        # Special handling for duration_text (Earn campaigns)
        if timing_config.get("special") == "duration_text":
            # Try to get duration from timeline or extendInfo
            duration_value = None
            if timeline and timing_config.get("timeline"):
                duration_value = timeline.get(timing_config["timeline"])
            if not duration_value and extend_info and timing_config.get("extendInfo"):
                duration_value = extend_info.get(timing_config["extendInfo"])
            if not duration_value and fallback_value:
                duration_value = fallback_value
            
            if duration_value == "Flexible" or duration_value == "NA":
                return ""
            # Get start time from campaign data to calculate actual end date
            if duration_value and campaign_dict:
                # Try to find start time from various possible fields
                start_time_candidates = [
                    campaign_dict.get('startTime'),
                    campaign_dict.get('subscriptionStartTime'), 
                    campaign_dict.get('announcementTime'),
                    campaign_dict.get('update_time')
                ]
                
                start_time_value = None
                for candidate in start_time_candidates:
                    if candidate:
                        # Convert timestamp to string format if needed
                        if isinstance(candidate, (int, float)):
                            start_time_value = self._convert_timestamp(candidate)
                        else:
                            start_time_value = str(candidate)
                        break
                
                # Calculate end date from start time + duration
                if start_time_value:
                    calculated_end_time = self.calculate_end_time_from_duration(start_time_value, str(duration_value))
                    if calculated_end_time:
                        return calculated_end_time
        
        # Fall back to standard timing value handling
        return self.get_timing_value(timing_config, timeline, extend_info, fallback_value, campaign_dict)
    
    def calculate_end_time_from_duration(self, start_time: str, duration: str) -> str:
        """Calculate end time by adding duration to start time"""
        if not start_time or not duration:
            return ""
        
        try:
            from datetime import datetime, timedelta
            import re
            
            # Parse start time
            start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
            
            # Extract number and unit from duration (e.g., "7 days", "150 days")
            match = re.match(r'(\d+)\s*(day|days)', duration.lower())
            if match:
                days = int(match.group(1))
                end_dt = start_dt + timedelta(days=days)
                return end_dt.strftime("%Y-%m-%d %H:%M:%S")
            
            # If duration format is not recognized, return empty
            return ""
        except Exception as e:
            logger.warning(f"Failed to calculate end time from duration {duration}: {e}")
            return ""
    
    def _convert_timestamp(self, value) -> str:
        """Convert Unix timestamp to datetime string if needed"""
        # Skip "NA" values
        if not value or value == "NA":
            return ""
            
        # If it's already a string in datetime format, return as is
        if isinstance(value, str):
            return value
        
        # If it's a Unix timestamp (milliseconds), convert to datetime string
        if isinstance(value, (int, float)) and value > 1000000000000:  # Millisecond timestamp
            try:
                from datetime import datetime
                dt = datetime.fromtimestamp(value / 1000)  # Convert from milliseconds
                return dt.strftime("%Y-%m-%d %H:%M:%S")
            except Exception as e:
                logger.warning(f"Failed to convert timestamp {value}: {e}")
                return ""
        
        # If it's a Unix timestamp (seconds), convert to datetime string  
        elif isinstance(value, (int, float)) and value > 1000000000:  # Second timestamp
            try:
                from datetime import datetime
                dt = datetime.fromtimestamp(value)
                return dt.strftime("%Y-%m-%d %H:%M:%S")
            except Exception as e:
                logger.warning(f"Failed to convert timestamp {value}: {e}")
                return ""
        
        # Return empty string for other cases to avoid "NA"
        return ""
    
    def get_icon_type(self, campaign_type: str, config: dict, campaign_dict: dict, token: str) -> str:
        """Get icon type based on configuration"""
        icon_type_config = config.get("icon_type", "campaign")
        
        if icon_type_config == "token":
            # Use actual token symbol
            base_coin_symbols = campaign_dict.get('baseCoinSymbols', [])
            if base_coin_symbols:
                return base_coin_symbols[0]
            elif token:
                return token
            else:
                return campaign_type.lower() if campaign_type else "campaign"
        else:
            # Use static icon type
            return icon_type_config
    
    def get_deeplink(self, config: dict, campaign_dict: dict, token: str, domain: str = "binance.com") -> str:
        """Get deeplink based on configuration"""
        deeplink_config = config.get("deeplink", {})
        deeplink_type = deeplink_config.get("type", "announcement_url")
        
        if deeplink_type == "static":
            # Use static URL
            logger.info(f"deeplink: {deeplink_config.get('url', 'pending_verification')}")
            return deeplink_config.get("url", "pending_verification")
        elif deeplink_type == "template":
            # Use template URL with token substitution
            url_template = deeplink_config.get("url", "")
            base_coin_symbols = campaign_dict.get('baseCoinSymbols', [])
            token_symbol = base_coin_symbols[0] if base_coin_symbols else token
            logger.info(f"url_template: {url_template}, token_symbol: {token_symbol}")
            logger.info(f"deeplink: {url_template.format(token=token_symbol) if token_symbol else url_template}")
            return url_template.format(token=token_symbol) if token_symbol else url_template
        elif deeplink_type == "announcement_url":
            # Use announcement URL from campaign data, concatenate with domain if needed
            announcement_url = campaign_dict.get('announcementUrl', 'pending_verification')
            if announcement_url and announcement_url != 'pending_verification':
                # check if the domain have www. 
                if "https:" in announcement_url:
                    http_url = announcement_url
                elif "www." in domain:
                    http_url = f"https://{domain}{announcement_url}"
                else:
                    http_url = f"https://www.{domain}{announcement_url}"
                print(f"http_url: {http_url}")
                # Deeplink URL format: bnc://app.binance.com/webview/webview?type=default&needDynamic=true&url={base64(http url)}
                # This format wraps HTTP URLs in Binance app webview with base64 encoding
                encoded_url = base64.b64encode(http_url.encode('utf-8')).decode('utf-8')
                logger.info(f"encoded_url: {encoded_url}")
                deeplink_url = f"bnc://app.binance.com/webview/webview?type=default&needDynamic=true&url={encoded_url}"
                return deeplink_url
            # Fallback for pending verification
            return announcement_url
        elif deeplink_type == "earn_url":
            # Based on the earnType from campaign dict's extendInfo, return the corresponding url
            earn_type = campaign_dict.get('extendInfo', {}).get('earnType', '')
            
            # Default to Simple Earn-Flexible if earnType is empty/null/NA
            if not earn_type or earn_type.lower() in ['na', 'null', '']:
                earn_type = "Simple Earn-Flexible"
            
            asset = campaign_dict.get('baseCoinSymbols', [])
            asset_symbol = asset[0] if asset else token
            
            # "Simple Earn-Flexible" , "Simple Earn - Locked Staking", "Simple Earn - SOL Staking",
            #  "Advanced Earn - Dual Investment","Advanced Earn - Smart Arbitrage","Advanced Earn - On-Chain Yields"
            # Map earnType to corresponding deeplink URLs
            earn_type_urls = {
                "Simple Earn-Flexible": f"bnc://app.binance.com/earns/simpleBuy?product={asset_symbol}001&asset={asset_symbol}",
                "Simple Earn - Locked Staking": f"bnc://app.binance.com/earns/simpleBuy?product={asset_symbol}*120&duration=120&asset={asset_symbol}",
                "Simple Earn - SOL Staking": "bnc://app.binance.com/earns/solLanding",
                "ETH Staking": "bnc://app.binance.com/earns/eth2",
                "SOL Staking": "bnc://app.binance.com/earns/solLanding",
                "Advanced Earn - Dual Investment": f"bnc://app.binance.com/earns/dualMain?asset={asset_symbol}",
                "Advanced Earn - On-Chain Yields": "bnc://app.binance.com/mp/app?appId=TMZakPNdh8ySzQF3GxSL3B&startPagePath=cGFnZXMvaW5kZXg",
                # 'RWUSD': "/earn/rwa-rwusd", ? 
                # 'BFUSD': "/bfusd" ? 
            }
            # Return the appropriate URL based on earnType
            earn_url = earn_type_urls.get(earn_type, 'pending_verification')     
            logger.info(f"earn_type: {earn_type}, earn_url: {earn_url}")
            return earn_url
        elif deeplink_type == "pending":
            # Pending verification
            return "pending_verification"
        else:
            # Default fallback
            return campaign_dict.get('announcementUrl', 'pending_verification')