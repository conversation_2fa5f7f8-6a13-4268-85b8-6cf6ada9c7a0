import asyncio
import json

from util.logger.logger_utils import setup_logger
from util.tools.memory import get_session_context, queue_websocket_message, get_current_session_id

logger = setup_logger(__name__)

# def send_trade_agent_response_to_client( response: str) -> None:
#     """
#     send the response to the client.
#     """
#     session_id = get_current_session_id()
#     if not session_id:
#         logger.error("No session ID found at send_trade_agent_response_to_client")
#         return
#     # Create the nested message structure
#     final_payload = {
#                     "type": 2,
#                     "message": {
#                         "sub_type": 200,
#                         "msg": response
#                     }
#                     }
    
#     logger.info(f"Queueing websocket message for session {session_id}: {final_payload}")
    
#     # Queue the message for the websocket handler to send using asyncio.create_task
#     # This schedules the async call without blocking the synchronous function
#     try:
#         loop = asyncio.get_event_loop()
#         task = loop.create_task(queue_websocket_message(session_id, final_payload))
#         logger.info("Websocket message queued successfully")
#     except RuntimeError:
#         # If no event loop is running, we can't schedule the task
#         logger.error("No event loop running, cannot queue websocket message")
        

def launch_spot_transaction_card(trade_dict: dict) -> str:
        """ launch_spot_transaction_card. input trade_dict, like {
            "base_coin": "BTC",
            "quote_coin": "USDT", 
            "amount": 0.001,
            "amount_base": "BTC",
            "side": "buy",
            "type": "market"
        }
        return explain_spot_transaction(trade_dict)
        return example1: BTC现在市场价格为10500 USDT，BNB现在市场价格为768 USDT，0.01 BTC 可以兑换 0.15 BNB
        return example1: BNB现在市场价格为768 USDT, 100 USDT 可以兑换 0.15 BNB
        """
        # Get the current session ID from thread-local storage
        session_id = get_current_session_id()
        logger.info(f"launch_spot_transaction_card: session_id: {session_id}, trade_dict: {trade_dict}")
        if not session_id:
            logger.error("No session ID found at launch_spot_transaction_card")
            return "Error No session ID found at launch_spot_transaction_card"
        
        # Ensure the convert_dict has the expected format
        if isinstance(trade_dict, dict) and "base_coin" in trade_dict:
            # Convert trade_dict to the expected format
            trade_data = {
                "from_token": trade_dict.get("base_coin", "BTC"),
                "to_token": trade_dict.get("quote_coin", "USDT"),
                "amount": trade_dict.get("amount", 0.001),
                "amount_base": trade_dict.get("amount_base", "BTC")
            }
            
            # Store the convert data in session memory for the root agent to use later
            trade_data_json = json.dumps(trade_data)
            session_context = get_session_context(session_id)
            if session_context:
                session_context["pending_spot_card"] = trade_data_json
                logger.info(f"Stored spot data in session context: {trade_data_json}")
            
            # Return success message - the root agent will handle launching the card
            #return f"Convert data prepared and stored. Root agent will launch the card after text response."
            return explain_spot_transaction(trade_dict)
        else:
            logger.error(f"Invalid trade_dict format: {trade_dict}")
            return "Error Invalid trade_dict format at launch_spot_transaction_card"
            

async def queue_spot_card(trade_data_json: str) -> str:
    """Queue a spot card message to be sent after text response"""
    session_id = get_current_session_id()
    if not session_id:
        logger.error("No session ID found at queue_spot_card")
        return "Error No session ID found at queue_spot_card"
    
    try:
        # Parse the convert data if it's a string
        if isinstance(trade_data_json, str):
            trade_data = json.loads(trade_data_json)
        else:
            trade_data = trade_data_json
        
        # Create the nested message structure
        websocket_message = {
            "sub_type": 202,
            "msg": trade_data
        }
        
        # Create the final websocket payload
        final_payload = {
            "type": 2,
            "message": websocket_message
        }
        
        logger.info(f"Queueing convert card message for session {session_id}: {final_payload}")
        
        # Queue the message for the websocket handler to send
        await queue_websocket_message(session_id, final_payload)
        logger.info("Spot card message queued successfully")
        
        return "Success queued spot card message"
        
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse convert data JSON: {e}")
        return f"Error: Failed to parse convert data JSON: {e}"
    except Exception as e:
        logger.error(f"Error queueing convert card: {e}")
        return f"Error: {e}"


def get_pending_spot_card(session_id: str) -> str:
    """Get pending spot card data from session context"""
    session_context = get_session_context(session_id)
    if session_context and "pending_spot_card" in session_context:
        trade_data = session_context.pop("pending_spot_card")  # Remove after getting
        logger.info(f"Retrieved pending spot card data: {trade_data}")
        return trade_data
    return None


from agent_convert.price_tool import get_price
from agent_convert.price_tool import format_number_for_display


def explain_spot_transaction(trade_dict: dict) -> str:
        """ input trade_dict, like {
            "base_coin": "BTC",
            "quote_coin": "BNB", 
            "amount": 0.001,
            "amount_base": "BTC",
            "side": "buy",
            "type": "market"
        }

        return example1: BTC现在市场价格为10500 USDT，BNB现在市场价格为768 USDT, 0.01 BTC 约等于 0.15 BNB
        return example1: BNB现在市场价格为768 USDT, 100 USDT 约等于 0.15 BNB

        """
        base_coin = trade_dict.get("base_coin", "BTC")
        quote_coin = trade_dict.get("quote_coin", "USDT")
        amount = trade_dict.get("amount", 0.001)
        amount_base = trade_dict.get("amount_base", "BTC")

        return_str = ""
        
        try:
            # Type checking and conversion for amount
            if isinstance(amount, (list, tuple)):
                logger.warning(f"Amount is a sequence: {amount}, taking first element")
                amount = amount[0] if amount else 0.001
            elif not isinstance(amount, (int, float)):
                logger.warning(f"Amount is not a number: {amount} (type: {type(amount)}), using default")
                amount = 0.001
            
            # Convert to float to ensure it's a number
            amount = float(amount)
            
            # Get current prices for both coins
            
            if base_coin in ["USDT", "USDC", "USDC.e"]:
                base_price = 1.0
            else:
                base_price = get_price(base_coin)
                # Type checking for base_price
                if isinstance(base_price, (list, tuple)):
                    logger.warning(f"base_price is a sequence: {base_price}, taking first element")
                    base_price = base_price[0] if base_price else -1
                elif not isinstance(base_price, (int, float)):
                    logger.warning(f"base_price is not a number: {base_price} (type: {type(base_price)}), using -1")
                    base_price = -1
                base_price = float(base_price)
            
            if quote_coin in ["USDT", "USDC", "USDC.e"]:
                quote_price = 1.0
            else:
                quote_price = get_price(quote_coin)
                # Type checking for quote_price
                if isinstance(quote_price, (list, tuple)):
                    logger.warning(f"quote_price is a sequence: {quote_price}, taking first element")
                    quote_price = quote_price[0] if quote_price else -1
                elif not isinstance(quote_price, (int, float)):
                    logger.warning(f"quote_price is not a number: {quote_price} (type: {type(quote_price)}), using -1")
                    quote_price = -1
                quote_price = float(quote_price)
            
            if base_price < 0 or quote_price < 0:
                logger.warning(f" Unable to get price for {base_coin} or {quote_coin}")
                return return_str
            
            # Calculate the conversion
            if amount_base == base_coin:
                # Converting from coin amount to to_coin
                base_amount = amount
                transaction_value_usdt = base_amount * base_price
                quote_amount = transaction_value_usdt / quote_price
    
            
            elif amount_base == quote_coin:
                quote_amount = amount
                transaction_value_usdt = quote_amount * quote_price
                base_amount = transaction_value_usdt / base_price
                            
            else:
                logger.warning(f" explain_spot_transaction: Unsupported amount_base: {amount_base}, trade_dict: {trade_dict}")
                return return_str
            
            if base_coin not in ["USDT", "USDC", "USDC.e"]:
                price_str = format_number_for_display(base_price)
                return_str = return_str + f"Current market price of {base_coin} is about {price_str} USDT，"
            if quote_coin not in ["USDT", "USDC", "USDC.e"]:
                price_str = format_number_for_display(quote_price)
                return_str = return_str + f"Current market price of {quote_coin} is about {price_str} USDT，"
            base_amount_str = format_number_for_display(base_amount)
            quote_amount_str = format_number_for_display(quote_amount)
            return_str = return_str + f"{base_amount_str} {base_coin} equals about {quote_amount_str} {quote_coin}"
            logger.info("transaction_value_usdt: "+str(transaction_value_usdt) + " base_coin: " + base_coin + "base_amount: " + str(base_amount)+" base_amount_str: " + base_amount_str)
            logger.info(" quote_coin: " + quote_coin + " quote_amount: " + str(quote_amount) + " quote_amount_str: " + quote_amount_str + " amount_base: " + amount_base)
            logger.info(" explain_spot_transaction: return_str:" + return_str)

            return return_str
                
        except Exception as e:
            logger.error(f"Error in explain_convert_transaction: {e}")
            return "Can not find price right now. Do not show coin price in the response to user."
        