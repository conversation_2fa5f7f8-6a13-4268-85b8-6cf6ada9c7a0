import json

from google.adk import Agent

from agent_asset.query_asset import query_user_asset
from agent_convert import convert
from agent_spot import prompt
from agent_spot.chat_spot_tool import launch_spot_transaction_card, queue_spot_card, get_pending_spot_card
from agent_convert.convert_pair import get_hot_coin_convert_pair

from conf import model_config
from util.logger.logger_utils import setup_logger
from util.tools.memory import get_session_context, get_current_session_id
from google.adk.agents.callback_context import CallbackContext

logger = setup_logger(__name__)

def process_convert_intent(user_intent: str) -> str:
    """
    input:
        user_intent: str, agent should generate and input it. For example: "User want to convert 100 USDT to BTC", "User want to convert 0.001 BTC to USDT"
    output:
        str, 交易意图
    """
    try:
        # Get the current session ID from thread-local storage
        session_id = get_current_session_id()
        if not session_id:
            logger.error("process_convert_intent called without session context")
            return json.dumps({"error": "Session context not available"})
        
        # step 1: get session context
        session_context = get_session_context(session_id)
        if not session_context:
            logger.warning(f"No session context found for session_id: {session_id}")
            user_id = session_id  # fallback to using session_id as user_id
        else:
            user_id = session_context.get('user_id') or session_id
        
        logger.info(f"Processing convert intent for session_id: {session_id}, user_id: {user_id}")
        
        # step 2: form a request dict {"user_id": user_id, "user_query": user_intent}
        request_dict = {
            "user_id": user_id,
            "user_query": user_intent
        }
        
        logger.info(f"Formed request dict: {request_dict}")
        
        # step 3: call the convert.recommend_convert_token_endpoint function
        recommendation = convert.recommend_convert_token_endpoint(request_dict)
        
        logger.info(f"Got recommendation: {recommendation}")

    except Exception as e:
        logger.error(f"Error in process_convert_intent: {e}")
        return json.dumps({"error": str(e)})
        

async def get_trading_agent_data():
    """
    Get user asset and hot coin convert pair data directly without using the callback.
    Returns:
        tuple: (user_asset, hot_coin_convert_pair)
    """
    logger.info("Getting trading agent data directly")
    
    # Get session_id from current context
    session_id = get_current_session_id()
    logger.info(f"[DEBUG] get_trading_agent_data - session_id: {session_id}")
    
    # query user asset with session_id
    user_asset = await query_user_asset(session_id)
    logger.info(f"User asset: {user_asset}")
    # query hot coin convert pair
    hot_coin_convert_pair = get_hot_coin_convert_pair()
    logger.info(f"Hot coin convert pair: {hot_coin_convert_pair}")
    
    return user_asset, hot_coin_convert_pair


async def create_trading_agent():
    """
    Create trading agent with resolved instruction using actual data.
    """
    # Get the data directly
    user_asset, hot_coin_convert_pair = await get_trading_agent_data()
    
    # Resolve the instruction with actual data
    resolved_instruction = prompt.TRADING_AGENT_INSTRUCTION.replace(
        "{user_asset_data}", str(user_asset)
    ).replace(
        "{hot_coin_convert_pair}", str(hot_coin_convert_pair)
    )
    
    logger.info(f"Creating trading agent with resolved instruction. resolved_instruction: {resolved_instruction}")
    
    # Create a session-aware callback for the trading agent
    def trading_session_callback(callback_context: CallbackContext) -> None:
        """Session-aware callback for trading agent to ensure session context is available"""
        try:
            # Get session_id from current context
            session_id = get_current_session_id()
            if session_id:
                logger.info(f"[DEBUG] Trading agent session callback - session_id: {session_id}")
                # Set session_id in callback context state
                callback_context.state["session_id"] = session_id
                
                # Get user context
                from util.tools.memory import get_user_context
                user_id, user_language = get_user_context(session_id)
                if user_id:
                    callback_context.state["user_id"] = user_id
                    callback_context.state["user_language"] = user_language or "english"
                    logger.info(f"[DEBUG] Trading agent session callback - set user_id: {user_id}, language: {user_language}")
            else:
                logger.warning("[DEBUG] Trading agent session callback - no session_id found")
        except Exception as e:
            logger.error(f"[DEBUG] Error in trading agent session callback: {e}")
    
    return Agent(
        name="SpotTradingAgent",
        description="解析用户交易意图",
        instruction=resolved_instruction,
        # model=model_config.MODEL_GEMINI_2_0_FLASH,
        # generate_content_config=model_config.create_generate_content_config(model_config.TRADING_AGENT_CONFIG),
        model=model_config.MODEL,
        planner=model_config.create_planner(model_config.DEFAULT_AGENT_CONFIG),
        tools=[launch_spot_transaction_card],
        before_agent_callback=trading_session_callback,
    )

