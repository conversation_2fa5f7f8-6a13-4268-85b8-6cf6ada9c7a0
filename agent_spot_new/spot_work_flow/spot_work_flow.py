from typing import Dict, Any, List
from util.trade_tools.llm import call_llm, cal_llm_gemini_2_5_flash, parse_str_to_dict
from util.trade_tools.check_user_asset import query_user_asset
from agent_spot.spot_work_flow.transaction_check import check_transaction_usdt_amount, filter_transaction_data

def parse_user_command(user_command:str, pre_context:List[Dict[str, str]]) -> Dict[str, Any]:
    """
    Parse the user command and the pre context, and then generate a response.
    user_command: buy some BTC
    pre_context: [{"role": "user", "content": "Hi"}, {"role": "assistant", "content": "Hello, how can I help you?"}]
    return: {"base_coin": "BTC", "quote_coin": "USDT", "amount": 0.001, "side": "buy", "type": "market"}}
    """

    prompt = f"""
    You are a Binance Spot Transaction Parser.
    You are given a user command and a pre context.
    You need to analyze the user command and the pre context, and then generate a transaction data based on the user command and the pre context.
    You need to return the transaction data in the following example format:
    {{"base_coin": "BTC", "quote_coin": "USDT", "amount": 0.001, "amount_coin":"BTC", "transaction_percent":0.1, "side": "buy", "type": "market","limit_price":10000}}
    The base_coin and quote_coin are the coins that the user wants to trade.
    The amount is the amount of the base_coin that the user wants to trade.
    The side is the side of the transaction, it can be "buy" or "sell", default is "buy".
    The type is the type of the transaction, it can be "market" or "limit", default is "market".
    The transaction_percent is the percentage of the transaction, it can be 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0.
    If user said "buy/sell all"，"全仓","all in","all out",", the transaction_percent is 1.0.
    If user said "buy/sell half","重仓","半仓", the transaction_percent is 0.5.
    If user said "buy/sell some","alittle","轻仓","买一点","买一些","试一试", the transaction_percent is 0.1.
    
    If some information is not provided, fill it with "null".
    {{"base_coin": "BTC", "quote_coin": "null", "amount": "null", "amount_coin":"BTC", "transaction_percent":"null", "side": "buy", "type": "market","limit_price":"null"}}
    User command: {user_command}
    Pre context: {pre_context}
    """
    #success, response = cal_llm_gemini_2_5_flash(prompt)
    success, response = call_llm(prompt)
    if success:
        return True,parse_str_to_dict(response)
    else:
        print('llm_error_response: ', response)
        return False, {}
    
def set_default_value(transaction_data:Dict[str, Any]) -> Dict[str, Any]:
    """
    Set the default value for the transaction data.
    """
    for key in ["base_coin", "quote_coin", "amount", "amount_coin", "transaction_percent", "side", "type", "limit_price"]:
        if key not in transaction_data:
            transaction_data[key] = None

    for key in ["base_coin", "quote_coin", "amount", "amount_coin", "transaction_percent", "side", "type", "limit_price"]:
        if str(transaction_data[key]).lower() == "null":
            transaction_data[key] = None

    if transaction_data['side'] is None:
        transaction_data['side'] = "buy"
    if transaction_data['type'] is None:
        transaction_data['type'] = "market"
    if transaction_data['quote_coin'] is None:
        transaction_data['quote_coin'] = 'USDT'

    transaction_data['explain_to_user'] = ""
    return transaction_data
    
def convert_transaction_percent_to_amount(transaction_data:Dict[str, Any], asset_data:Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert the transaction percent to the amount.
    """
   
    # get user usdt asset amount
    # print('-'*100)
    # print('asset_data: ', asset_data)
    free_asset_amount = 0
    if "assetList" in asset_data:
        for asset_dict in asset_data["assetList"]:
            if transaction_data['side'].lower() == 'buy':
                if asset_dict["asset"] == transaction_data['quote_coin']:
                    print('buy_free_asset_amount from transaction_data_quote_coin: ', asset_dict)
                    free_asset_amount = asset_dict["quoteAmount"]
                    break
            else:
                if asset_dict["asset"] == transaction_data['base_coin']:
                    print(' sell_free_asset_amount from transaction_data_base_coin: ', asset_dict)
                    free_asset_amount = asset_dict["quoteAmount"]
                    break
        while free_asset_amount == 0:
            for stable_coin in ["USDT", "USDC", "USDC.e"]:
                for asset_dict in asset_data["assetList"]:
                    if asset_dict["asset"] == stable_coin:
                        print('free_asset_amount from stable_coin: ', asset_dict)
                        free_asset_amount = asset_dict["quoteAmount"]
                        break
                if free_asset_amount > 0:
                    break

    transaction_data['free_asset_amount'] = free_asset_amount

    if transaction_data['amount'] is not None:
        if transaction_data['side'].lower() == 'buy':
            transaction_data['transaction_amount_usdt_from_percent'] = None
            if transaction_data['amount_coin'] is None:
                transaction_data['amount_coin'] = transaction_data['base_coin']
            transaction_data['explain_to_user'] = f"You want to trade {transaction_data['amount']} {transaction_data['amount_coin']}."
            return transaction_data
        else:
            transaction_data['transaction_amount_usdt_from_percent'] = None
            if transaction_data['amount_coin'] is None:
                transaction_data['amount_coin'] = transaction_data['base_coin']
            transaction_data['explain_to_user'] = f"You want to sell {transaction_data['amount']} {transaction_data['amount_coin']}."
            return transaction_data
    else:
        
        if free_asset_amount == 0:
            if transaction_data['side'].lower() == 'buy':
                transaction_data['transaction_amount_usdt_from_percent'] = None
                explain_str = "You don't have USDT to trade. Please get some USDT first."
                transaction_data['explain_to_user'] = explain_str
            else:
                transaction_data['transaction_amount_usdt_from_percent'] = None
                explain_str = f"Sorry, you don't have {transaction_data['base_coin']} to sell."
                transaction_data['explain_to_user'] = explain_str
            return transaction_data
        else:
            # Ensure transaction_percent is not None before using it
            if transaction_data['transaction_percent'] is None:
                transaction_data['transaction_percent'] = 0.1
                transaction_data['explain_to_user'] = "Since you didn't provide the transaction percentage, I'll use 10% of your assets."
            else:
                transaction_data['explain_to_user'] = ""
                
            transaction_data['transaction_amount_usdt_from_percent'] = free_asset_amount * transaction_data['transaction_percent']
            transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + f" According to your assets, I'd like to use {transaction_data['transaction_percent']*100}% of your assets to trade, which is {transaction_data['transaction_amount_usdt_from_percent']} usdt to trade."
            return transaction_data

       
def generate_agent_response(transaction_data:Dict[str, Any], user_command:str, pre_context:List[Dict[str, str]], asset_data:Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate the agent response.
    """
    prompt = f"""
    You are a Binance Spot Transaction Assistant.
    You are given a user command and a pre context and a spot transaction solution.
    You need to: 
    1. Aanalyze the user command and the pre context
    2. Generate a response to explain the spot transaction solution to the user. 
    
    Important: extract the message from the transaction_data['explain_to_user'] and explain it to the user.

    transaction_data['transaction_value_usdt'] is the transaction value in usdt.
    
    **Important**: transaction_data['base_price'] is the price of the base coin. Do not use the price from LLM inner data. Price has changed after the LLM had been trained. Always use the price from transaction_data.
    transaction_data['quote_price'] is the price of the quote coin.

    transaction_data['base_amount'] is the amount of the base coin.
    transaction_data['quote_amount'] is the amount of the quote coin.
    transaction_data['free_asset_amount'] is the amount of the available asset in usdt that can be used for this transaction.
    
    User command: {user_command}
    Pre context: {pre_context}
    Preview Spot transaction solution: {transaction_data}
    User asset: {asset_data}
    One example of the response:
    User: Buy some Bitcoin

Got your buy order! I’ll arrange the BTC purchase right away—so fast even the blockchain can’t keep up with me 🪙🚀

Based on your account status (Available funds: xxxx.x USDT), I’ve set up a market order to buy BTC by default, spending approximately xxx USDT. This way, you can ride the crypto wave while keeping things flexible—steady and strategic.

If you'd like to buy more or less, just let me know the specific amount or BTC quantity. I’m here to adjust it anytime!

BTC Buy Order Confirmation

Asset: BTC (Bitcoin)

Price: Market price

Order Type: Market Order

Amount: xxx USDT

Click this card to place your order in one tap—catch the bull run and start embracing digital gold now!


Want to modify the order? Just say the word! I’m always online, ready to surf the blockchain waves with you 😎📈

"""

    success, response = call_llm(prompt)
    if success:
        return response
    else:
        print('llm_error_response: ', response)
        return "Sorry, I'm having trouble processing your request. Please try again later."

def run(user_id:int, user_command:str, pre_context:List[Dict[str, str]]) -> Dict[str, Any]:
    """
    Run the spot work flow.
    user_command: buy some BTC
    pre_context: [{"role": "user", "content": "Hi"}, {"role": "assistant", "content": "Hello, how can I help you?"}]
    return: {"status": "success", "agent_response": "xxxx", 
    "transaction_data": {"base_coin": "BTC", "quote_coin": "USDT", "amount": 0.001, "amount_coin":"BTC", "side": "buy", "type": "market"}}
    """

    success, transaction_data = parse_user_command(user_command, pre_context)
    if not success:
        return {"status": "success", "agent_response": "Sorry, I'm having trouble processing your request. Please try again later.", "transaction_data": {}, "transaction_data_filtered": {}}
    
    print('transaction_data after parse_user_command: ', transaction_data)
    transaction_data = set_default_value(transaction_data)
    print('transaction_data after set_default_value: ', transaction_data)

    
    if transaction_data['base_coin'] is None:
        transaction_data['base_coin'] = "BNB"
        transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + " Since you didn't provide the base coin, I'd like to use BNB as the default coin."
    if transaction_data['amount'] is None and transaction_data['transaction_percent'] is None:
        transaction_data['transaction_percent'] = 0.1
        transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + " Since you didn't provide the amount, I'd like to use 10% USDT as the transaction amount."
    
    # check transaction usdt amount

    asset_data = query_user_asset(user_id)
    print('asset_data after query_user_asset: ', str(asset_data)[0:100])
    print('asset_data after query_user_asset_2: ', str(asset_data)[-100:])
    if "assetList" in asset_data:
        totalQuoteAmount = asset_data.get("totalQuoteAmount")
        print('after query_user_asset totalQuoteAmount: ', totalQuoteAmount)
        if totalQuoteAmount is None or totalQuoteAmount <= 0:
            transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + " After checking your asssets, your current assets is empty. Please deposit some assets first."

    
    transaction_data = convert_transaction_percent_to_amount(transaction_data, asset_data)
    print('transaction_data after convert_transaction_percent_to_amount: ', transaction_data)
    
    print('transaction_data after explain_to_user: ', transaction_data)
    transaction_data = check_transaction_usdt_amount(transaction_data, asset_data)
    print('transaction_data after check_transaction_usdt_amount: ', transaction_data)
    agent_response = generate_agent_response(transaction_data, user_command, pre_context, asset_data)
    print('agent_response after generate_agent_response: ', agent_response)

    transaction_data_filtered = filter_transaction_data(transaction_data)
    print('transaction_data_filtered after filter_transaction_data: ', transaction_data_filtered)

    return {"status": "success", "agent_response": agent_response, "transaction_data": transaction_data, "transaction_data_filtered": transaction_data_filtered}
   
