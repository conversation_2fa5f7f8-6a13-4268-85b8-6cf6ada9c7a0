
from util.logger.logger_utils import setup_logger, log_exception_as_single_line
import httpx
import json
import os
from agent_convert.price_tool import get_price
from agent_convert.chat_convert_tool import format_number_for_display

logger = setup_logger(__name__)



def filter_transaction_data(transaction_data:dict[str, any]) -> dict[str, any]:
    """
    Filter the transaction data.
    """
    def is_first_priority_num(num:float) -> bool:
        """
        Check if the number is the first priority number.
        """
        if num is None:
            return False
        if num in [0.1,0.01,0.001,0.0001,0.00001]:
            return True
        if int(num) != num:
            return False
        
        return True

    def is_low_priority_num(num:float) -> bool:
        """
        Check if the number is the low priority number.
        """
        if num is None:
            return True
        if num in [0.1,0.01,0.001,0.0001,0.00001]:
            return False
        if int(num) != num:
            return True
        return False
    
    output_transaction_data = {}
    for key in ["base_coin", "quote_coin","type","side","limit_price"]:
        output_transaction_data[key] = transaction_data[key]
    for key in ['base_amount', 'quote_amount']:
        if key not in transaction_data:
            transaction_data[key] = None
        
    if is_first_priority_num(transaction_data['base_amount']) and is_low_priority_num(transaction_data['quote_amount']):
        output_transaction_data['base_amount'] = transaction_data['base_amount']
    elif is_first_priority_num(transaction_data['quote_amount']) and is_low_priority_num(transaction_data['base_amount']):
        output_transaction_data['quote_amount'] = transaction_data['quote_amount']
    elif is_first_priority_num(transaction_data['base_amount']):
        output_transaction_data['base_amount'] = transaction_data['base_amount']
    elif is_first_priority_num(transaction_data['quote_amount']):
        output_transaction_data['quote_amount'] = transaction_data['quote_amount']
    elif transaction_data['base_amount'] is not None:
        output_transaction_data['base_amount'] = transaction_data['base_amount']
    elif transaction_data['quote_amount'] is not None:
        output_transaction_data['quote_amount'] = transaction_data['quote_amount']
    else:
        output_transaction_data['base_amount'] = transaction_data['base_amount']
    
    return output_transaction_data

def check_transaction_usdt_amount(transaction_data: dict, asset_data: dict) -> dict:
        """ input transaction_data, like {
            "explain_to_user": "You want to trade 100 USDT.",
            "transaction_amount_usdt_from_percent": 200,
            "base_coin": "BTC",
            "quote_coin": "USDT", 
            "amount": 0.001,
            "amount_coin": "BTC"
        }

        return {"explain_to_user": "BTC现在市场价格为10500 USDT，BNB现在市场价格为768 USDT，0.01 BTC 约等于 0.15 BNB",
                "transaction_value_usdt": 0.01,
                "base_price": 10500,
                "quote_price": 768,
                "base_amount": 0.01,
                "quote_amount": 0.15,
                "base_coin": "BTC",
                "quote_coin": "USDT"
        }

        """
        base_coin = transaction_data.get("base_coin", "BTC")
        quote_coin = transaction_data.get("quote_coin", "USDT")
        amount = transaction_data.get("amount", 0.001)
        amount_coin = transaction_data.get("amount_coin", "BTC")

        return_str = ""
        
        # Type checking and conversion for amount
        if isinstance(amount, (list, tuple)):
            logger.warning(f"Amount is a sequence: {amount}, taking first element")
            amount = amount[0] if amount else 1
        elif not isinstance(amount, (int, float)):
            logger.warning(f"Amount is not a number: {amount} (type: {type(amount)}), using default")
            amount = 1
        
        # Convert to float to ensure it's a number
        amount = float(amount)
        
        # Get current prices for both coins
        
        if base_coin in ["USDT", "USDC", "USDC.e"]:
            base_price = 1.0
        else:
            base_price = get_price(base_coin)
            # Type checking for base_price
            if isinstance(base_price, (list, tuple)):
                logger.warning(f"base_price is a sequence: {base_price}, taking first element")
                base_price = base_price[0] if base_price else -1
            elif not isinstance(base_price, (int, float)):
                logger.warning(f"base_price is not a number: {base_price} (type: {type(base_price)}), using -1")
                base_price = -1
            base_price = float(base_price)
        
        if quote_coin in ["USDT", "USDC", "USDC.e"]:
            quote_price = 1.0
        else:
            quote_price = get_price(quote_coin)
            # Type checking for quote_price
            if isinstance(quote_price, (list, tuple)):
                logger.warning(f"quote_price is a sequence: {quote_price}, taking first element")
                quote_price = quote_price[0] if quote_price else -1
            elif not isinstance(quote_price, (int, float)):
                logger.warning(f"quote_price is not a number: {quote_price} (type: {type(quote_price)}), using -1")
                quote_price = -1
            quote_price = float(quote_price)
        
        if base_price == -2 or quote_price == -2: #-2 # Symbol '{coin_symbol}' not found in Binance products
            if base_price == -2:
                transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + f" Coin {base_coin} is not supported in Binance Spot. Please try another coin."
            if quote_price == -2:
                transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + f" Coin {quote_coin} is not supported in Binance Spot. Please try another coin."
            return transaction_data
        elif transaction_data['type'].lower() == "market":
            if base_price < 0 or quote_price < 0 :
                logger.warning(f" Unable to get price for {base_coin} or {quote_coin}")
                transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + f"Can not find price right now. Do not show coin price in the response to user."
                return transaction_data
            
        if transaction_data['type'].lower() == "limit" and transaction_data['limit_price'] is not None:
            if base_coin in ["USDT", "USDC", "USDC.e"]:
                base_price = 1.0
            else:
                if abs(transaction_data['limit_price'] - base_price)/base_price > 0.2:
                    transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + f" Warning: The limit price is too different from the current market price. The transaction may not be executed."
                base_price = transaction_data['limit_price']
            if quote_coin in ["USDT", "USDC", "USDC.e"]:
                quote_price = 1.0
            else:
                if abs(transaction_data['limit_price'] - quote_price)/quote_price > 0.2:
                    transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + f" Warning: The limit price is too different from the current market price. The transaction may not be executed."
                quote_price = transaction_data['limit_price']
        
        # Calculate the conversion
        if transaction_data['transaction_amount_usdt_from_percent'] is not None:
            tramsaction_value_usdt = transaction_data['transaction_amount_usdt_from_percent']
            base_amount = tramsaction_value_usdt / base_price
            quote_amount = tramsaction_value_usdt / quote_price
        else:
            if amount_coin == base_coin:
                # Converting from coin amount to to_coin
                base_amount = amount
                tramsaction_value_usdt = amount * base_price
                quote_amount = tramsaction_value_usdt / quote_price
    
            
            elif amount_coin == quote_coin:
                quote_amount = amount
                tramsaction_value_usdt = amount * quote_price
                base_amount = tramsaction_value_usdt / base_price
                            
            else:
                logger.warning(f" explain_spot_transaction: Unsupported amount_coin: {amount_coin}, transaction_data: {transaction_data}")
                transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + f"Can not find price right now. Do not show coin price in the response to user. Unsupported amount_coin: {amount_coin} "
                return transaction_data
    
        
        if base_coin not in ["USDT", "USDC", "USDC.e"]:
            price_str = format_number_for_display(base_price)
            if transaction_data['type'].lower() == "limit":
                return_str = return_str + f"In your limit order, the price of {base_coin} is about {price_str} USDT，"
            else:
                return_str = return_str + f"Current market price of {base_coin} is about {price_str} USDT，"
        if quote_coin not in ["USDT", "USDC", "USDC.e"]:
            price_str = format_number_for_display(quote_price)
            return_str = return_str + f"Current market price of {base_coin} is about {price_str} USDT，"
        base_amount_str = format_number_for_display(base_amount)
        quote_amount_str = format_number_for_display(quote_amount)

        if transaction_data['type'].lower() == "limit":
            return_str = return_str + f"In your limit order price, {base_amount_str} {base_coin} is about equal to {quote_amount_str} {quote_coin}"
        else:
            return_str = return_str + f"{base_amount_str} {base_coin} is about equal to {quote_amount_str} {quote_coin}"
            

        if asset_data is not None and "assetList" in asset_data:
            totalQuoteAmount = asset_data.get("totalQuoteAmount")
            if totalQuoteAmount is None or totalQuoteAmount <= 0:
                return_str = return_str + " Your total asses is 0, please deposit some assets first."
            else:
                if totalQuoteAmount < tramsaction_value_usdt:
                    return_str = return_str + f" The amount you want to trade is larger than your total assets."
            for asset_dict in asset_data["assetList"]:
                if transaction_data['side'].lower() == 'buy' and asset_dict["asset"] == quote_coin:
                    if asset_dict["amount"] < quote_amount:
                        return_str = return_str + f" As you have {asset_dict['amount']} {quote_coin} in your account, let's reduce the buy amount to {asset_dict['amount']} {quote_coin}."
                        quote_amount = asset_dict["amount"]
                        tramsaction_value_usdt = quote_amount * quote_price
                        base_amount = tramsaction_value_usdt / base_price
                        base_amount_str = format_number_for_display(base_amount)
                        quote_amount_str = format_number_for_display(quote_amount)
                        return_str = return_str + f" {base_amount_str} {base_coin} is about equal to {quote_amount_str} {quote_coin}"
                        break
                elif transaction_data['side'].lower() == 'sell' and asset_dict["asset"] == base_coin:
                    if asset_dict["amount"] < base_amount:
                        return_str = return_str + f" As you just have {asset_dict['amount']} {base_coin} in your account, let's reduce the sell amount to {asset_dict['amount']} {base_coin}."
                        base_amount = asset_dict["amount"]
                        tramsaction_value_usdt = base_amount * base_price
                        quote_amount = tramsaction_value_usdt / quote_price
                        base_amount_str = format_number_for_display(base_amount)
                        quote_amount_str = format_number_for_display(quote_amount)
                        return_str = return_str + f" {base_amount_str} {base_coin} is about equal to {quote_amount_str} {quote_coin}"
                        break

        transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + return_str
        transaction_data['transaction_value_usdt'] = tramsaction_value_usdt
        transaction_data['base_price'] = base_price
        transaction_data['quote_price'] = quote_price
        transaction_data['base_amount'] = base_amount
        transaction_data['quote_amount'] = quote_amount
        transaction_data['base_coin'] = base_coin
        transaction_data['quote_coin'] = quote_coin
        return transaction_data
                
        