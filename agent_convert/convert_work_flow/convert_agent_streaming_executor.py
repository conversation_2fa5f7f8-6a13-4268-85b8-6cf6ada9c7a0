import asyncio
import os

from a2a.server.agent_execution import <PERSON><PERSON><PERSON><PERSON><PERSON>, RequestContext
from a2a.server.events import EventQueue
from a2a.server.tasks import TaskUpdater
from a2a.types import TextPart, DataPart, Part, TaskState, UnsupportedOperationError
from a2a.utils import new_task, new_agent_text_message
from a2a.utils.errors import ServerError

from agent_convert.convert_work_flow.convert_work_flow import run, generate_agent_response_async, generate_agent_response
from util.logger.logger_utils import setup_logger
from util.logger.log_context import set_trace_id, set_session_id, set_user_id
from agent_convert.convert_work_flow.convert_work_flow import filter_transaction_data
from agent_convert.convert_work_flow.convert_work_flow import report_to_opik, get_isoformat_time

logger = setup_logger(__name__)


class ConvertAgentStreamingExecutor(AgentExecutor):
    async def execute(self, context: RequestContext, event_queue: EventQueue) -> None:
        # 1) If there's no existing Task, start one using the incoming Message
        start_time = get_isoformat_time()
        task = context.current_task
        lang = context.message.metadata.get("lang", "")
        bnc_lang = context.message.metadata.get("requestHeaders", {}).get("bnc_lang", "")
        session_id = context.message.metadata.get("sessionId")

        if not task:
            assert context.message is not None, "No incoming message!"
            task = new_task(context.message)
            await event_queue.enqueue_event(task)
        message = context.message.dict()

        # 2) Binder for sending updates
        # updater = TaskUpdater(event_queue, task.id, task.contextId)
        updater = TaskUpdater(event_queue, task.id, task.context_id)

        # 3) Extract user input from A2A message format
        user_query = ""
        user_id = "default_user"

        # print(f'context.message:{context.message}')
        # print(f'context.message.parts:{context.message.parts}')
        # print(f'context.message.parts[0]:{context.message.parts[0]}')
        # print(f'context.message.parts[0].root:{context.message.parts[0].root}')
        # print(f'context.message.parts[0].root.text:{context.message.parts[0].root.text}')
        #
        # print(f'context.message.metadata:{context.message.metadata}')
        # print(f'type(context.message.metadata):{type(context.message.metadata)}')
        # print(f'context.message.metadata["userId"]:{context.message.metadata['userId']}')
        #
        # print('user_id 1', user_id)
        # print('user_query 1', user_query)

        if context.message and context.message.parts:
            # Extract text from message parts
            for part in context.message.parts:
                print('part:', part)
                if hasattr(part, 'root') and part.root:
                    if hasattr(part.root, 'text') and part.root.text:
                        user_query = part.root.text
                        input_part_json = part.json()
                        break

        # Extract user_id from metadata if available
        try:
            user_id = context.message.metadata['userId']
        except Exception as e:
            pass

        # if context.message and hasattr(context.message, 'metadata'):
        #     metadata = context.message.metadata
        #     # if hasattr(metadata, 'userId') and metadata.userId:
        #     if 'metadata' in metadata and metadata['userId']:
        #         user_id = metadata['userId']


        # print('user_id 2', user_id)
        # print('user_query 2', user_query)


        if not user_query:
            user_query = "Convert 100 USDT to BTC"  # Default fallback
        
        logger.info(f"Processing convert request: user_query={user_query}, user_id={user_id}, taskId={task.id}, "
                    "task.context_id={task.context_id}, lang={lang}, bnc_lang={bnc_lang}, session_id={session_id}")

        # # Send intermediate updates to the task updater
        # accumulated_text = ''
        # chunk_text = 'Received your convert request, processing...'
        # accumulated_text += chunk_text
        # await updater.update_status(
        #     TaskState.working,
        #     new_agent_text_message(
        #         chunk_text,
        #         task.context_id,
        #         task.id
        #     )
        # )
        
        # 4) Run the convert workflow
        # result = run(user_id, user_query, [])

        asset_data_mock = None
        # asset_data_mock =  {'assetList': [{'asset': 'BTC', 'amount': 10000.0, 'quoteAmount': 1189922800.0}, {'asset': 'USDT', 'amount': 0.005, 'quoteAmount': 0.005}], 'quoteAsset': 'USDT', 'totalQuoteAmount': 1189922800.005}

        pre_context = []
        result = run(user_id, user_query, pre_context, only_return_transaction_data=True, attach_coin_price=False,
                     asset_data_mock=asset_data_mock)
        # if result['status'] != 'success':
        #     error_message = f"Error processing Convert request: {result.get('error', 'Unknown error')}"
        #     await updater.update_status(
        #         TaskState.failed,
        #         new_agent_text_message(error_message, task.context_id, task.id),
        #         final=True
        #     )
        #     return

        accumulated_text = ''
        transaction_data = result.get('transaction_data', {})
        agent_response = result.get('agent_response', None)
        asset_data = result.get('asset_data', {})
        # agent_response = generate_agent_response(transaction_data, user_command, pre_context, asset_data)
        print(f'[transaction_data] after run: ', transaction_data)
        print(f'[agent_response] after run: ', agent_response)

        try:
            if not agent_response:
                chunk_idx = 0
                async for chunk in generate_agent_response_async(transaction_data, user_query, pre_context, asset_data, reply_lang=lang):
                    # Send intermediate updates to the task updater
                    # print(f'chunk_{chunk_idx}: {chunk} \n\n')
                    chunk_idx += 1
                    chunk_text = chunk.text
                    accumulated_text += chunk_text
                    await updater.update_status(
                        TaskState.working,
                        new_agent_text_message(
                            chunk_text,
                            task.context_id,
                            task.id
                        )
                    )
                agent_response = accumulated_text
                print(f'[agent_response] after generate_agent_response: ', agent_response)
        except Exception as e:
            # logger.error(f"Error during agent response generation: {e}")
            # agent_response = "An error occurred while processing your request. Please try again later."
            import traceback
            logger.error(f"Error in taskId {task.id} ConvertAgentStreamingExecutor.execute: {e}")
            logger.error(f"Full taskId {task.id} traceback: {traceback.format_exc()}")

            error_message = f"We couldn’t process your Convert request. Please try again later."
            await updater.update_status(
                TaskState.failed,
                new_agent_text_message(error_message, task.context_id, task.id),
                final=True
            )

        if transaction_data and transaction_data.get('fail_status') is not True:
            transaction_data_filtered = filter_transaction_data(transaction_data)
        else:
            transaction_data_filtered = None
        print(f'[transaction_data_filtered] after filter_transaction_data: ', transaction_data_filtered)


        # agent_response = result['agent_response']
        # transaction_data = result['transaction_data']
        # transaction_data_filtered = result['transaction_data_filtered']
        
        logger.info(f"Got conversion result: {result}")

        # 5) Build A2A response parts
        response_parts = []

        # text_part = Part(root=TextPart(text='Conversion Transaction Completed.'))
        # response_parts.append(text_part)

        # Add TEXT part with agent response
        if agent_response:
            text_part = Part(root=TextPart(text=agent_response))
            response_parts.append(text_part)
        
        # Add DATA part with transaction data if available
        data_artifact = None
        if transaction_data_filtered:
            data_artifact = {
                "data": {
                    "name": "convertTradeCard",
                    "props": {
                        # "businessType": "Convert",
                        "from_coin": transaction_data_filtered.get("from_coin", ""),
                        "to_coin": transaction_data_filtered.get("to_coin", ""),
                        "from_coin_amount": transaction_data_filtered.get("from_coin_amount", None),
                        "to_coin_amount": transaction_data_filtered.get("to_coin_amount", None)
                    }
                },
                "type": "jarvis.comp"
            }
            data_part = Part(root=DataPart(data=data_artifact))
            response_parts.append(data_part)

        # 6) Send the final response directly
        if response_parts:
            await updater.add_artifact(
                response_parts,
                name="conversion_result",
            )

        # 7) report to opik
        input_data = input_part_json
        output_data = {'content':{'parts':[ {'text':agent_response}], 'convertTradeCard':data_artifact} }
        try:
            report_to_opik(context, input_data=input_data, output_data=output_data, error_data=None, start_time=start_time, logger=logger)
        except Exception as e:
            logger.error(f"Error reporting to Opik: {e}")



        await updater.complete()

    async def cancel(self, context: RequestContext, event_queue: EventQueue) -> None:
        # No cancellation support
        raise ServerError(error=UnsupportedOperationError()) 