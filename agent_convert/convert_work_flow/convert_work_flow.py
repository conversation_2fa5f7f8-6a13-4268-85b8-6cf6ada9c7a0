import time
import json
import os
import requests
from datetime import datetime
from typing import Dict, Any, List, Tuple
from util.trade_tools.llm import call_llm, cal_llm_gemini_2_5_flash, parse_str_to_dict, call_llm_async
from util.trade_tools.check_user_asset import query_user_asset
from agent_convert.convert_work_flow.transaction_check import check_transaction_usdt_amount
from agent_convert.convert import recommend_convert_token_endpoint_using_api
from agent_convert.convert_pair import get_convert_coin_list
from agent_convert.convert_work_flow.transaction_check import estimate_convert_amount, btc_coin_list, eth_coin_list, bnb_coin_list
from agent_convert.price_tool import get_price

stablecoin_set = {"USDT", "USDC", "BUSD", "USDC.e"}

def parse_user_command(user_command:str, pre_context:List[Dict[str, str]]) -> Dict[str, Any]:
    """
    Parse the user command and the pre context, and then generate a response.
    user_command: buy some BTC
    pre_context: [{"role": "user", "content": "Hi"}, {"role": "assistant", "content": "Hello, how can I help you?"}]
    return: {"base_coin": "BTC", "quote_coin": "USDT", "amount": 0.001, "side": "buy", "type": "market"}}
    """

    # prompt = f"""
    # You are a Binance Convert Transaction Parser.
    # You are given a user command and a pre context.
    # You need to generate a transaction data based on the user command and the pre context.
    # You need to return the transaction data in the following example format:
    # {{"from_coin": "BTC", "to_coin": "USDT", "amount": 0.001, "amount_coin":"BTC"}}
    # The from_coin and to_coin are the coins that the user wants to trade.
    # The amount_coin is the unit of the amount the user wants to trade, it is the from_coin or to_coin.
    #
    # If some information is not provided, fill it with "null".
    # {{"from_coin": "BTC", "to_coin": "null", "amount": "null", "amount_coin":"null"}}
    # User command: {user_command}
    # Pre context: {pre_context}
    # """

    prompt = f"""
    You are a Binance Convert Transaction Parser.
    You are given a user command and a pre context.
    You need to generate a transaction data based on the user command and the pre context.
    You need to return the transaction data in the following example format:
    {{"from_coin": "BTC", "to_coin": "USDT", "amount": 0.001, "amount_coin":"BTC"}}
    The from_coin and to_coin are the coins that the user wants to trade.
    The amount_coin is the unit of the amount the user wants to trade, it is the from_coin or to_coin.
    
    If some information is not provided, fill it with "null".
    {{"from_coin": "BTC", "to_coin": "null", "amount": "null", "amount_coin":"null"}}
    
    1. If the user specifies a trade price, disregard it, as Convert Transactions are executed at the market price.
    
    User command: {user_command}
    Pre context: {pre_context}
    """

    success, response = cal_llm_gemini_2_5_flash(prompt)
    if success:
        return True, parse_str_to_dict(response)
    else:
        print('llm_error_response: ', response)
        return False, {}

def set_default_value(transaction_data:Dict[str, Any]) -> Dict[str, Any]:
    """
    Set the default value for the transaction data.
    """
    for key in ["from_coin", "to_coin", "amount", "amount_coin"]:
        if key not in transaction_data:
            transaction_data[key] = None

    for key in ["from_coin", "to_coin", "amount", "amount_coin"]:
        if str(transaction_data[key]).lower() == "null":
            transaction_data[key] = None

    transaction_data['explain_to_user'] = ""
    return transaction_data

def get_prompt_v2(transaction_data, user_command, pre_context, asset_data):
    prompt = f"""
    You are a Binance Convert Transaction Assistant.
    You are given a user command and a pre context and a convert transaction solution.
    You need to analyze the user command and the pre context, and then generate a response to explain the convert transaction solution to the user.

    Important: extract the message from the transaction_data['explain_to_user'] and explain it to the user.

    transaction_data['transaction_value_usdt'] is the transaction value in usdt.

    **Important**: transaction_data['from_coin_price'] is the price of the from coin. Do not use the price from LLM inner data. Price has changed after the LLM had been trained. Always use the price from transaction_data.
    transaction_data['to_coin_price'] is the price of the to coin.

    transaction_data['from_coin_amount'] is the amount of the from coin.
    transaction_data['to_coin_amount'] is the amount of the to coin.

    User command: {user_command}
    Pre context: {pre_context}
    Convert transaction solution: {transaction_data}
    User asset: {asset_data}
    One example of the response:
Received your Convert request.

Trading Suggestion:
Based on your account, I’ve set up a Convert from xx BNB to USDT.

Convert Order Confirmation

- From: BNB (Binance Coin)
- To: USDT
- Amount: xx BNB
- If got coin market price, extract and explain the content of transaction['explain_to_user'] here.

Click the card to convert instantly.
    """
    return prompt

def get_prompt_v3(transaction_data, user_command, pre_context, asset_data, reply_lang=''):
    prompt = f"""
    You are a Binance Convert Transaction Assistant.
    You are given a user command and a pre context and a convert transaction solution.
    You need to analyze the user command and the pre context, and then generate a response to explain the convert transaction solution to the user.

    Data Explanation:
    * transaction_data['transaction_value_usdt'] is the transaction value in usdt.
    * transaction_data['from_coin_price'] is the price of the from coin. transaction_data['to_coin_price'] is the price of the to coin.
    * transaction_data['from_coin_amount'] is the amount of the from coin.
    * transaction_data['to_coin_amount'] is the amount of the to coin.
    
    Important: 
    * Extract the message from the transaction_data['explain_to_user'] and explain it to the user.
    * Do not use the price from LLM inner data. Always use the price from transaction_data.
    * If reply language is specified, reply in that language. Otherwise, reply with the language of the user command.


    User command: {user_command}
    Pre context: {pre_context}
    Convert transaction solution: {transaction_data}
    User asset: {asset_data}
    Reply language: {reply_lang}
    
    One example of the response:
Received your Convert request.

Trading Suggestion:
Based on your account, I’ve set up a Convert from xx BNB to USDT.

Convert Order Confirmation

- From: BNB (Binance Coin)
- To: USDT
- Amount: xx BNB
- If got coin market price, extract and explain the content of transaction['explain_to_user'] here.

Click the card to convert instantly.
    """
    return prompt

def generate_agent_response(transaction_data:Dict[str, Any], user_command:str, pre_context:List[Dict[str, str]], asset_data:Dict[str, Any], reply_lang=None) -> Dict[str, Any]:
    """
    Generate the agent response.
    """
#     prompt = f"""
#     You are a Binance Convert Transaction Assistant.
#     You are given a user command and a pre context and a convert transaction solution.
#     You need to analyze the user command and the pre context, and then generate a response to explain the convert transaction solution to the user.
#
#     Important: extract the message from the transaction_data['explain_to_user'] and explain it to the user.
#
#     transaction_data['transaction_value_usdt'] is the transaction value in usdt.
#
#     **Important**: transaction_data['from_coin_price'] is the price of the from coin. Do not use the price from LLM inner data. Price has changed after the LLM had been trained. Always use the price from transaction_data.
#     transaction_data['to_coin_price'] is the price of the to coin.
#
#     transaction_data['from_coin_amount'] is the amount of the from coin.
#     transaction_data['to_coin_amount'] is the amount of the to coin.
#
#     User command: {user_command}
#     Pre context: {pre_context}
#     Convert transaction solution: {transaction_data}
#     User asset: {asset_data}
#     One example of the response:
# Received your Convert request! Arranging BNB to USDT conversion now — I’m faster than the blockchain itself～
#
#
# Trading Suggestion:
# Based on your account, I’ve set up a Convert from xx BNB to USDT.
#
# Convert Order Confirmation
#
# - From: BNB (Binance Coin)
# - To: USDT
# - Amount: xx BNB
# - If got coin market price, extract and explain the content of transaction['explain_to_user'] here.
#
# Click the card to convert instantly. Seize the moment and stay ahead of the game!
#
# Want to modify the order? Just say the word!  I’m always online surfing the chain with you～
#     """

    # prompt = get_prompt_v2(transaction_data, user_command, pre_context, asset_data)
    prompt = get_prompt_v3(transaction_data, user_command, pre_context, asset_data, reply_lang=reply_lang)
    success, response = call_llm(prompt)
    if success:
        return response
    else:
        print('llm_error_response: ', response)
        return "Sorry, I'm having trouble processing your request. Please try again later."


async def generate_agent_response_async(transaction_data: Dict[str, Any], user_command: str, pre_context: List[Dict[str, str]],
                            asset_data: Dict[str, Any], reply_lang=None):
    """
    Generate the agent response.
    """
#     prompt = f"""
#     You are a Binance Convert Transaction Assistant.
#     You are given a user command and a pre context and a convert transaction solution.
#     You need to analyze the user command and the pre context, and then generate a response to explain the convert transaction solution to the user.
#
#     Important: extract the message from the transaction_data['explain_to_user'] and explain it to the user.
#
#     transaction_data['transaction_value_usdt'] is the transaction value in usdt.
#
#     **Important**: transaction_data['from_coin_price'] is the price of the from coin. Do not use the price from LLM inner data. Price has changed after the LLM had been trained. Always use the price from transaction_data.
#     transaction_data['to_coin_price'] is the price of the to coin.
#
#     transaction_data['from_coin_amount'] is the amount of the from coin.
#     transaction_data['to_coin_amount'] is the amount of the to coin.
#
#     User command: {user_command}
#     Pre context: {pre_context}
#     Convert transaction solution: {transaction_data}
#     User asset: {asset_data}
#     One example of the response:
# Received your Convert request! Arranging BNB to USDT conversion now — I’m faster than the blockchain itself～
#
#
# Trading Suggestion:
# Based on your account, I’ve set up a Convert from xx BNB to USDT.
#
# Convert Order Confirmation
#
# - From: BNB (Binance Coin)
# - To: USDT
# - Amount: xx BNB
# - If got coin market price, extract and explain the content of transaction['explain_to_user'] here.
#
# Click the card to convert instantly. Seize the moment and stay ahead of the game!
#
# Want to modify the order? Just say the word!  I’m always online surfing the chain with you～
#     """
#     prompt = get_prompt_v2(transaction_data, user_command, pre_context, asset_data)
    prompt = get_prompt_v3(transaction_data, user_command, pre_context, asset_data, reply_lang=reply_lang)

    try:
        async for chunk in call_llm_async(prompt):
            yield chunk
    except Exception as e:
        print(f"Error generating agent response: {e}")
        yield "Sorry, I'm having trouble processing your request. Please try again later."


def get_high_value_coin(asset_data:Dict[str, Any], parsed_to_coin=None) -> Tuple[str, str]:
    """
    Get the high value coin from the asset data.
    """
    high_value_coin = "BTC"
    high_value_coin_amount = 0
    high_value_coin_amount_threshold = 1.0
    if "assetList" in asset_data:
        # 1. to_coin不为空, 优先使用 from非稳定币+to稳定币 / from稳定不+to非稳定币 组合
        if parsed_to_coin:
            for asset_dict in asset_data["assetList"]:
                coin, quote_amount = asset_dict["asset"], asset_dict["quoteAmount"]
                if quote_amount < high_value_coin_amount_threshold:
                    continue
                if coin in stablecoin_set and parsed_to_coin not in stablecoin_set:
                    return coin, quote_amount
                if coin not in stablecoin_set and parsed_to_coin in stablecoin_set:
                    return coin, quote_amount

        # 2. to_coin为空，遍历资产列表，找到持有的币种中价值最高的币种
        for asset_dict in asset_data["assetList"]:
            coin = asset_dict["asset"]
            # 1. coin is different to parsed_to_coin
            if parsed_to_coin and parsed_to_coin == coin:
                continue
            # 2. coin have valid price
            price = get_price(coin)
            if price <= 0:
                continue

            # if coin not in ["USDT", "USDC", "USDC.e","BUSD"]:
            #     continue
            if asset_dict["quoteAmount"] > high_value_coin_amount:
                high_value_coin = coin
                high_value_coin_amount = asset_dict["quoteAmount"]
                return high_value_coin, high_value_coin_amount
    return high_value_coin, high_value_coin_amount

def recommend_convert_coin_if_command_is_not_clear(user_id:int, transaction_data:Dict[str, Any], asset_data:Dict[str, Any]) -> Dict[str, Any]:
    """
    Recommend the convert coin if the command is not clear.
    """
    # 1. user have not specific from_coin
    if transaction_data['from_coin'] is None:
        transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + f" You didn't provide the source coin. I'll recommend a convert coin for you."
        print('from_coin is None, from_coin: ', transaction_data['from_coin'])

    # 2. from_coin invalid
    if transaction_data['from_coin'] is not None \
            and get_price(transaction_data['from_coin']) <= 0 \
            and transaction_data['from_coin'] not in stablecoin_set:
        transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + f" [{transaction_data['from_coin']}] is not available for conversion. I'll recommend a convert coin for you."
        print('from_coin price not available', 'from_coin: ', transaction_data['from_coin'])
        if transaction_data['amount_coin'] == transaction_data['from_coin']:
            transaction_data['amount'] = None
            transaction_data['amount_coin'] = None
        transaction_data['from_coin'] = None

    # 3. to_coin not in asset
    asset_coins = [info['asset'] for info in asset_data.get("assetList", []) if 'asset' in info and info.get('amount',0) > 0]
    if transaction_data['from_coin'] is not None \
            and transaction_data['from_coin'] not in asset_coins:
        transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + f" You don't have {transaction_data['from_coin']} in your account. I'll recommend a convert coin for you."
        print('from_coin not in asset_coins', 'from_coin: ', transaction_data['from_coin'], 'asset_coins: ', asset_coins)
        if transaction_data['amount_coin'] == transaction_data['from_coin']:
            transaction_data['amount'] = None
            transaction_data['amount_coin'] = None
        transaction_data['from_coin'] = None

    if transaction_data['from_coin'] is None or transaction_data['from_coin'] not in asset_coins:
        parsed_to_coin = transaction_data.get('to_coin', None)
        success, recommend_data = recommend_convert_token_endpoint_using_api({"user_id": user_id}, parsed_to_coin=parsed_to_coin)
        print('recommend_convert_token_endpoint_using_api success: ', success, 'recommend_data: ', recommend_data)
        if success:
            print('recommend_convert_token_endpoint_using_api success, transaction_data: ', transaction_data)
            transaction_data['from_coin'] = recommend_data['from_coin']
            transaction_data['to_coin'] = recommend_data['to_coin']
            if transaction_data['amount_coin'] == transaction_data['to_coin']:
                pass
            else: # update amount/amount_coin
                transaction_data['amount'] = recommend_data['amount']
                transaction_data['amount_coin'] = recommend_data['amount_base']
                if transaction_data['amount_coin'] in btc_coin_list:
                    transaction_data['amount'] = min(transaction_data['amount'], 0.001)
                elif transaction_data['amount_coin'] in eth_coin_list:
                    transaction_data['amount'] = min(transaction_data['amount'], 0.1)
                elif transaction_data['amount_coin'] in bnb_coin_list:
                    transaction_data['amount'] = min(transaction_data['amount'], 1)
                else:
                    transaction_data['amount'] = min(transaction_data['amount'], 10)
                print('recommend_convert_token_endpoint_using_api success, transaction_data after update: ', transaction_data)
        else:
            print('recommend_convert_token_endpoint_using_api failed, try get_high_value_coin, user_id: ', user_id)
            high_value_coin, high_value_coin_amount = get_high_value_coin(asset_data, parsed_to_coin=parsed_to_coin)
            if high_value_coin_amount == 0:
                transaction_data['from_coin'] = "BTC"
                transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + " Your assets is empty. Please deposit some assets first."
            else:
                transaction_data['from_coin'] = high_value_coin
                # transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + f" Since you didn't provide the from coin, I'd like to use {high_value_coin} as the default coin. You have {high_value_coin_amount} usdt {high_value_coin} in your account."
    
    if transaction_data['to_coin'] is None:
        if transaction_data['from_coin'] != "USDT":
            transaction_data['to_coin'] = "USDT"
            transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + " Since you didn't provide the convert to coin, I'd like to use USDT as the default convert to coin."
        else:
            transaction_data['to_coin'] = "BNB"
            transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + " Since you didn't provide the convert to coin, I'd like to use BNB as the default convert to coin."
    
    if transaction_data['from_coin'] is not None and transaction_data['to_coin'] is None:
        transaction_data['to_coin'] = "USDT"
            
   
    if transaction_data['amount'] is not None and transaction_data['amount_coin'] is None:
        transaction_data['amount_coin'] = transaction_data['from_coin']
  

    return transaction_data

def check_convert_pair_and_correct_convert_pair(transaction_data:Dict[str, Any]) -> Dict[str, Any]:
    """
    Check if the convert pair is valid.
    """
    convert_coin_list = get_convert_coin_list(transaction_data['from_coin'])
    if len(convert_coin_list) == 0:
        transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + f" Convert {transaction_data['from_coin']} coin is not supported in Binance. Please tell me another coin to convert."
        transaction_data['fail_status'] = True
        print('check_convert_pair_and_correct_convert_pair failed, convert_coin_list: ', convert_coin_list, 'from_coin: ', transaction_data['from_coin'])
        return transaction_data

    if transaction_data['to_coin'] not in convert_coin_list:
        invalid_to_coin = transaction_data['to_coin']
        if transaction_data['from_coin'] in stablecoin_set:
            transaction_data['to_coin'] = "BNB"
        else:
            transaction_data['to_coin'] = "USDT"
        transaction_data['explain_to_user'] += f"  {transaction_data['from_coin']} can not convert to {invalid_to_coin} in Binance. Let's try to convert to {transaction_data['to_coin']} instead."
        print('to_coin not in convert_coin_list, convert_coin_list: ', convert_coin_list, 'from_coin: ', transaction_data['from_coin'], 'to_coin: ', transaction_data['to_coin'])
    return transaction_data


def filter_transaction_data(transaction_data:Dict[str, Any]) -> Dict[str, Any]:
    """
    Filter the transaction data.
    """
    def is_first_priority_num(num:float) -> bool:
        """
        Check if the number is the first priority number.
        """
        if num is None:
            return False
        if num in [0.1,0.01,0.001,0.0001,0.00001]:
            return True
        if int(num) != num:
            return False
        
        return True

    def is_low_priority_num(num:float) -> bool:
        """
        Check if the number is the low priority number.
        """
        if num is None:
            return True
        if num in [0.1,0.01,0.001,0.0001,0.00001]:
            return False
        if int(num) != num:
            return True
        return False
    
    output_transaction_data = {}
    for key in ["from_coin", "to_coin"]:
        output_transaction_data[key] = transaction_data[key]
    for key in ['from_coin_amount', 'to_coin_amount']:
        if key not in transaction_data:
            transaction_data[key] = None
        

    if transaction_data['from_coin_amount'] is None and transaction_data['to_coin_amount'] is not None:
        output_transaction_data['to_coin_amount'] = transaction_data['to_coin_amount']
    elif is_first_priority_num(transaction_data['from_coin_amount']) and is_low_priority_num(transaction_data['to_coin_amount']):
        output_transaction_data['from_coin_amount'] = transaction_data['from_coin_amount']
    elif is_first_priority_num(transaction_data['to_coin_amount']) and is_low_priority_num(transaction_data['from_coin_amount']):
        output_transaction_data['to_coin_amount'] = transaction_data['to_coin_amount']
    elif is_first_priority_num(transaction_data['from_coin_amount']):
        output_transaction_data['from_coin_amount'] = transaction_data['from_coin_amount']
    elif is_first_priority_num(transaction_data['to_coin_amount']):
        output_transaction_data['to_coin_amount'] = transaction_data['to_coin_amount']
    elif transaction_data.get('amount_coin') and transaction_data.get('amount_coin') == transaction_data.get('from_coin'):
        output_transaction_data['from_coin_amount'] = transaction_data.get('from_coin_amount')
    elif transaction_data.get('amount_coin') and transaction_data.get('amount_coin') == transaction_data.get('to_coin'):
        output_transaction_data['to_coin_amount'] = transaction_data.get('to_coin_amount')
    else:
        output_transaction_data['from_coin_amount'] = transaction_data['from_coin_amount']
    
    return output_transaction_data

def get_isoformat_time():
    return datetime.now().isoformat() + "Z"

def report_to_opik(context, input_data, output_data, error_data=None, \
                   start_time=None, end_time=None, logger=None):
    try:
        trace_id = context.context_id
        user_id = context.message.metadata.get("userId")
        result_id = context.message.metadata.get("bizData", {}).get("resultId", None)
        lang = context.message.metadata.get("lang", "")
        bnc_lang = context.message.metadata.get("requestHeaders", {}).get("bnc_lang", "")
        session_id = context.message.metadata.get("sessionId")
        user_query = context.get_user_input()
        user_ip = context.message.metadata.get("userRequestIp")
        metadata = {
            'trace_id': trace_id,
            'user_id': user_id,
            'result_id': result_id,
            'lang': lang,
            'bnc_lang': bnc_lang,
            'session_id': session_id,
            'user_query': user_query,
            'user_ip': user_ip
        }
        name = 'ConvertAgent'

        logger.info(f'Reporting to Opik: name={name}, metadata={metadata}, input_data={input_data}, output_data={output_data}')


        current_time = get_isoformat_time()
        if not start_time:
            start_time = current_time
        if not end_time:
            end_time = current_time
        payload = {
            "start_time": start_time,
            "id": None,
            "project_name": "bdp-search-agent",
            "name": name,
            "end_time": end_time,
            "thread_id": "v1.0.0",
            "input": input_data,
            "output": output_data,
            "metadata": metadata,
            "tags": [],
            "error_info": error_data
        }

        environment = os.getenv("ENV", "qa").lower()
        if environment == "prod":
            url_base = "http://opik.toolsfdg.net"
        else:
            url_base = "http://opik.qa1fdg.net"

        # Make the HTTP POST request
        response = requests.post(
            f"{url_base}/api/v1/private/traces",
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=10
        )

        if response.status_code in [200, 201]:
            logger.info(f"Successfully reported to Opik: {response.json()}")
            return True
        else:
            logger.warning(f"Failed to report to Opik: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.warning(f"Error reporting to Opik: {str(e)}")
        return False


def run(user_id:int, user_command:str, pre_context:List[Dict[str, str]], \
        only_return_transaction_data=False, attach_coin_price=True, asset_data_mock=None) -> Dict[str, Any]:
    """
    Run the convert work flow.
    user_command: convert some BTC
    pre_context: [{"role": "user", "content": "Hi"}, {"role": "assistant", "content": "Hello, how can I help you?"}]
    return: {"status": "success", "agent_response": "xxxx", 
    "transaction_data": {"from_coin": "BTC", "to_coin": "USDT", "amount": 0.001, "amount_coin":"BTC"}}
    """
    start_time = time.time()
    success, transaction_data = parse_user_command(user_command, pre_context)
    if not success:
        return {"status": "success", "agent_response": "Sorry, I'm having trouble processing your request. Please try again later.", "transaction_data": {}, "transaction_data_filtered": {}}
    
    print('transaction_data after parse_user_command: ', transaction_data)
    transaction_data = set_default_value(transaction_data)
    print('transaction_data after set_default_value: ', transaction_data)

    if transaction_data['from_coin'] is None and transaction_data['to_coin'] is None and len(user_command) > len(' convert '):
        transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + " Could you make a more specific command?  like 'convert 0.001 BTC to USDT'."
        return {"status": "success", "agent_response": transaction_data['explain_to_user'], "transaction_data": {}, "transaction_data_filtered": {}}
   
    asset_data = query_user_asset(user_id)
    if asset_data_mock:
        asset_data = asset_data_mock
    print('asset_data after query_user_asset: ', str(asset_data)[0:500])
    print('asset_data after query_user_asset_2: ', str(asset_data)[-500:])
    if "assetList" in asset_data:
        totalQuoteAmount = asset_data.get("totalQuoteAmount")
        print('after query_user_asset totalQuoteAmount: ', totalQuoteAmount)
        if totalQuoteAmount is None or totalQuoteAmount <= 0:
            transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + " After checking your asssets, your current assets is empty. Please deposit some assets first."
            failed_response = "Your current assets is empty. Please deposit some assets first."
            return {"status": "success", "agent_response": failed_response, "transaction_data": {}, "transaction_data_filtered": {}}

    transaction_data = recommend_convert_coin_if_command_is_not_clear(user_id, transaction_data, asset_data)
    print('transaction_data after recommend_convert_coin_if_command_is_not_clear: ', transaction_data)

    transaction_data = estimate_convert_amount(transaction_data, asset_data)
    print('transaction_data after estimate_convert_amount: ', transaction_data)

    transaction_data = check_convert_pair_and_correct_convert_pair(transaction_data)
    print('transaction_data after check_convert_pair_and_correct_convert_pair: ', transaction_data)
    
    transaction_data = check_transaction_usdt_amount(transaction_data, asset_data, attach_coin_price=attach_coin_price)
    print(f'{round(time.time()-start_time, 2)} transaction_data after check_transaction_usdt_amount:', transaction_data)

    if only_return_transaction_data:
        return {"status": "success", "transaction_data": transaction_data, "asset_data": asset_data}

    agent_response = generate_agent_response(transaction_data, user_command, pre_context, asset_data)
    print(f'{round(time.time()-start_time, 2)} agent_response after generate_agent_response: ', agent_response)

    transaction_data_filtered = filter_transaction_data(transaction_data)
    print(f'{round(time.time()-start_time, 2)} transaction_data_filtered after filter_transaction_data: ', transaction_data_filtered)

    return {"status": "success", "agent_response": agent_response, "transaction_data": transaction_data, "transaction_data_filtered": transaction_data_filtered}

