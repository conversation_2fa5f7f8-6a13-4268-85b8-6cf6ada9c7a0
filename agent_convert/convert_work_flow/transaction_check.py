
from util.logger.logger_utils import setup_logger, log_exception_as_single_line
import httpx
import json
import os
from agent_convert.price_tool import get_price
from agent_convert.chat_convert_tool import format_number_for_display
from agent_convert.price_tool import get_price

logger = setup_logger(__name__)

btc_coin_list = ["BTC","WBTC"]
eth_coin_list = ["ETH","WETH","WBETH"]
bnb_coin_list = ["BNB","BCH","TAO","SOL","LTC","ADA","AAVE"]

def estimate_convert_amount(transaction_data: dict, asset_data: dict) -> dict:
    """
    Estimate the convert amount.
    """
    if transaction_data['amount'] is None:
        if transaction_data['from_coin'] is not None:
            if transaction_data['from_coin'] == "USDT":
                transaction_data['amount'] = 100
            elif transaction_data['from_coin'] in btc_coin_list:
                transaction_data['amount'] = 0.001
            elif transaction_data['from_coin'] in eth_coin_list:
                transaction_data['amount'] = 0.1
            elif transaction_data['from_coin'] in bnb_coin_list:
                transaction_data['amount'] = 1
            else:
                price = get_price(transaction_data['from_coin'])
                if price <= 0: # invalid coin
                    transaction_data['amount'] = 0
                else:
                    transaction_data['amount'] = int(100 / price)

            transaction_data['amount_coin'] = transaction_data['from_coin']
            transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + f" Since you didn't provide the amount, let's try to convert {transaction_data['amount']}  {transaction_data['from_coin']}."
        
        elif transaction_data['to_coin'] is not None:
            if transaction_data['to_coin'] == "USDT":
                transaction_data['amount'] = 100
            elif transaction_data['to_coin'] in btc_coin_list:
                transaction_data['amount'] = 0.001
            elif transaction_data['to_coin'] in eth_coin_list:
                transaction_data['amount'] = 0.1
            elif transaction_data['to_coin'] in bnb_coin_list:
                transaction_data['amount'] = 1
            else:
                price = get_price(transaction_data['to_coin'])
                if price <= 0:
                    transaction_data['amount'] = 10
                else:
                    transaction_data['amount'] = int(100 / price)

            transaction_data['amount_coin'] = transaction_data['to_coin']
            transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + f" Since you didn't provide the amount, let's try to convert {transaction_data['amount']}  {transaction_data['to_coin']}."
        else:
            transaction_data['from_coin'] = "BNB"
            transaction_data['to_coin'] = "USDT"
            transaction_data['amount'] = 100
            transaction_data['amount_coin'] = "USDT"
            transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + " Since you didn't provide the convert coin, let's try to convert 100 USDT from BNB."
    return transaction_data



def check_transaction_usdt_amount(transaction_data: dict, asset_data: dict, attach_coin_price=True) -> dict:
        """ input transaction_data, like {
            "explain_to_user": "You want to trade 100 USDT.",
            "from_coin": "BTC",
            "to_coin": "USDT", 
            "amount": 0.001,
            "amount_coin": "BTC"
        }

        return {"explain_to_user": "BTC现在市场价格为10500 USDT，BNB现在市场价格为768 USDT，0.01 BTC 约等于 0.15 BNB",
                "transaction_value_usdt": 0.01,
                "from_coin_price": 10500,
                "to_coin_price": 768,
                "from_coin_amount": 0.01,
                "to_coin_amount": 0.15,
                "from_coin": "BTC",
                "to_coin": "USDT"
        }

        """
        
        from_coin = transaction_data.get("from_coin", "BTC")
        to_coin = transaction_data.get("to_coin", "USDT")
        amount = transaction_data.get("amount", 0.001)
        amount_coin = transaction_data.get("amount_coin", "BTC")

        return_str = ""
        
    
        # Type checking and conversion for amount
        if isinstance(amount, (list, tuple)):
            logger.warning(f"Amount is a sequence: {amount}, taking first element")
            amount = amount[0] if amount else 0.001
        elif not isinstance(amount, (int, float)):
            logger.warning(f"Amount is not a number: {amount} (type: {type(amount)}), using default")
            amount = 0.001
        
        # Convert to float to ensure it's a number
        amount = float(amount)
        
        # Get current prices for both coins
        
        if from_coin in ["USDT", "USDC", "USDC.e"]:
            from_coin_price = 1.0
        else:
            from_coin_price = get_price(from_coin)
            # Type checking for from_coin_price
            if isinstance(from_coin_price, (list, tuple)):
                logger.warning(f"from_coin_price is a sequence: {from_coin_price}, taking first element")
                from_coin_price = from_coin_price[0] if from_coin_price else -1
            elif not isinstance(from_coin_price, (int, float)):
                logger.warning(f"from_coin_price is not a number: {from_coin_price} (type: {type(from_coin_price)}), using -1")
                from_coin_price = -1
            from_coin_price = float(from_coin_price)
        
        if to_coin in ["USDT", "USDC", "USDC.e"]:
            to_coin_price = 1.0
        else:
            to_coin_price = get_price(to_coin)
            # Type checking for to_coin_price
            if isinstance(to_coin_price, (list, tuple)):
                logger.warning(f"to_coin_price is a sequence: {to_coin_price}, taking first element")
                to_coin_price = to_coin_price[0] if to_coin_price else -1
            elif not isinstance(to_coin_price, (int, float)):
                logger.warning(f"to_coin_price is not a number: {to_coin_price} (type: {type(to_coin_price)}), using -1")
                to_coin_price = -1
            to_coin_price = float(to_coin_price)
        
        if from_coin_price < 0 or to_coin_price < 0:
            logger.warning(f" Unable to get price for {from_coin} or {to_coin}")
            transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + f"Can not find coin price right now. Do not show coin price in the response to user."
            return transaction_data
        
        # Calculate the conversion
        if amount_coin == to_coin: 
            to_coin_amount = amount
            tramsaction_value_usdt = amount * to_coin_price
            from_coin_amount = tramsaction_value_usdt / from_coin_price
    
        else: # amount_coin == from_coin:
            from_coin_amount = amount
            tramsaction_value_usdt = amount * from_coin_price
            to_coin_amount = tramsaction_value_usdt / to_coin_price

        if attach_coin_price:
            if from_coin not in ["USDT", "USDC", "USDC.e"]:
                price_str = format_number_for_display(from_coin_price)
                return_str = return_str + f"Current market price of {from_coin} is about {price_str} USDT，"
            if to_coin not in ["USDT", "USDC", "USDC.e"]:
                price_str = format_number_for_display(to_coin_price)
                return_str = return_str + f"Current market price of {to_coin} is about {price_str} USDT，"
            from_coin_amount_str = format_number_for_display(from_coin_amount)
            to_coin_amount_str = format_number_for_display(to_coin_amount)
            return_str = return_str + f"{from_coin_amount_str} {from_coin} is about equal to {to_coin_amount_str} {to_coin}"
        
        
        if asset_data is not None and "assetList" in asset_data:
            totalQuoteAmount = asset_data.get("totalQuoteAmount")
            if totalQuoteAmount is None or totalQuoteAmount <= 0:
                return_str = return_str + " Your total asses is 0, please deposit some assets first."
            else:
                if totalQuoteAmount < tramsaction_value_usdt:
                    return_str = return_str + f" The amount you want to trade is larger than your total assets."
            for asset_dict in asset_data["assetList"]:
                if asset_dict["asset"] == from_coin:
                    # special judgement, the amount usdt less than 0.01
                    if asset_dict['quoteAmount'] < 0.01:
                        return_str += f"Sorry, your {from_coin} balance is worth less than 0.01 USDT, which isn't enough for conversion. Please deposit more {from_coin} to your account."
                        transaction_data['fail_status'] = True
                    elif asset_dict["amount"] < from_coin_amount:
                        return_str = return_str + f" As you have {asset_dict['amount']} {from_coin} in your account, let's reduce the transaction value to {asset_dict['amount']} {from_coin}."
                        from_coin_amount = asset_dict["amount"]
                        tramsaction_value_usdt = from_coin_amount * from_coin_price
                        to_coin_amount = tramsaction_value_usdt / to_coin_price
                        from_coin_amount_str = format_number_for_display(from_coin_amount)
                        to_coin_amount_str = format_number_for_display(to_coin_amount)
                        return_str = return_str + f" {from_coin_amount} {from_coin} is about equal to {to_coin_amount} {to_coin}"

                    break

        # Validate transaction value limits: minimum 0.01 USDT, maximum 1000 USDT
        if tramsaction_value_usdt > 1000:
            return_str = return_str + "The maximum order value is 1000 USDT equivalent. Your order is above this limit, so I'll adjust it to meet the requirement."
            tramsaction_value_usdt = 1000
            from_coin_amount = tramsaction_value_usdt / from_coin_price
            to_coin_amount = tramsaction_value_usdt / to_coin_price
            from_coin_amount_str = format_number_for_display(from_coin_amount)
            to_coin_amount_str = format_number_for_display(to_coin_amount)
            return_str = return_str + f"After adjustment: {from_coin_amount_str} {from_coin} is about equal to {to_coin_amount_str} {to_coin}"
        elif tramsaction_value_usdt < 0.01:
            return_str = return_str + "The minimum order value is 0.01 USDT equivalent. Your order is below this limit, so I'll adjust it to meet the requirement."
            tramsaction_value_usdt = 0.01
            from_coin_amount = tramsaction_value_usdt / from_coin_price
            to_coin_amount = tramsaction_value_usdt / to_coin_price
            from_coin_amount_str = format_number_for_display(from_coin_amount)
            to_coin_amount_str = format_number_for_display(to_coin_amount)
            return_str = return_str + f"After adjustment:  {from_coin_amount_str} {from_coin} is about equal to {to_coin_amount_str} {to_coin}"

            
        if attach_coin_price is True:
            transaction_data['from_coin_price'] = from_coin_price
            transaction_data['to_coin_price'] = to_coin_price

        transaction_data['explain_to_user'] = transaction_data['explain_to_user'] + return_str
        transaction_data['transaction_value_usdt'] = tramsaction_value_usdt
        transaction_data['from_coin_amount'] = from_coin_amount
        transaction_data['to_coin_amount'] = to_coin_amount
        transaction_data['from_coin'] = from_coin
        transaction_data['to_coin'] = to_coin
        return transaction_data
                
        