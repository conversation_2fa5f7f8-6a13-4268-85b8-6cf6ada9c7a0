"""
RAG Root Agent for A2A Service

This module creates a root agent that coordinates between different RAG capabilities:
- News Analysis (using rag_agent/news)
- Market Analysis (using rag_agent/market) 
- General search and information retrieval

The root agent determines user intent and routes to the appropriate specialized agent.
"""

import asyncio
import sys
import os
from typing import Dict, Any, Optional, AsyncGenerator, List, AsyncIterable
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from google.adk.agents import Agent
from google.adk.tools import FunctionTool
from google.adk.agents.callback_context import CallbackContext
from pydantic import BaseModel, Field
from google.genai.types import Content, Part

from conf import model_config
from util.logger.logger_utils import setup_logger
from util.tools.memory import set_user_context, set_session_context, set_current_session_id, clear_session_context
from util.tools.language_map import map_iso_to_language
from util.tools.unified_session_service import unified_session_service
from google.adk.artifacts.in_memory_artifact_service import InMemoryArtifactService
from google.adk.memory.in_memory_memory_service import InMemoryMemoryService
from dotenv import load_dotenv

# # Setup GoogleADK instrumentation for Langfuse tracing
from openinference.instrumentation.google_adk import GoogleADKInstrumentor
GoogleADKInstrumentor().instrument()

# Import specialized agents and their functions
from google.adk import Runner
from rag_agent.rag_helper.helper import parse_json_response
from rag_agent.news.optimized_news_agent import create_optimized_news_agent
from rag_agent.market.market_analysis_agent import create_market_analysis_agent
# Import both methods for testing
from rag_agent.google.google_search_agent import create_sequential_google_search_agent
# Import both methods for testing
from rag_agent.google.google_search_agent import create_sequential_google_search_agent
from rag_agent.campaign.campaign_agent import create_campaign_analysis_agent
from rag_agent.campaign.campaign_agent import create_campaign_cards

# Import the RAG root agent prompt
from a2a_agent.rag_root_agent_prompt import RAG_ROOT_AGENT_PROMPT
from google.adk.agents.run_config import RunConfig, StreamingMode
from rag_agent.rag_helper.helper import get_session_metadata
from util.opik_util import opik_tracer
from agent_translate import create_translate_agent
import logging

logger = setup_logger(__name__)
logger.setLevel(logging.WARNING)
APP_NAME = "RagRootAgent_A2A"

load_dotenv()

class RagRootAgent:
    """
    Root agent that coordinates different RAG capabilities through A2A protocol.
    Determines user intent and routes to appropriate specialized agents.
    """
    
    def __init__(self):
        self.news_agent = None 
        self.market_agent = None
        self.google_search_agent = None
        self.campaign_agent = None
        self._session_id = None
        self.translate_agent = None
        self._build_translate_agent()
        self.root_agent = None
        self.root_agent = self._build_root_agent()
        self.runner = Runner(
            app_name="RagRootAgent_A2A",
            agent=self.root_agent,
            session_service=unified_session_service,
            artifact_service=InMemoryArtifactService(),
            memory_service=InMemoryMemoryService(),
        )

    def _build_translate_agent(self):
        """Build the translate agent if not already created"""
        if not self.translate_agent:
            try:
                self.translate_agent = create_translate_agent()
                if self.translate_agent:
                    logger.info("Created Translate agent for RAG root")
                else:
                    logger.error("Failed to create translate agent - returned None")
            except Exception as e:
                logger.error(f"Error creating translate agent: {e}")
                self.translate_agent = None
        
    def _build_specialized_agents(self):
        """Build the specialized agents if not already created"""
        try:
            if not self.news_agent:
                self.news_agent = create_optimized_news_agent()
                if self.news_agent:
                    logger.info("Created News summary generation agent for RAG root")
                else:
                    logger.error("Failed to create news agent - returned None")

            if not self.market_agent:
                self.market_agent = create_market_analysis_agent()
                if self.market_agent:
                    logger.info("Created Market analysis agent for RAG root")
                else:
                    logger.error("Failed to create market agent - returned None")
                
            if not self.google_search_agent:
                self.google_search_agent = create_sequential_google_search_agent(faq_mode=True) 
                if self.google_search_agent:
                    logger.info("Created Sequential Google Search agent for RAG root")
                else:
                    logger.error("Failed to create google search agent - returned None")
            
            if not self.campaign_agent:
                self.campaign_agent = create_campaign_analysis_agent(user_query="User query will be provided by the user", user_language="User language will be provided by the user", use_user_context=False)
                if self.campaign_agent:
                    logger.info("Created Campaign Analysis agent for RAG root")
                else:
                    logger.error("Failed to create campaign agent - returned None")
                    
            # Debug: Log the status of all agents
            logger.info(f"Agent creation status - News: {self.news_agent is not None}, Market: {self.market_agent is not None}, Google: {self.google_search_agent is not None}, Campaign: {self.campaign_agent is not None}")
            
        except Exception as e:
            logger.error(f"Error in _build_specialized_agents: {e}")
            raise
    
    def _build_root_agent(self) -> Agent:
        """
        Build the root agent that coordinates between specialized RAG agents.
        """
        if self.root_agent is None:
            logger.info("Building RAG Root Agent for A2A service")
            
            # Build specialized agents first
            self._build_specialized_agents()
            
            # Validate that all agents were created successfully
            sub_agents_list = [self.news_agent, self.market_agent, self.google_search_agent, self.campaign_agent]
            valid_agents = [agent for agent in sub_agents_list if agent is not None]
            
            if len(valid_agents) != len(sub_agents_list):
                logger.error(f"Some agents failed to create. Valid agents: {len(valid_agents)}/{len(sub_agents_list)}")
                logger.error(f"Agent status: news={self.news_agent is not None}, market={self.market_agent is not None}, google={self.google_search_agent is not None}, campaign={self.campaign_agent is not None}")
                raise RuntimeError("Failed to create all required specialized agents")
            
            logger.info(f"All {len(valid_agents)} specialized agents created successfully")
            
            # Create session-aware callback for context management
            def session_aware_callback(callback_context: CallbackContext) -> None:
                """Session-aware callback that sets up user context"""
                opik_tracer.before_agent_callback(callback_context)
                try:
                    if self._session_id:
                        set_current_session_id(self._session_id)
                        # Set basic context in callback state
                        callback_context.state["session_id"] = self._session_id
                        callback_context.state["user_language"] = "english"  # Default language
                        logger.info(f"RAG Root callback context set for session: {self._session_id}")
                except Exception as e:
                    logger.error(f"Error in RAG root callback: {e}")
            RAG_ROOT_AGENT_CONFIG = {
                "temperature": 0.3,
                "max_output_tokens": 2048,
                "top_p": 0.8,
                "top_k": 30,
                "include_thoughts": False,  # Enable Gemini's thinking process
                "thinking_budget": 0,  # Set thinking budget to 1000
                # "temperature": TEMPERATURE,  # Low temperature for analytical tasks
            }
            # Create the root agent with all sub-agents (no direct tools)
            self.root_agent = Agent(
                name="RagRootAgent",
                model=model_config.MODEL_FLASH,
                # generate_content_config=model_config.create_generate_content_config(model_config.DEFAULT_AGENT_CONFIG), not used 
                planner=model_config.create_planner(RAG_ROOT_AGENT_CONFIG),
                description="Root coordinator for RAG capabilities including news, market analysis, and general search queries",
                instruction=RAG_ROOT_AGENT_PROMPT.format(user_language="english"),
                sub_agents=[self.news_agent, self.market_agent, self.google_search_agent, self.campaign_agent],
                before_agent_callback=session_aware_callback,
                after_agent_callback=opik_tracer.after_agent_callback,
                before_model_callback=opik_tracer.before_model_callback,
                after_model_callback=opik_tracer.after_model_callback,
                before_tool_callback=opik_tracer.before_tool_callback,
                after_tool_callback=opik_tracer.after_tool_callback,
            )
            logger.info(f"RAG Root Agent created with {len([agent for agent in [self.news_agent, self.market_agent, self.google_search_agent, self.campaign_agent] if agent is not None])} sub-agents")

        return self.root_agent

    async def invoke(self, query: str, session_id: str, user_language: str, user_id: str, **kwargs) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Main invoke method for the RAG Root Agent.
        
        Args:
            query: User's query
            session_id: Optional session identifier
            **kwargs: Additional parameters (user_language, user_id, etc.)
            
        Yields:
            Dict: Events containing the agent's response
        """
        logger.info(f"RagRootAgent.invoke called with query: '{query[:50]}...'")
        
        # Store session context
        self._session_id = session_id or f"rag_root_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            # Try to acquire a session slot using unified session service
            logger.info(f"[DEBUG] Attempting to acquire session slot for: {session_id}")
            if not await unified_session_service.acquire_session(session_id):
                raise Exception("RESOURCE_EXHAUSTED: Maximum concurrent sessions exceeded. Please try again later.")
            logger.info(f"[DEBUG] Session slot acquired for: {session_id}")
            # set user context
            normalized_user_language = map_iso_to_language(user_language)
            set_user_context(user_id, normalized_user_language, self._session_id)
            # Set up session context
            # Set session context for agents
            logger.info(f"[DEBUG] Setting session context for: {session_id}")
            set_session_context(session_id=session_id, user_id=user_id, language=user_language)
            logger.info(f"[DEBUG] Session context set for: {session_id}, user_language: {user_language}")

            # Create a Session using unified session service
            logger.info(f"[DEBUG] Creating session service session for: {session_id}")
            await unified_session_service.create_session(app_name=APP_NAME, user_id=user_id, session_id=session_id)
            logger.info(f"[DEBUG] Session service session created for: {session_id}")

            # Set the current session ID for agent tools
            from util.tools.memory import set_current_session_id
            set_current_session_id(session_id)
            logger.info(f"[DEBUG] Current session ID set for agent tools: {session_id}")
            
            
            logger.info(f"RAG Root session context set - User: {user_id}, Language: {normalized_user_language}, Session: {self._session_id}")
            
            # Build the root agent
            if not self.root_agent:
                logger.info("Building RAG Root Agent")
                self.root_agent = self._build_root_agent()
            # Create session for the runner
            session = await self.runner.session_service.create_session(
                app_name="RagRootAgent_A2A",
                user_id=user_id
            )
            logger.info(f"RAG Root session created: {session}")
            input_query = f"""User Query: {query} | User langauge: {user_language}"""
            # Create query content
            query_content = Content(
                parts=[Part.from_text(text=input_query)],
                role="user"
            )
            
            # Execute the root agent and stream responses
            agent_response = ""
            active_agent = "rag_root"  # Track the current active agent across events
            
            run_config = RunConfig(
                response_modalities=["TEXT"],
                streaming_mode=StreamingMode.NONE,
            )
            async for event in self.runner.run_async(
                session_id=session.id,
                user_id=user_id,
                new_message=query_content,
                run_config=run_config
            ):
                # Check for transfer_to_agent function call
                if hasattr(event, 'content') and event.content and event.content.parts:
                    for part in event.content.parts:
                        if hasattr(part, 'function_call') and part.function_call:
                            function_name = getattr(part.function_call, 'name', '')
                            if 'transfer_to_agent' in function_name.lower():
                                active_agent = self._detect_transfer_agent(part)
                                logger.info(f"[DEBUG] Detected transfer_to_agent: {active_agent}")

                # Handle streaming response events
                if hasattr(event, 'content') and event.content and event.content.parts:
                    for part in event.content.parts:
                        if hasattr(part, 'thought') and part.thought:
                                logger.info(f"[DEBUG] Skipping thinking content for session {session_id}: {part.text if part.text else 'No text'}...")
                                continue
                        if hasattr(part, 'text') and part.text:
                            # Yield text chunks for streaming with detected agent
                            yield {
                                "is_task_complete": False,
                                "type": "text_chunk",
                                "content": part.text,
                                "session_id": self._session_id,
                                "agent": active_agent
                            }
                
                # Check if turn is complete
                if hasattr(event, 'turn_complete') and event.turn_complete:
                    logger.info(f"Turn Complete Event: {event} (Active Agent: {active_agent})")
                    break
            
            # Process final response
            if agent_response:
                logger.info(f"Processing final agent response: {agent_response}")
                logger.info(f"Detected specialized agent: {active_agent}")
                
                try:
                    parsed_response = parse_json_response(agent_response)
                    title = parsed_response.get("title", "Search Analysis")
                    body = parsed_response.get("body", agent_response)
                    symbol = parsed_response.get("symbol", "")
                    logger.info(f"Successfully parsed response - Title: {title[:50]}..., Body length: {len(body)}")
                except Exception as parse_error:
                    logger.error(f"Error parsing JSON response: {parse_error}")
                    # Fallback to using raw response
                    title = "Search Analysis"
                    body = agent_response
                
                # Create final text response
                final_text = f"{title}\n\n{body}"   
                logger.info("Creating final response structure")
                
                # Create structured final response
                yield {
                    "is_task_complete": True,
                    "type": "final_response",
                    "status": "success", 
                    "title": title,
                    "body": final_text,
                    "symbol": symbol,
                    "specialized_agent": active_agent,
                    "query": query,
                    "session_id": self._session_id,
                    "timestamp": datetime.now().isoformat(),
                    "agent": "rag_root"
                }
                logger.info("Final response yielded successfully")
            else:
                # No response case
                yield {
                    "type": "error",
                    "status": "error",
                    "error": "No response received from RAG root agent",
                    "query": query,
                    "session_id": self._session_id,
                    "timestamp": datetime.now().isoformat(),
                    "agent": "rag_root"
                }
                
        except Exception as e:
            logger.error(f"Error in RagRootAgent.invoke: {e}")
            
            yield {
                "type": "error",
                "status": "error",
                "error": str(e),
                "query": query,
                "session_id": self._session_id,
                "timestamp": datetime.now().isoformat(),
                "agent": "rag_root"
            }
            
        finally:
            # Clean up session context
            try:
                clear_session_context(self._session_id)
                logger.info(f"Cleaned up RAG root session context: {self._session_id}")
            except Exception as e:
                logger.error(f"Error cleaning up RAG root session context: {e}")

    async def stream(self, query: str, session_id: str, user_language: str, user_id: str, **kwargs) -> AsyncIterable[Dict[str, Any]]:
        """
        ADK-compatible streaming method that yields updates in the expected format.
        
        This method wraps the existing invoke method to provide the ADK streaming interface:
        - Yields intermediate updates with is_task_complete=False
        - Yields final response with is_task_complete=True
        """
        logger.info(f"RagRootAgent.stream called for session {session_id}")
        try:
            # Try to acquire a session slot using unified session service
            session = await self.runner.session_service.get_session(
                app_name=APP_NAME,
                user_id=user_id,
                session_id=session_id,
            )
            # session created 
            if session is None:
                session = await self.runner.session_service.create_session(
                    app_name=APP_NAME,
                    user_id=user_id,
                    session_id=session_id,
                )
                logger.info(f"RAG Root session created: {session}")

            logger.info(f"[DEBUG] Attempting to acquire session slot for: {session_id}")
            if not await unified_session_service.acquire_session(session_id):
                raise Exception("RESOURCE_EXHAUSTED: Maximum concurrent sessions exceeded. Please try again later.")
            logger.info(f"[DEBUG] Session slot acquired for: {session_id}")
            # set user context
            normalized_user_language = map_iso_to_language(user_language)
            set_user_context(user_id, normalized_user_language, session_id)
            # Set session context for agents
            logger.info(f"[DEBUG] Setting session context for: {session_id}")
            set_session_context(session_id=session_id, user_id=user_id, language=user_language)
            logger.info(f"[DEBUG] Session context set for: {session_id}, user_language: {user_language}")

            # Set the current session ID for agent tools
            set_current_session_id(session_id)
            logger.info(f"RAG Root session context set - User: {user_id}, Language: {normalized_user_language}, Session: {session_id}")
            
            # Build the translate agent if needed
            if not self.translate_agent:
                self._build_translate_agent()
            
            # Build the root agent
            if not self.root_agent:
                logger.info("Building RAG Root Agent")
                self.root_agent = self._build_root_agent()
            
            input_query = f"""User Query: {query} | User langauge: {user_language}"""
            logger.info(f"RagRootAgent.stream called with input_query: '{input_query}'")
            # Create query content
            query_content = Content(
                parts=[Part.from_text(text=input_query)],
                role="user"
            )
            # Execute the root agent and stream responses
            agent_response = ""
            active_agent = "rag_root"  # Track the current active agent across events
            # text 
            
            run_config = RunConfig(
                response_modalities=["TEXT"],
                streaming_mode=StreamingMode.SSE,
                max_llm_calls=1000,
            )
            logger.info(f"[DEBUG] RunConfig: {run_config}")

            async for event in self.runner.run_async(
                session_id=session.id,
                user_id=user_id,
                new_message=query_content,
                run_config=run_config
            ):
                # the actual event handling is done here.
                if hasattr(event, 'content') and event.content and event.content.parts:
                    for part in event.content.parts:
                        if hasattr(part, 'function_call') and part.function_call:
                            function_name = getattr(part.function_call, 'name', '')
                            if 'transfer_to_agent' in function_name.lower():
                                active_agent = self._detect_transfer_agent(part)
                                logger.info(f"[DEBUG] Detected transfer_to_agent: {active_agent}") 

                # Handle streaming response events
                if hasattr(event, 'content') and event.content and event.content.parts:
                    for part in event.content.parts:
                        if hasattr(part, 'thought') and part.thought:
                                logger.info(f"[DEBUG] Skipping thinking content for session {session_id}: {part.text if part.text else 'No text'}...")
                                continue
                        if hasattr(part, 'text') and part.text:
                            # only if partial is True, yeild the text chunk 
                            if event.partial:
                                # Yield text chunks for streaming with detected agent
                                logger.info(f"[DEBUG] Yielding text chunk: {part.text}")
                                yield {
                                    "is_task_complete": False,
                                    "type": "text_chunk",
                                    "content": part.text,
                                    "session_id": session_id,
                                    "agent": active_agent
                                }
                            else:
                                # partial is None, this is the final response 
                                agent_response = part.text
            
            # Process final response
            if agent_response and len(agent_response) > 0:
                logger.info(f"Processing final agent response (length: {len(agent_response)})")
                logger.info(f"Detected specialized agent: {active_agent}")
                logger.debug(f"Response preview: {agent_response[:200]}..." if len(agent_response) > 200 else f"Full response: {agent_response}")
                logger.info("Creating final response structure")
                
                # Get agent-specific token
                agent_token = ""
                if active_agent == "market_analysis_agent":
                    agent_token = get_session_metadata(session_id, 'market_token', 'market_metadata_')
                    logger.info(f"Market token: {agent_token}")
                elif active_agent == "campaign_analysis_agent":
                    campaign_args = get_session_metadata(session_id, 'campaign_args', "campaign_metadata_")
                    agent_token = campaign_args.get('token', '') if isinstance(campaign_args, dict) else ''
                    logger.info(f"Campaign token: {agent_token}")
                elif active_agent == "news_agent":
                    # Add news token logic if needed
                    agent_token = get_session_metadata(session_id, 'news_token', 'news_metadata_') or ""
                    logger.info(f"News token: {agent_token}")
                
                # Prepare response data
                response_data = {
                    "is_task_complete": True,
                    "type": "final_response",
                    "status": "success", 
                    "body": agent_response,
                    "specialized_agent": active_agent,
                    "query": query,
                    "session_id": session_id,
                    "timestamp": datetime.now().isoformat(),
                    "agent": active_agent,
                    "token": agent_token
                }
                
                # Add campaign-specific data if this is a campaign agent response
                if active_agent == "campaign_analysis_agent":
                    campaign_metadata_list = get_session_metadata(session_id, "card_metadata", "campaign_metadata_")
                    if campaign_metadata_list:
                        campaign_args = get_session_metadata(session_id, 'campaign_args', "campaign_metadata_")
                        campaign_type = campaign_args.get("campaign_type", "")
                        campaign_cards_data = await create_campaign_cards(campaign_type, campaign_metadata_list, agent_token, "binance.com", user_language, self.translate_agent, session_id)
                        response_data["campaign_cards_data"] = campaign_cards_data
                        logger.info(f"Added campaign_cards_data to response: {len(campaign_cards_data)} cards")
                
                yield response_data

        except Exception as e:
            logger.error(f"Error in RagRootAgent.stream: {e}")
            yield {
                "type": "error",
                "status": "error",
                "error": str(e),
            }
    
    def _detect_transfer_agent(self, part: Part) -> str:
        """
        Detect which specialized agent was likely used based on response content and query.
        """
        current_active_agent = ""
        try:
            args = part.function_call.args
            logger.info(f"[DEBUG] transfer_to_agent args type: {type(args)}, value: {args}")
            
            # Handle both dict and JSON string formats
            if isinstance(args, dict):
                parsed_args = args
            elif isinstance(args, str):
                try:
                    import json
                    parsed_args = json.loads(args)
                    logger.info(f"[DEBUG] Successfully parsed JSON args: {parsed_args}")
                except json.JSONDecodeError as e:
                    logger.error(f"[DEBUG] Failed to parse JSON args: {e}")
                    return current_active_agent
            else:
                logger.warning(f"[DEBUG] Unexpected args type: {type(args)}")
                return current_active_agent
            
            target_agent = parsed_args.get('agent_name', parsed_args.get('target', parsed_args.get('name', '')))
            if target_agent:
                if 'news' in target_agent.lower():
                    current_active_agent = "news_agent"
                elif 'market' in target_agent.lower():
                    current_active_agent = "market_analysis_agent"
                elif 'campaign' in target_agent.lower():
                    current_active_agent = "campaign_analysis_agent"
                elif 'google' in target_agent.lower() or 'search' in target_agent.lower():
                    current_active_agent = "google_search_agent"
                logger.info(f"[DEBUG] Detected agent via transfer_to_agent: {current_active_agent}")
        except Exception as e:
            logger.error(f"[DEBUG] Error in _detect_transfer_agent: {e}")
            
        return current_active_agent
                


def create_rag_root_agent() -> RagRootAgent:
    """
    Factory function to create a new RAG Root Agent instance.
    
    Returns:
        RagRootAgent: Configured RAG root agent instance
    """
    logger.info("Creating RAG Root Agent for A2A service")
    return RagRootAgent()