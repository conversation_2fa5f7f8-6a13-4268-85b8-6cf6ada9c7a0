import asyncio
import httpx
import os

from a2a.server.agent_execution import <PERSON><PERSON><PERSON><PERSON><PERSON>, RequestContext
from a2a.server.events import EventQueue
from a2a.types import UnsupportedOperationError
from a2a.utils import new_agent_text_message, new_agent_parts_message 
from a2a.utils.errors import ServerError
from a2a_agent.rag_root_agent import RagRootAgent
from util.logger.logger_utils import setup_logger
from util.logger.log_context import set_trace_id, set_session_id, set_user_id
from a2a.types import Part, TextPart, DataPart
import json 
from util.tools.compliance import is_business_compliance, BusinessType
from rag_agent.rag_helper.helper import (
    get_summary_async,
    build_parts_from_summary_data,  
    get_score_from_summary_data,
    create_market_analysis_parts,
    create_news_analysis_parts,
    create_google_search_part,
    create_cardsJson_parts
)

logger = setup_logger(__name__)

async def create_summary_parts(result_id: str, lang: str, user_id: str):
    """Create parts for summary response by fetching summary data."""
    try:
        result = await get_summary_async(result_id, lang, user_id, scene="searchRagAgent")
        # logger.info(f"getAISearchRAGMeta: result: {result}")
        
        if result["status"] == "OK":
            es_data = result["data"]  # could be None 
            
            if es_data:
                summary = es_data.get("esSummary", {}).get("body", {}).get('text', '')
                cardsJson = es_data.get("cardsJson", "")
                
                if cardsJson:
                    return create_cardsJson_parts(cardsJson)
                else:
                    return [Part(root=TextPart(text=summary))]
            else:
                return [Part(root=TextPart(text="No content available"))]
        else:
            return [Part(root=TextPart(text="No content available"))]
            
    except Exception as e:
        logger.error(f"Error creating summary parts: {e}")
        return [Part(root=TextPart(text="Error retrieving summary"))]

class RagRootAgentExecutorSimple(AgentExecutor):
    """Simple, non-streaming version of RagRootAgentExecutor"""
    
    def __init__(self):
        self.rag_root_agent = RagRootAgent()
    
    async def execute(self, context: RequestContext, event_queue: EventQueue) -> None:
        trace_id = context.context_id
        logger.info(f"RagRootAgentExecutorSimple: trace_id: {trace_id}")
        
        user_id = context.message.metadata.get("userId")
        result_id = context.message.metadata.get("bizData", {}).get("resultId", None)
        lang = context.message.metadata.get("lang") 
        session_id = context.message.metadata.get("sessionId")
        # get ip from context metadata 
        user_ip = context.message.metadata.get("userRequestIp")
        user_query = context.get_user_input()
        set_trace_id(trace_id)
        set_session_id(session_id)
        set_user_id(user_id)
        
        try:
            # Handle direct summary case
            if result_id is not None and result_id != "":
                parts = await create_summary_parts(result_id, lang, user_id)
                await event_queue.enqueue_event(new_agent_parts_message(parts=parts, context_id=trace_id))
                return
            
            # Handle RAG agent processing - collect final result only
            logger.info(f"RagRootAgentExecutorSimple: Processing query '{user_query[:50]}...'")
            
            final_response = None
            accumulated_text = ""
            
            async for event in self.rag_root_agent.invoke(
                query=user_query, 
                session_id=session_id, 
                user_language=lang, 
                user_id=user_id,
                task_id=trace_id
            ):
                
                if not event.get("is_task_complete", False):
                    # Just accumulate, don't stream
                    chunk_text = event.get("content", "")
                    accumulated_text += chunk_text
                    
                elif event.get("type") == "final_response":
                    final_response = event
                    break
                    
                elif event.get("type") == "error":
                    error_msg = f"Error: {event.get('error', 'Unknown error')}"
                    await event_queue.enqueue_event(new_agent_text_message(text=error_msg, context_id=trace_id))
                    return
            
            # Send final result only
            if final_response:
                await self._send_final_response(final_response, accumulated_text, event_queue, trace_id, user_id, user_ip, lang)
                return 
            else:
                await event_queue.enqueue_event(new_agent_text_message(text="No response generated for your query.", context_id=trace_id))
                return 
                
        except Exception as e:
            import traceback
            logger.error(f"Error in RagRootAgentExecutorSimple.execute: {e}")
            logger.error(f"Full traceback: {traceback.format_exc()}")
            error_message = f"Error processing RAG request: {str(e)}"
            await event_queue.enqueue_event(new_agent_text_message(text=error_message, context_id=trace_id))

    async def _send_final_response(self, final_response: dict, accumulated_text: str, event_queue: EventQueue, trace_id: str, user_id: str, user_ip: str, lang: str) -> None:
        """Send the final response, handling market analysis with data parts or regular text messages."""
        logger.info(f"final_response: {final_response}")
        body = final_response.get("body", accumulated_text)
        symbol = final_response.get("symbol", "")
        title = final_response.get("title", "")
        
        # Create parts based on response type - detect agent type
        specialized_agent = final_response.get("specialized_agent", "")
        if specialized_agent == 'market_analysis_agent':
            is_compliance = is_business_compliance(BusinessType.SPOT, user_id, lang, user_ip)
            logger.info(f"RagRootAgentExecutorSimple: is_compliance: {is_compliance}")
            parts = create_market_analysis_parts(body, symbol, is_compliance)
        elif specialized_agent == "news_agent":
            parts = create_news_analysis_parts(body=body, news_data=None)  
        elif specialized_agent == "google_search_agent":
            parts = create_google_search_part(body=body, news_data=None)
        else:
            parts = [Part(root=TextPart(text=body))]
        
        # Send message with parts
        await event_queue.enqueue_event(new_agent_parts_message(
            parts=parts,
            context_id=trace_id
        ))

    async def cancel(self, context: RequestContext, event_queue: EventQueue) -> None:
        raise ServerError(error=UnsupportedOperationError())
    