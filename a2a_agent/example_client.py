#!/usr/bin/env python3
"""
Example client for the A2A Cryptocurrency News Agent
Demonstrates how to interact with the news agent using A2A protocol.
"""

import asyncio
import json
import uuid
import httpx
from datetime import datetime
from typing import Dict, Any, Optional
import time 

# Configuration
NEWS_AGENT_BASE_URL = "http://0.0.0.0:8001/"  # Updated for integrated main.py
CLIENT_AGENT_ID = "example-client-agent"


class A2ARagAgentClient:
    """
    Client for interacting with the A2A Cryptocurrency RAG Agent.
    Demonstrates proper A2A protocol usage for news, market analysis, and general search.
    """
    
    def __init__(self, base_url: str = NEWS_AGENT_BASE_URL):
        self.base_url = base_url.rstrip("/")
        self.client_id = CLIENT_AGENT_ID
        
    async def get_agent_card(self, agent_type: str = "agent") -> Optional[Dict[str, Any]]:
        """
        Fetch the agent card to understand capabilities.
        
        Args:
            agent_type: Either "agent" (RAG root)
        """
        print(f"Fetching {agent_type.upper()} Agent Card...")
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.base_url}/{agent_type}/.well-known/agent-card.json")
                response.raise_for_status()
                
                agent_card = response.json()
                print(f"{agent_type.upper()} Agent Card retrieved successfully!")
                print(f"Agent Name: {agent_card.get('name', 'Unknown')}")
                print(f"Description: {agent_card.get('description', 'No description')}")
                print(f"Version: {agent_card.get('version', 'Unknown')}")
                print(f"URL: {agent_card.get('url', 'Unknown')}")
                
                # Display input/output modes
                input_modes = agent_card.get('defaultInputModes', [])
                output_modes = agent_card.get('defaultOutputModes', [])
                print(f"Input Modes: {', '.join(input_modes)}")
                print(f"Output Modes: {', '.join(output_modes)}")
                
                # Display capabilities
                capabilities = agent_card.get('capabilities', {})
                if capabilities:
                    print("Capabilities:")
                    for key, value in capabilities.items():
                        print(f"  - {key}: {value}")
                
                # Display skills
                skills = agent_card.get('skills', [])
                print(f"Available Skills ({len(skills)}):")
                for skill in skills:
                    skill_name = skill.get('name', 'Unknown')
                    skill_desc = skill.get('description', 'No description')
                    skill_tags = skill.get('tags', [])
                    skill_examples = skill.get('examples', [])
                    
                    print(f"  • {skill_name}")
                    print(f"    Description: {skill_desc}")
                    if skill_tags:
                        print(f"    Tags: {', '.join(skill_tags)}")
                    if skill_examples:
                        print(f"    Examples:")
                        for example in skill_examples[:3]:  # Show first 3 examples
                            print(f"      - \"{example}\"")
                    print()
                
                return agent_card
                
            except httpx.HTTPError as e:
                print(f"Failed to fetch {agent_type} agent card: {e}")
                return None
    
    async def discover_all_agents(self) -> Dict[str, Dict[str, Any]]:
        """
        Discover all available agents and their capabilities.
        """
        print("🔍 Discovering all available agents...")
        agents = {}
        
        agent_types = ["agent"]  # Both RAG root and news agents
        
        for agent_type in agent_types:
            print(f"\n{'='*50}")
            agent_card = await self.get_agent_card(agent_type)
            if agent_card:
                agents[agent_type] = agent_card
        
        print(f"\n🎯 Discovery completed! Found {len(agents)} agent(s)")
        return agents
    
    async def test_all_endpoints(self) -> None:
        """
        Test all available endpoints as listed in the service discovery.
        """
        print("🧪 Testing all A2A endpoints...")
        
        endpoints = {
            "root_info": "/",
            "health": "/health", 
            "status": "/status",
            "rag_agent_card": "/agent/.well-known/agent-card.json",
        }
        
        async with httpx.AsyncClient() as client:
            for name, path in endpoints.items():
                full_url = f"{self.base_url}{path}"
                print(f"\n📡 Testing {name}: {full_url}")
                
                try:
                    response = await client.get(full_url)
                    response.raise_for_status()
                    
                    data = response.json()
                    print(f"✅ {name}: Success")
                    
                    # Show key information
                    if 'name' in data:
                        print(f"   Name: {data['name']}")
                    if 'description' in data:
                        print(f"   Description: {data['description'][:100]}...")
                    if 'status' in data:
                        print(f"   Status: {data['status']}")
                    if 'service' in data:
                        print(f"   Service: {data['service']}")
                        
                except httpx.HTTPError as e:
                    print(f"❌ {name}: Failed - {e}")
                except Exception as e:
                    print(f"❌ {name}: Error - {e}")
        
        print("\n🏁 Endpoint testing completed!")
    
    async def send_message(self, query: str) -> Optional[Dict[str, Any]]:
        """
        Send a message to the agent using the standard A2A message endpoint.
        """
        print(f"Sending message: '{query}'")
        
        message_payload = {
            "id": "6b1d2b3a-e38e-4058-9f27-a0f35f0d1f83",
            "jsonrpc": "2.0",
            "method": "message/send",
            "params": {
                "configuration": {
                "acceptedOutputModes": ["text", "text/plain", "image/png"]
                },
                "message": {
                "kind": "message",
                "messageId": "6570a17c-a060-4150-b030-ad00de7c0bd2",
                "metadata": {
                    "userId": "************",
                    "appName": "jarvis-routing",
                    "sessionId": "01983b64-33c1-7a8f-a9c8-dd9a0514bb79",
                    "lang": "zh-cn",
                    "userRequestIp": '127.0.0.1',
                    "fiatCurrency": "CNY"
                    # "bizData": { "resultId": "**********" }
                },
                "parts": [{ "kind": "text", "text": query}],
                "role": "user"
                }
            }
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                response = await client.post(
                    f"{self.base_url}/message/",
                    json=message_payload,
                    headers={
                        "Content-Type": "application/json",
                        "User-Agent": f"A2AClient/{self.client_id}"
                    }
                )
                response.raise_for_status()
                
                result = response.json()
                print("Message sent successfully!")
                
                # A2A JSON-RPC responses have different structure
                if 'result' in result:
                    return result['result']
                elif 'error' in result:
                    print(f"A2A Error: {result['error']}")
                    return None
                else:
                    return result
                
            except httpx.HTTPError as e:
                print(f"Failed to send message: {e}")
                if hasattr(e, 'response') and e.response:
                    print(f"Response status: {e.response.status_code}")
                    try:
                        # Read response body safely
                        print(f"Response body: {e.response.text}")
                    except Exception:
                        print("Could not read response body")
                return None
    
    async def send_streaming_message(self, query: str) -> None:
        """
        Send a message to the agent using streaming endpoint.
        """
        print(f"Sending streaming message: '{query}'")
        
        message_id = str(uuid.uuid4())
        message_payload = {
            "id": "6b1d2b3a-e38e-4058-9f27-a0f35f0d1f83",
            "jsonrpc": "2.0",
            "method": "message/stream",
            "params": {
                "configuration": {
                "acceptedOutputModes": ["text", "text/plain", "image/png"]
                },
                "message": {
                    "kind": "message",
                    "messageId": "6570a17c-a060-4150-b030-ad00de7c0bd2",
                    "metadata": {
                        "userId": "************",
                        "appName": "jarvis-routing",
                        "sessionId": "01983b64-33c1-7a8f-a9c8-dd9a0514bb79",
                        "lang": "zh-cn",
                        "userRequestIp": '127.0.0.1',
                        "fiatCurrency": "CNY",
                        "bizData": { "resultId": "**********" }
                    },
                    "parts": [{ "kind": "text", "text": query}],
                    "role": "user"
                }
            }
        }

        async with httpx.AsyncClient(timeout=120.0) as client:
            async with client.stream(
                'POST',
                f"{self.base_url}/agent/",
                json=message_payload,
                headers={
                    "Content-Type": "application/json",
                    "Accept": "text/event-stream",
                    "User-Agent": f"A2AClient/{self.client_id}"
                }
            ) as response:
                response.raise_for_status()

                print("Streaming response:")
                async for line in response.aiter_lines():
                    if line.startswith('data: '):
                        chunk_data = line[6:]  # Remove 'data: ' prefix
                        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]}]") # 2025-08-07 22:52:37,171
                        try:
                            data = json.loads(chunk_data)
                            print(f"{data}")
                        except json.JSONDecodeError:
                            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]}] Raw text: {chunk_data}")
    
    async def health_check(self) -> bool:
        """
        Check if the agent service is healthy.
        """
        print("Checking agent health...")
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.base_url}/health")
                response.raise_for_status()
                
                health_data = response.json()
                print(f"Agent is healthy: {health_data}")
                return True
                
            except httpx.HTTPError as e:
                print(f"Health check failed: {e}")
                return False
    
    async def get_status(self) -> Optional[Dict[str, Any]]:
        """
        Get detailed status information from the agent.
        """
        print("Getting agent status...")
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.base_url}/status")
                response.raise_for_status()
                
                status_data = response.json()
                print("Status retrieved:")
                print(f"  Service: {status_data.get('service', 'Unknown')}")
                print(f"  Version: {status_data.get('version', 'Unknown')}")
                print(f"  Status: {status_data.get('status', 'Unknown')}")
                print(f"  Capabilities: {', '.join(status_data.get('capabilities', []))}")
                
                return status_data
                
            except httpx.HTTPError as e:
                print(f"Failed to get status: {e}")
                return None


async def run_example_tests():
    """
    Run a comprehensive test of the A2A RAG Agent.
    """
    print("Starting A2A Cryptocurrency RAG Agent Client Tests")
    print("=" * 60)
    
    client = A2ARagAgentClient()
    
    # Test 1: Health Check
    print("\nTest 1: Health Check")
    print("-" * 30)
    is_healthy = await client.health_check()
    if not is_healthy:
        print("Agent is not healthy. Exiting tests.")
        return
    
    # Test 2: Get Status
    print("\nTest 2: Agent Status")
    print("-" * 30)
    await client.get_status()
    
    # Test 3: Test All Endpoints
    print("\nTest 3: Endpoint Testing")
    print("-" * 30)
    await client.test_all_endpoints()
    
    # Test 4: Discover All Agents
    print("\nTest 4: Agent Discovery")
    print("-" * 30)
    agents = await client.discover_all_agents()
    if not agents:
        print("Could not discover any agents. Exiting tests.")
        return
    
    # Test 5: Send  Message
    # print("\nTest 5: RAG Capabilities Test")
    # print("-" * 30)
    # test_queries = [
    #     "Bitcoin news today",  # News analysis
    #     # "Ethereum price analysis",  # Market analysis
    #     # "What is DeFi?",  # General search
    #     # "What is DeFi?",  # General search
    #     # "Latest ETH developments"  # News analysis
    # ]
    
    # for query in test_queries:
    #     print(f"\nTesting query: '{query}'")
    #     result = await client.send_message(query)
    #     if result:
    #         print("Message processed successfully!")
    #         print(f"Response: {result}")
    #     else:
    #         print("Message failed!")
        
    #     # Wait between requests
    #     await asyncio.sleep(10)
    
    # # Test : Streaming Message
    print("\nTest 6: Streaming Message")
    print("-" * 30)
    await client.send_streaming_message("what is staking")
    
    print("\nAll tests completed!")
    print("=" * 60)



if __name__ == "__main__":
    import sys
    # Run automated tests
    asyncio.run(run_example_tests())
