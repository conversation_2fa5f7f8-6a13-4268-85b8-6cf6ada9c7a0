"""
1. 先判断resultId 是否存在，如果存在，则直接返回summary
2. 如果resultId不存在，则调用rag_root_agent
3. rag root 根据user query 对意图进行判断，然后根据意图调用不同的agent
4. 如果意图是news，则调用news_agent
5. 如果意图是market，则调用market_agent
6. 如果意图是general search或者是其他的，则调用google_search_agent? 
7. 如果rag_root_agent返回的summary为空，则返回error
8. 如果rag_root_agent返回的summary不为空，则返回summary
"""
import asyncio
import httpx
import os

from a2a.server.agent_execution import AgentExecutor, RequestContext
from a2a.server.events import EventQueue
from a2a.server.tasks import TaskUpdater
from a2a.types import (
    Part,
    TaskState,
    TextPart,
    UnsupportedOperationError
)
from a2a.utils import new_agent_text_message, new_agent_parts_message, new_task
from a2a.utils.errors import ServerError
from rag_root_agent import RagRootAgent
from util.logger.logger_utils import setup_logger
from util.logger.log_context import set_trace_id, set_session_id, set_user_id
from rag_root_agent_executor_simple import create_cardsJson_parts, create_summary_parts, create_market_analysis_parts
from util.tools.compliance import is_business_compliance, BusinessType
from rag_agent.rag_helper.helper import prepare_cardsJson
from rag_agent.rag_helper.helper import (
    get_summary_async,
    get_summary_by_query_async,
    build_parts_from_summary_data,
    get_score_from_summary_data
)
from util.tools.apollo import apollo_helper
logger = setup_logger(__name__)

try:
    SCORE_THRESHOLD = float(apollo_helper.get_apollo_values("query_summary_score_threshold"))
except ValueError as e:
    SCORE_THRESHOLD = 0.94
    logger.warning(f"Error {e} No query_summary_score_threshold found in apollo, using default value: {SCORE_THRESHOLD}")

class RagRootAgentExecutorStreaming(AgentExecutor):
    """Streaming version of RagRootAgentExecutor following ADK pattern"""
    
    def __init__(self):
        self.rag_root_agent = RagRootAgent()
    
    async def execute(self, context: RequestContext, event_queue: EventQueue) -> None:
        trace_id = context.context_id
        user_id = context.message.metadata.get("userId")
        result_id = context.message.metadata.get("bizData", {}).get("resultId", None)
        lang = context.message.metadata.get("lang", "")
        # bnc_lang for the compliance related check 
        # when buidling parts, data part will be use the bnc_lang to build the data part
        bnc_lang = context.message.metadata.get("requestHeaders", {}).get("bnc_lang", "")
        session_id = context.message.metadata.get("sessionId")
        user_query = context.get_user_input()
        user_ip = context.message.metadata.get("userRequestIp")
        logger.info(f"RagRootAgentExecutorStreaming: trace_id: {trace_id}, session_id {session_id}, metadata {context.message.metadata}")
        set_trace_id(trace_id)
        set_session_id(session_id)
        set_user_id(user_id)
        final_response = None
        accumulated_text = ""
        # Create task and updater (ADK pattern)
        task = context.current_task
        if not task:
            task = new_task(context.message)
            await event_queue.enqueue_event(task)
        
        updater = TaskUpdater(event_queue, task.id, task.context_id)
        logger.info(f" RagRootAgentExecutorStreaming: trace_id: {trace_id}, taskId {task.id}, result_id {result_id}")

        try:
            # Step 1: If resultId exists, try to fetch summary and return
            if result_id:
                output_parts = []
                try:
                    result = await get_summary_async(result_id, lang, user_id, scene="searchRagAgent")
                    logger.info(f"getAISearchRAGMeta (by resultId) taskId {task.id} resultId: {result_id}")
                except Exception as e:
                    logger.error(f"taskId {task.id} Error calling get_summary_async(resultId={result_id}): {e}")
                    result = None

                if result and isinstance(result, dict) and result.get("status") == "OK":
                    es_data = result.get("data")
                    if es_data:
                        output_parts = await build_parts_from_summary_data(es_data, user_id, bnc_lang, user_ip, lang, self.rag_root_agent.translate_agent, session_id)
                        logger.info(f"finished build parts for resultId {result_id}, session_id: {session_id}, lang: {lang}")
                    else:
                        logger.info(f"taskId {task.id} No data returned in resultId summary; falling back to normal workflow")
                else:
                    logger.warning(f"taskId {task.id} resultId summary returned non-OK/invalid response; falling back to normal workflow")

                if output_parts:
                    # first send a full trunk which is status update 
                    await updater.update_status(
                        TaskState.working,
                        new_agent_parts_message(
                            output_parts,
                            task.context_id, 
                            task.id
                        )
                    )
                    # then send a artifact 
                    await updater.add_artifact(output_parts, name='summary')
                    await updater.complete()
                    return

            # Step 2: If no resultId, try to fetch summary by query; use it if score is sufficient
            if not result_id:
                logger.info(f"No resultId provided, checking semantic cache for query: '{user_query[:50]}...'")
                output_parts = []
                query_types = ["news", "marketAnalysis", "faq"]
                try:
                    result = await get_summary_by_query_async(
                        query=user_query, 
                        lang=lang, 
                        user_id=user_id, 
                        scene="searchRagAgent",
                        query_types=query_types
                    )
                    if result.get('data') and isinstance(result, dict) and result.get("status") == "OK":
                        data = result.get("data") or {}
                        score = get_score_from_summary_data(data)
                        if score >= SCORE_THRESHOLD:
                            output_parts = await build_parts_from_summary_data(data, user_id, bnc_lang, user_ip, lang, self.rag_root_agent.translate_agent, session_id)
                            logger.info(f"semantic match found for query '{user_query}' with resultId {data.get('resultId')}, score {score}")
                            logger.info(f"prepare send summary to taskId {task.id}, sessionId {session_id}, traceId {trace_id}")
                        else:
                            logger.info(f"Query '{user_query}' with resultId {data.get('resultId')}, score {score}, but below threshold {SCORE_THRESHOLD}")
                    else:
                        logger.info(f"No semantic match found for query '{user_query}' with taskId {task.id}, sessionId {session_id}")
                except Exception as e:
                    logger.warning(f"No semantic match found for query '{user_query}' with taskId {task.id}, sessionId {session_id}: {e}")
                
                if output_parts:
                    await updater.update_status(
                        TaskState.working,
                        new_agent_parts_message(
                            output_parts,
                            task.context_id, 
                            task.id
                        )
                    )
                    await updater.add_artifact(output_parts, name='summary')
                    await updater.complete()
                    return

            # Step 3: Handle RAG agent with streaming (ADK pattern)
            logger.info(f"taskId {task.id} RagRootAgentExecutorStreaming: Processing query '{user_query[:50]}...'")
            
            # Track intermediate items to determine if we need additional taskUpdate
            intermediate_items_count = 0
            
            async for item in self.rag_root_agent.stream(
                query=user_query, 
                session_id=session_id,
                user_language=lang, 
                user_id=user_id,
                task_id=trace_id
            ):
                is_task_complete = item.get('is_task_complete', False)
                if not is_task_complete:
                    # Send intermediate updates to the task updater
                    intermediate_items_count += 1
                    chunk_text = item.get("content", "")
                    accumulated_text += chunk_text
                    await updater.update_status(
                        TaskState.working,
                        new_agent_text_message(
                            item.get('content', ''), 
                            task.context_id, 
                            task.id
                        )
                    )
                elif item.get("type") == "final_response":
                    # Send final result with artifact
                    final_response = item
                    logger.info(f"taskId {task.id} RagRootAgentExecutorStreaming: final_response: {final_response}")
                    parts = self.generate_artifact_parts(final_response, user_id, bnc_lang, user_ip)
                    specialized_agent = final_response.get("specialized_agent", "rag_root")
                    
                    # Add taskUpdate if no intermediate items were streamed (e.g., Google search fallback)
                    if intermediate_items_count == 0:
                        logger.info(f"taskId {task.id} No intermediate updates detected, adding taskUpdate before artifact")
                        await updater.update_status(
                            TaskState.working,
                            new_agent_parts_message(
                                parts,
                                task.context_id, 
                                task.id
                            )
                        )
                    else:
                        logger.info(f"taskId {task.id} {intermediate_items_count} intermediate updates detected, skipping additional taskUpdate")
                    
                    await updater.add_artifact(
                        parts, 
                        name='response',
                        metadata={
                            "agent": specialized_agent,
                            "query": user_query,
                            "session_id": session_id,
                        }
                    )
                    await updater.complete()
                    break
                elif item.get("type") == "error":
                    error_msg = f"Error: {item.get('error', 'Unknown error')}"
                    await updater.update_status(
                        TaskState.failed,
                        new_agent_text_message(error_msg, task.context_id, task.id),
                        final=True
                    )
                    return
        except Exception as e:
            import traceback
            logger.error(f"Error in taskId {task.id} RagRootAgentExecutorStreaming.execute: {e}")
            logger.error(f"Full taskId {task.id} traceback: {traceback.format_exc()}")
            
            error_message = f"Error processing RAG request: {str(e)}"
            await updater.update_status(
                TaskState.failed,
                new_agent_text_message(error_message, task.context_id, task.id),
                final=True
            )

    async def cancel(self, context: RequestContext, event_queue: EventQueue) -> None:
        raise ServerError(error=UnsupportedOperationError())
    
    def generate_artifact_parts(self, final_response: dict, user_id: str, bnc_lang: str, user_ip: str) -> list:
        body = final_response.get("body", final_response)
        specialized_agent = final_response.get("specialized_agent", "rag_root")
        if specialized_agent == "market_analysis_agent":
            token = final_response.get("token", "")
            is_compliance = is_business_compliance(BusinessType.SPOT, user_id, bnc_lang, user_ip)
            logger.info(f"RagRootAgentExecutorStreaming: is_compliance: {is_compliance}")
            return create_market_analysis_parts(body, token, is_compliance=is_compliance)
        elif specialized_agent == "campaign_analysis_agent":
            # generate the campaign data cards for the response 
            campaign_cards_data = final_response.get("campaign_cards_data", [])
            # Use markdown content for storage
            cardsJson = prepare_cardsJson(body, campaign_cards_data)
            logger.info(f"Campaign cards cardsJson: {cardsJson}")
            
            return create_cardsJson_parts(cardsJson)
        else:
            return [Part(root=TextPart(text=body))]
    