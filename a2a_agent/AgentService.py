"""
A2A News Agent Service Application
Provides a properly structured A2A-compliant news agent service.
"""

from datetime import datetime
from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from pathlib import Path
import os
from fastapi.responses import FileResponse
from a2a.server.apps import A2AStarletteApplication
from a2a.server.request_handlers import <PERSON><PERSON>ultR<PERSON><PERSON>H<PERSON><PERSON>
from a2a.server.tasks import InMemoryTaskStore
from a2a.types import Agent<PERSON>ard, AgentCapabilities, AgentSkill

from rag_root_agent_executor_simple import RagRootAgentExecutorSimple
from rag_root_agent_executor_streaming import RagRootAgentExecutorStreaming
from util.logger.middleware import TraceIdMiddleware
from dotenv import load_dotenv

load_dotenv()

class AgentServiceApp:
    """
    A2A News Agent Service Application
    
    Configures and provides a cryptocurrency news analysis agent
    using the A2A (Agent-to-Agent) protocol framework.
    """
    
    def __init__(self, lifespan=None):
        self.app = FastAPI(
            title="Cryptocurrency News Agent",
            description="A2A-compliant agent for cryptocurrency news analysis",
            version="1.0.0"
        )
        self.STATIC_DIR = Path("static")
        self._configure_routes()

    def _configure_routes(self):
        """Configure the A2A agent routes and services."""
        
        # Define the RAG root agent's skills (comprehensive capabilities)
        rag_skills = [
            AgentSkill(
                id="crypto_news_analysis",
                name="Cryptocurrency News Analysis",
                description="Retrieve and analyze cryptocurrency news with market impact insights",
                tags=["cryptocurrency", "news", "analysis", "updates", "hotspots", "latest developments"],
                examples=[
                    "BTC news today",
                    "ETH updates", 
                    "XRP latest news",
                    "Solana hotspots",
                ]
            ),
            AgentSkill(
                id="market_analysis",
                name="Market Analysis & Technical Analysis",
                description="Comprehensive market analysis including marco trend, coin level market trends, technical indicators, and analysis",
                tags=["market analysis", "technical analysis", "analysis", "coin level trends", "index", "netflow", "marco", "fear index", "trading volume"],
                examples=[
                    "ETH market analysis",
                    "BTC trend analysis",
                    "ETH technical indicators",
                    "Market trends analysis",
                    "Coin level trend analysis",
                    "BTC latest capital flow",
                    "marco market index",
                    "btc trading volume"
                ]
            ),
            AgentSkill(
                id="campaign_analysis",
                name="Campaign & Promotion Analysis",
                description="Analyze Binance campaigns new listing, tge, lauchpool, airdrops, earn, growth campaign, mission, trading competitions,  opportunities, and promotional activities",
                tags=["new listing", 'tge', 'launchpool', "campaign", "competition", "airdrop", "earn", "staking", "promotion", "bonus", "rewards", "contest", "trading competition", "growth", "mission"],
                examples=[
                    "latest new listing",
                    "latest tge",
                    "latest launchpool",
                    "Bitcoin trading competition",
                    "latest airdrops",
                    "Binance earn campaigns",
                    "staking rewards",
                    "new listing promotion",
                    "trading contest rewards",
                    "earn opportunities"
                ]
            ),
            AgentSkill(
                id="general_crypto_search",
                name="General Cryptocurrency Information",
                description="General cryptocurrency and Binance information, FAQ, explanations, and educational content",
                tags=["cryptocurrency", "defi", "blockchain", "search", "faq", "how-to", "binance how-to", "custom service", "board questions"],
                examples=[
                    "What is DeFi?",
                    "What is staking",
                    "How does Bitcoin work?",
                    "Ethereum smart contracts",
                    "Crypto regulatory questions"
                ]
            )
        ]

        env = os.getenv("ENV", "qa").lower()
        if env == "qa":
            url = "http://bdp-search-agent.eureka.qa.local:13122/a2a/agent/"
        elif env == 'prod':
            url = "http://bdp-search-agent.eureka.local:13122/a2a/agent/"
        else:
            url = "http://bdp-search-agent.eureka.local:13122/a2a/agent/"
        
        # Define the agent card for RAG root agent
        rag_root_card = AgentCard(
            name="searchRagAgent",
            description="Comprehensive cryptocurrency information agent with news, market analysis, campaign analysis capabilities", # and general crypto information & FAQ search
            url=url,
            version="1.0.0",
            defaultInputModes=["text"],
            defaultOutputModes=["text", "application/json"],
            capabilities=AgentCapabilities(
                streaming=True, 
            ),
            skills=rag_skills,
        )

        # Create streaming executor and handler
        streaming_executor = RagRootAgentExecutorStreaming()
        streaming_task_store = InMemoryTaskStore()
        streaming_handler = DefaultRequestHandler(streaming_executor, streaming_task_store)
        
        # Create streaming A2A application
        streaming_app = A2AStarletteApplication(
            agent_card=rag_root_card,
            http_handler=streaming_handler
        )
        # Mount the RAG root agent at the agent path
        self.app.mount("/agent", streaming_app.build())
        
        # old pipeline to emit 1 time message only
        # Create the request handler with RAG root executor
        rag_root_executor = RagRootAgentExecutorSimple()
        task_store = InMemoryTaskStore()
        rag_root_handler = DefaultRequestHandler(rag_root_executor, task_store)
        
        # Create the A2A application with RAG root agent
        rag_root_app = A2AStarletteApplication(
            agent_card=rag_root_card, 
            http_handler=rag_root_handler
        )
        self.app.mount("/message", rag_root_app.build())


        # Mount the convert agent
        from agent_convert.convert_work_flow.a2a_convert_agent_service import ConvertAgentServiceApp
        convert_service = ConvertAgentServiceApp()
        self.app.mount("/convert", convert_service.get_app())

        # # static files 
        # self.app.mount("/static", StaticFiles(directory=self.STATIC_DIR), name="static")

        # Add middleware
        self.app.add_middleware(TraceIdMiddleware)

        # Add basic health and info routes
        @self.app.get("/")
        async def root():
            """Root endpoint with service information."""
            return {
                "service": "Cryptocurrency RAG Agent",
                "version": "1.0.0",
                "description": "A2A-compliant cryptocurrency RAG agent with news, market analysis, campaign analysis, and search capabilities",
                "capabilities": ["news_analysis", "market_analysis", "campaign_analysis", "general_search"],
                "endpoints": {
                    "rag_root_agent_card": "/agent/.well-known/agent-card.json",
                    "rag_root_agent_stream": "/agent/",
                    "simple_message": "/message",
                    "health": "/health",
                    "status": "/status"
                },
                "framework": "A2A Protocol",
                "timestamp": datetime.now().isoformat()
            }

        @self.app.get("/health")
        async def health_check():
            """Health check endpoint."""
            return {
                "status": "healthy",
                "service": "rag-agent",
                "timestamp": datetime.now().isoformat()
            }

        @self.app.get("/status")
        async def status_check():
            """Detailed status endpoint."""
            return {
                "status": "ready",
                "service": "Cryptocurrency RAG Agent",
                "version": "1.0.0",
                "capabilities": [skill.id for skill in rag_skills],
                "specialized_agents": ["news", "market", "campaign", "search"],
                "available_endpoints": ["/agent", "/stream"],
                "uptime": "running",
                "timestamp": datetime.now().isoformat()
            }

    def get_app(self):
        """Get the configured FastAPI application."""
        return self.app